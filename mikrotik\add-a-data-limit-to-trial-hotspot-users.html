<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Add a Data Limit To Trial Hotspot Users - MikroTik Script RouterOS</title>
<meta content='Add a Data Limit To Trial Hotspot Users - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Add a Data Limit To Trial Hotspot Users - MikroTik Script RouterOS</h1>
<pre>The following script allows you to assign a data limit to trial hotspot users and

a) have them kicked offline upon reaching this limit
b) create a temporary local user to stop them from logging back in again
This is a feature which is not available via the current hotspot settings, so I decided to script it into existence. Enjoy!

Script to check users for those over the limit:
Make a scheduler entry to run this every X minutes, I'd recommend every 5-10 minutes.

<code class="routeros">#Download limit in MB
:local downquotamb "50"
# Do not modify anything below this line #
:local downquota [$downquotamb * 1000]
:local counter
:local datadown
:local username
:local macaddress
:foreach counter in=[/ip hotspot active find ] do={
:set datadown [/ip hotspot active get $counter bytes-out]
:if ($datadown>$downquota) do={
:set username [/ip hotspot active get $counter user]
:set macaddress [/ip hotspot active get $counter mac-address]
/ip hotspot user remove [/ip hotspot user find where name=$username]
/ip hotspot user add name=$username limit-bytes-out=$downquota mac-address=$macaddress
/ip hotspot active remove $counter
:log info "Logged out $username - Reached download quota"
}}</code>
Script to clear all manually created user accounts; this can be done hourly, daily or weekly depending on your requirements.

<code class="routeros">:foreach counter in=[/ip hotspot user find ] do={/ip hotspot user remove \$counter}</code>
Notes: 
You can modify how often you want each of the script

Credit: https://wiki.mikrotik.com/wiki/Add_a_data_limit_to_trial_hotspot_users
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
