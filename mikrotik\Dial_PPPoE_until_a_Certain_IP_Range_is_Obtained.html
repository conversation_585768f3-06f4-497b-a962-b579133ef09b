<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Dial PPPoE until a Certain IP Range is Obtained - Mikrotik Script RouterOS</title>
<meta content='Dial PPPoE until a Certain IP Range is Obtained - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>

<h1>Dial PPPoE until a Certain IP Range is Obtained - Mikrotik Script RouterOS</h1><pre>
Dial PPPoE until a Certain IP Range is Obtained
This script is for those who wants a certain IP range when connecting with PPPoE to your ISP. Instead of constantly disabling and enabling the pppoe client, all you have to do is run this script.

You may want a certain IP range for any number of reasons. Sometimes I use this to check the routes that my ISP uses. The routes they use differs for certain IP ranges. Hope someone finds this useful.
Forum thread for discussion
3 variables to set, if, targetip and pppoedelay. if: name of your pppoe-client interface targetip: eg: *********/8 or *********/16 pppoedelay: Enough delay so that your ISP gives a new IP instead of the previous one. You can play around with this.

<code class="routeros">:global if "pppoe-out1"
:global targetip 210.0.0.0/8
:global currentip [/ip address get [/ip address find interface=$if ] address]
:global ip $currentip
:global pppoedelay 10s

:put "Current IP: $currentip\rTarget: $targetip\r"

:while (!($currentip in $targetip)) do={
     put "$currentip is not in $targetip. Getting new IP. $pppoedelay delay"
     /interface disable $if
     :delay $pppoedelay
     /interface enable $if
     :do {delay 1s; put "waiting for pppoe-server"} while=( [/interface get unifi running]=false)
     :set currentip [/ip address get [/ip address find interface=$if ] address]
     :set currentip ([[:parse ":return $currentip"]])
     :set ip $currentip
     }
:put "Target obtained: $currentip"</code>

Best to run from the CLI so you can see the output. Example:

[admin@RB2011UAS] /system script> run pppoe-ip-cidr 
Current IP: ***************/32
Target: 210.0.0.0/8

***************/32 is not in 210.0.0.0/8. Getting new IP. 00:00:10 delay
waiting for pppoe-server
waiting for pppoe-server
***************/32 is not in 210.0.0.0/8. Getting new IP. 00:00:10 delay
waiting for pppoe-server
waiting for pppoe-server
*************/32 is not in 210.0.0.0/8. Getting new IP. 00:00:10 delay
waiting for pppoe-server
waiting for pppoe-server
*************/32 is not in 210.0.0.0/8. Getting new IP. 00:00:10 delay
waiting for pppoe-server
waiting for pppoe-server
***************/32 is not in 210.0.0.0/8. Getting new IP. 00:00:10 delay
waiting for pppoe-server
waiting for pppoe-server
***************/32 is not in 210.0.0.0/8. Getting new IP. 00:00:10 delay
waiting for pppoe-server
waiting for pppoe-server
**************/32 is not in 210.0.0.0/8. Getting new IP. 00:00:10 delay
waiting for pppoe-server
waiting for pppoe-server
Target obtained: ***************/32
[admin@RB2011UAS] /system script> 

Credit: https://wiki.mikrotik.com/wiki/Dial_PPPoE_until_a_Certain_IP_Range_is_Obtained
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
