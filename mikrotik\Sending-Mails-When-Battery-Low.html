<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Sending Mails When Battery Low (UPS Script) - MikroTik Script RouterOS</title>
<meta content='Sending Mails When Battery Low (UPS Script) - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Sending Mails When Battery Low (UPS Script) - MikroTik Script RouterOS</h1>
<pre>How to Sending Mails When Battery Low (UPS Script)
version for ROS 3.x
these scripts send emails on power-blackout and when the battery voltage is low,
works ony when UPS-Package is installed, and only with APC ups or our ups-mudule

more details: http://www.stone-rich.at/dl/str-ups/default.htm
start these scripts about every minute

<code class="routeros">/system scheduler
add comment="" disabled=no interval=1m name="ups-powermonitor" on-event=ups-powermonitor \
    start-date=jan/01/1970 start-time=00:00:00</code>
add this scripts as "ups-powermonitor" check the correct version for your device!

<code class="routeros"># UPS-Script powerfail
# (c) steinmann und weidinger OEG
# www.stone-rich.at
#
# Watches ups status and sends emails on power failure and low battery.
# This script will FAIL if:
# - Policies write, test, and read are not set
# - The system name contains non-standard characters (space, /, ...)
# - The UPS is not named ups1 (fixed by adding configurable variable)
#
# This script was tested up to ROS 3.23
# user-configurable parameters below:
:local mailserver [:resolve mailserver];
:local mailfrom "<EMAIL>";
:local mailto "<EMAIL>";
:local upsName "ups1";
#
# do NOT make changes below!
#
:global flagonbatt;
:global flagbattlow;
:local battalarm 15;
:local battok 40;
:local curonbatt;
:local curcharge;
:local sysname [/system identity get name];
:local datetime "$[/system clock get date] $[/system clock get time]";
# First run? If so, we need to initialize the global flags
:if ([:typeof $flagonbatt]="nothing") do={:set flagonbatt 0}
:if ([:typeof $flagbattlow]="nothing") do={:set flagbattlow 0}
:set curonbatt false;
:set curcharge 100;
/system ups monitor [/system ups find name=$upsName] once do={
  :set curonbatt $"on-battery"; :set curcharge $"battery-charge";
}
:if (($curonbatt) && ($flagonbatt=0)) do={
  :set flagonbatt 1;
 /tool e-mail send from=$mailfrom  to=$mailto server=$mailserver subject="$sysname: Power failure!" \
    body="$sysname  is on battery since $datetime";
  :log info "Power-Fail: EMail sent to $mailto";
}
:if ((!$curonbatt) && ($flagonbatt=1)) do={
 :set flagonbatt 0;
 /tool e-mail send from=$mailfrom  to=$mailto server=$mailserver subject="$sysname: Power is back" \
    body="$sysname is back on power since $datetime";
  :log info "Power-Restore: Email sent to $mailto";
}
:if (($curcharge <= $battalarm) && ($flagbattlow=0)) do={
  :set flagbattlow 1;
  /tool e-mail send from=$mailfrom  to=$mailto server=$mailserver subject="$sysname: Low battery!" \
    body="$sysname battery is at $curcharge %! $datetime";
  :log info "Batt-Low: Email sent to $mailto";
}
:if (($curcharge >= $battok) && ($flagbattlow=1)) do={
  :set flagbattlow 0;
  /tool e-mail send from=$mailfrom  to=$mailto server=$mailserver subject="$sysname: Battery recharged" \
    body="$sysname Battery recharged to $curcharge% $datetime";
  :log info "Batt-Recharged: Email sent to $mailto";
}</code>

version for ROS 2.9x:

<code class="routeros"># UPS-Script powerfail
# (c) steinmann und weidinger OEG
# www.stone-rich.at
#
# Watches ups status and sends emails on powerfail and battery low.
# This script will FAIL if:
# - Policies write, test, and read are not set
# - The system name contains non-standard characters (space, /, ...)
# - The UPS is not named ups1
#
# This script will not run on 3.x !!
:set mailserver [:resolve mailserver]
:set mailto "<EMAIL>"
:set mailfrom "<EMAIL>"
:set battalarm 15
:set battok 40
:set sysname [/system identity get name]
:set datetime ([/system clock get date] . " " . [/system clock get time])
# First run? If so, we need to initialize the global flags
:if (("-" . $flagonbatt)="-") do {:global flagonbatt 0}
:if (("-" . $flagbattlow)="-") do {:global flagbattlow 0}
:set curonbatt false
:set curcharge 100
/system ups monitor [/system ups find name "ups1"] once do {:set curonbatt $on-battery; :set curcharge $battery-charge}
:if (($curonbatt) && ($flagonbatt=0)) do {
  :global flagonbatt 1
  /tool e-mail send from=($mailfrom)  to=($mailto) server=($mailserver) subject=($sysname . ": Power failure!") body=($sysname . " is on battery since " . $datetime)
  :log info ("Power-Fail: EMail sent to " . $mailto)
}
:if ((!$curonbatt) && ($flagonbatt=1)) do {
 :global flagonbatt 0
  /tool e-mail send from=($mailfrom)  to=($mailto) server=($mailserver) subject=($sysname . ": Power is back") body=($sysname . " is back on power since " . $datetime)
  :log info ("Power-Restore: Email sent to " . $mailto)
}
:if (($curcharge <= $battalarm) && ($flagbattlow=0)) do {
  :global flagbattlow 1
  /tool e-mail send from=($mailfrom)  to=($mailto) server=($mailserver) subject=($sysname . ": Low battery!") body=($sysname . " battery is at " . $curcharge . "%! " . $datetime)
  :log info ("Batt-Low: Email sent to " . $mailto)
}
:if (($curcharge >= $battok) && ($flagbattlow=1)) do {
  :global flagbattlow 0
  /tool e-mail send from=($mailfrom)  to=($mailto) server=($mailserver) subject=($sysname . ": Battery recharged") body=($sysname . " Battery recharged to " . $curcharge . "% " . $datetime)
  :log info ("Batt-Recharged: Email sent to " . $mailto)
}</code>
Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

