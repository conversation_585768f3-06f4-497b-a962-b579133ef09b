  
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Remote IP Public Static For Secondary Gateway - Mikrotik Script RouterOS</title>
<meta content='Remote IP Public Static For Secondary Gateway - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Remote IP Public Static For Secondary Gateway - Mikrotik Script RouterOS</h1>
<pre>So that the Public Static IP can be used for remote even though not as the main gateway

An easy example like this ... suppose we use Indihome as the main gateway and Astinet as a backup gateway, in the standard routing concept, of course, Public IP Astinet cannot be used for remote because its position is only as a backup gateway, so that Astinet Public IP can still be used for example. Winbox remote needs are very easy, and this method can also be applied to all ISP that have Public Static IP

Or you can use <a target="_blank" href="https://mikrotiktool.github.io/remote-ip-public-static.html" >Script Maker or Generator</a>

<code class="routeros">####################################################################
# Remote IP Public Static For Secondary Gateway Mikrotik
# Date/Time: 2/14/2021, 1:40:07 PM
#  Secure!  
####################################################################

/ip firewall mangle
add action=mark-connection chain=input in-interface="ether2" new-connection-mark=ip-public passthrough=yes
add action=mark-routing chain=output connection-mark=ip-public new-routing-mark=ip-public passthrough=no

/ip route
add check-gateway=ping gateway="************" routing-mark=ip-public</code>
Credit: https://www.o-om.com/2020/12/remote-ip-public-static-for-secondary.html
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
