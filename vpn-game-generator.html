<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Vpn Game Script Generator For Mikrotik Routeros (Port Games) - mikrotiktool.Github.io</title>
<meta content='Vpn Game Script Generator For Mikrotik Routeros (Port Games) - mikrotiktool.Github.io' name='description'/>
<meta content='script generator, mikrotik, router, winbox, termimal, rsc, script, hotspot, wireless, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon">
<meta property="og:title" content="Vpn Game Script Generator For Mikrotik (Port Game) ">
<meta property="og:description" content="Vpn Game Script Generator For Mikrotik (Port Game) ">
<meta property="og:image:alt" content="Vpn Game Script Generator For Mikrotik (Port Game) ">
<meta property="og:type" content="website">
<meta property="og:url" content="https://mikrotiktool.github.io/vpn-game-generator.html"> 
<script data-ad-client="ca-pub-3815059393374366" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M5TL99Q9M2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-M5TL99Q9M2');
</script>
<style>
*{-webkit-transition: all .3s ease-in;-moz-transition: all .3s ease-in;-ms-transition: all .3s ease-in;transition: all .3s ease-in;}
body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table{margin:0; padding:0}
body { font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; margin:20px;}
html, body {
height: 100%;
width:100%;
margin-top: 0px;
padding:0;
font-size:16px;
}
* {
box-sizing: border-box;
}
.Menu{
width:100%;
border-bottom:1px solid #ccc;
height:100%;
padding-bottom: 15px;
margin-bottom: 20px;
font-size:14px;
font-weight:bold;
 }
.logo {
margin-top:0px;
color: #111;
text-decoration:none;
font-size:27px;
font-weight:bold; 
}	
.logo a{
color:#111 !important;
text-decoration:none !important;
}
#wrap{
width:1120px;
margin:0 auto;
padding:10px;
background:#fff
}
.header{
height:auto; 
width:100%;
margin-top:0px;
margin-bottom:10px;
border-top:1px solid #ccc;
}
.main-wrap{
display:flex;
}     
.content{
flex:1;
float:right;
width:800px;
padding: 10px;
border-top:1px solid #ccc;
border-bottom:1px solid #ccc;
border-right:1px solid #ccc;
background:#eee;
height: auto;
}
.sidebar{
float:left;
width:300px;
Padding-top:8px;
padding-left: 10px;
Padding-right:10px;
Padding-bottom:20px;
background-color:#eee;
border:1px solid #ccc; 
height: auto;
}
.footer{
padding-top:15px;
clear:both;
width:auto;
font-size:14px;
}
h1 {
color:#111;
font-size:22px;
margin-bottom:5px;
margin-top:10px;
}
a:link {
  color: #4CAF50;
}
a:visited {
  color: #4CAF50;
}
a:hover {
  color: #4CAF50;
}
a:active {
  color: #4CAF50;
}
.menu {
margin-bottom:13px;
clear:both;
width:auto;
}
.menu1 {
width:300px;
margin-bottom:1px;
font-weight:bold;
background-color:#4CAF50;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu1 a:link {
text-decoration:none;
color:white;
}
.menu1 a:visited{
text-decoration:none;
color:white;
}
.menu2 {
width:360px;
margin-bottom:1px;
font-weight:bold;
background-color:#ff6600;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu2 a:link{
text-decoration:none;
color:white;
}
.menu2 a:visited{
text-decoration:none;
color:white;
}
 button {
 color: #fff;
 background-color: #4CAF50;
 border-color: #4CAF50;
 border:none;
 padding:8px;
 width:135px;
 font-size:16px;
 font-weight:bold;
 }
 .row:after {
 content: "";
 display: table;
 clear: both;
 }  
 input[type=checkbox]{
 margin-right:7px; 
}
 input[type=text], select, textarea {
 width: 100%;
 padding: 5px;
 border: 1px solid #ccc;
 border-radius: 1px;
 resize: vertical;
 font-size:16px !important;
 }
 label {
 padding: 5px 5px 5px 5px;
 display: inline-block;
 }
 input[type=submit] {
 background-color: #4CAF50;
 color: white;
 padding: 13px 20px;
 border: none;
 border-radius: 1px;
 cursor: pointer;
 float: right;
 }
 input[type=submit]:hover {
 background-color: #45a049;
 }
 .col-25 {
 float: left;
 width: 25%;
 margin-top: 6px;
 }
 .col-75 {
 float: left;
 width: 75%;
 margin-top: 6px;
 }
 /* Clear floats after the columns */
 .row:after {
 content: "";
 display: table;
 clear: both;
 }
 .list-game {
 height: 200px;
 background:white;
 overflow: scroll;
 overflow-x: hidden;
 width: 100%;
 margin-top:2px;
 padding: 3px;
 border: 1px solid rgba(0,0,0,0.25);
 }
 .list-game label {
 padding: 0;
 display: inline-block;
 } 
 .list-mangle {
 height: 625px;
 background:#fff;
 overflow: scroll;
 width: 100%;
 padding: 4px;
 margin-top:8px;
 border: 1px solid #ccc;
 }
 table, tr, td {
border: none;
}
/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}
/* Modal Content */
.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
}

/* The Close Button */
.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
} 
</style>
<script src="https://platform-api.sharethis.com/js/sharethis.js#property=5fa97f556a73380012578d69&product=video-share-buttons" async="async"></script>
</head>
<body onLoad="callmodal()"> 
<div id="wrap">	
<div class="logo">	  
<a href="https://mikrotiktool.github.io"><span style="color:#4CAF50 !important">mikrotiktool</span>.Github.io</a> <img style="float:right; padding-top:10px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=hits&edge_flat=false" alt="Hits"/> 
</div>	
<div class="header">
<span style="float:left; display:inline;margin-right:6px; margin-top:10px">
<a href="https://mikrotiktool.github.io/vpn-game-generator.html"><img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="HOME"></a>
</span>
<h1>STATIC ROUTING VPN GAME SCRIPT GENERATOR <span style="color:#4CAF50">{ PORT GAME ROUTING METHOD }</span></h1> 
</div>
<div class="main-wrap">
    <div class="sidebar">
         <label>Select VPN Connection</label><div style="clear: both;"></div><div style="clear: both;"></div>
         <select onchange="myFunction()" id="vpn-list">
            <option value="pptp">PPTP - Point to Point Tunneling Protocol</option>
            <option value="l2tp">L2TP - Layer Two Tunneling Protocol</option>
         </select>
         <div style="clear: both;"></div>
         <label>Create VPN Name on Interface</label><div style="clear: both;"></div>
         <input type="text" id="vpn-name" value="VPN-GAME" placeholder="VPN-GAME"><div style="clear: both;"></div>
         <label>VPN IP Address</label><div style="clear: both;"></div>
         <input type="text" id="vpn-ip" placeholder="example:**************"><div style="clear: both;"></div>
         <label>VPN Username</label><div style="clear: both;"></div>
         <input type="text" id="vpn-username" placeholder="username"><div style="clear: both;"></div>
         <label>VPN Password</label><div style="clear: both;"></div>
         <input type="text" id="vpn-password" placeholder="password">
         <div style="margin-top:10px;margin-bottom:10px">
	 <input onchange="myFunction()" type="checkbox" id="ip-gateway-check"><label for="ip-gateway-check" style="padding-left:0px">IP Gateway ISP Game (optional)</label>
         <input disabled type="text" id="ip-gateway-input" placeholder="example:***********"><div style="clear: both;"></div>
	 </div>
         <label>Select Mangle Games</label> <input type="text" id="myInput" onkeyup="myFunctionGames()" placeholder="find Game.."> 
		 <div class="list-game">
		 <table id="myTable">
			<!-- MOBILE GAMES -->
 			<tr><td><label style="font-weight:bold;color:green;padding-left:5px">MOBILE GAMES</label></td></tr>
            <tr><td><input type="checkbox" id="ArenaOfValor-Mobile"><label for="ArenaOfValor-Mobile">Arena Of Valor - Mobile</label></td></tr>
			<tr><td><input type="checkbox" id="BooyaCapsaSusun-Mobile"><label for="BooyaCapsaSusun-Mobile">Booya Capsa Susun - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="BooyaDominoQiuqiu-Mobile"><label for="BooyaDominoQiuqiu-Mobile">Booya Domino Qiuqiu - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="ClashOfClans-Mobile"><label for="ClashOfClans-Mobile">Clash Of Clans - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="ClashRoyale-Mobile"><label for="ClashRoyale-Mobile">Clash Royale - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="CODCallOfDuty-Mobile"><label for="CODCallOfDuty-Mobile">Call of Duty (COD) - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="DominoQq-Mobile"><label for="DominoQq-Mobile">Domino Qq - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="DragonNest-Mobile"><label for="DragonNest-Mobile">DragonNest - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="DreamLeagueSoccer-Mobile"><label for="DreamLeagueSoccer-Mobile">Dream League Soccer - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="FreeFire-Mobile"><label for="FreeFire-Mobile">FreeFire (FF) - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="LastEmpireWarZ-Mobile"><label for="LastEmpireWarZ-Mobile">Last Empire War Z - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="LineLetsGetRich-Mobile"><label for="LineLetsGetRich-Mobile">Line Lets Get Rich - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="MobileLegends-Mobile"><label for="MobileLegends-Mobile">Mobile Legends (ML) - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="Mostly-Mobile"><label for="Mostly-Mobile">Mostly - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="NarutoLittleNinja-Mobile"><label for="NarutoLittleNinja-Mobile">Naruto Little Ninja - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="Netmarble-Mobile"><label for="Netmarble-Mobile">Netmarble - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="PointBlank-Mobile"><label for="PointBlank-Mobile">PointBlank (PB) - Mobile </label></td></tr>
            <tr><td><input type="checkbox" id="PUBG-Mobile"><label for="PUBG-Mobile">PUBG - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="RpgToramOnline-Mobile"><label for="RpgToramOnline-Mobile">Rpg Toram Online - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="RulesOfSurvival-Mobile"><label for="RulesOfSurvival-Mobile">RulesOfSurvival (ROS) - Mobile</label></td></tr>
            <tr><td><input type="checkbox" id="ShinobiHeroes-Mobile"><label for="ShinobiHeroes-Mobile">Shinobi Heroes - Mobile</label></td></tr>
			<!-- WEB GAMES -->   
			<tr><td><label style="font-weight:bold;color:green;padding-left:5px">WEB GAMES</label></td></tr>
			<tr><td><input type="checkbox" id="BallPool-WebGames"><label for="BallPool-WebGames">8 Ball Pool - WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="CastleVille-WebGames"><label for="CastleVille-WebGames">Castle Ville - WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="Headshot-WebGames"><label for="Headshot-WebGames">Headshot - WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="EmpireAllies-WebGames"><label for="EmpireAllies-WebGames">Empire Allies - WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="LeagueofAngels-WebGames"><label for="LeagueofAngels-WebGames">League of Angels 2 - WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="MegarealmRiseOfChaos-WebGames"><label for="MegarealmRiseOfChaos-WebGames">Megarealm RiseOfChaos-WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="PerjuanganSemut-WebGames"><label for="PerjuanganSemut-WebGames">Perjuangan Semut - WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="Roblox-WebGames"><label for="Roblox-WebGames">Roblox - WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="SwordofAngels-WebGames"><label for="SwordofAngels-WebGames">Sword of Angels - WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="TexasHoldEmPoker-WebGames"><label for="TexasHoldEmPoker-WebGames">Texas Hold EmPoker - WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="Warflare-WebGames"><label for="Warflare-WebGames">Warflare - WebGames</label></td></tr>
			<tr><td><input type="checkbox" id="WildOnes-WebGames"><label for="WildOnes-WebGames">WildOnes - WebGames</label></td></tr>
			<!-- DEKSTOP / PC GAMES -->    
		    <tr><td><label style="font-weight:bold;color:green;padding-left:5px">DEKSTOP / PC GAMES</label></td></tr>
			<tr><td><input type="checkbox" id="Atlantica-PC"><label for="Atlantica-PC">Atlantica - PC</label></td></tr>
			<tr><td><input type="checkbox" id="AuraKingdom-PC"><label for="AuraKingdom-PC">Aura Kingdom - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Ayodance-PC"><label for="Ayodance-PC">Ayodance - PC</label></td></tr>
			<tr><td><input type="checkbox" id="OriginAPEXLegends-PC"><label for="OriginAPEXLegends-PC">APEX Legends - PC</label></td></tr>
			<tr><td><input type="checkbox" id="BlackDesertOnline-PC"><label for="BlackDesertOnline-PC">BlackDesert (BDO) - PC</label></td></tr>
			<tr><td><input type="checkbox" id="BlackDesertOnlinePS-PC"><label for="BlackDesertOnlinePS-PC">BlackDesert (BDO) - Playstaion</label></td></tr>
			<tr><td><input type="checkbox" id="BlackDesertOnlineSteam-PC"><label for="BlackDesertOnlineSteam-PC">BlackDesert Steam (BDO) - PC</label></td></tr>
			<tr><td><input type="checkbox" id="BlackDesertOnlineXbox-PC"><label for="BlackDesertOnlineXbox-PC">Black Desert (BDO) - Xbox</label></td></tr>
			<tr><td><input type="checkbox" id="Blackretribution-PC"><label for="Blackretribution-PC">Blackretribution - PC</label></td></tr>
			<tr><td><input type="checkbox" id="BlizzardBattleNet-PC"><label for="BlizzardBattleNet-PC">Blizzard BattleNet - PC</label></td></tr>
			<tr><td><input type="checkbox" id="BountyHound-PC"><label for="BountyHound-PC">Bounty Hound - PC</label></td></tr>
			<tr><td><input type="checkbox" id="CabalExtremePrivate-PC"><label for="CabalExtremePrivate-PC">Cabal Extreme - PC</label></td></tr>
			<tr><td><input type="checkbox" id="CabalIndonesia-PC"><label for="CabalIndonesia-PC">Cabal Indonesia - PC</label></td></tr>
			<tr><td><input type="checkbox" id="CallofDutyBO-PC"><label for="CallofDutyBO-PC">CallofDuty(COD)BO - PC</label></td></tr>
			<tr><td><input type="checkbox" id="CallofDutyBOPS-PC"><label for="CallofDutyBOPS-PC">CallofDuty(COD)BO - PlaySt..</label></td></tr>
			<tr><td><input type="checkbox" id="CallofDutyBOXBox-PC"><label for="CallofDutyBOXBox-PC">CallofDuty(COD)BO - XBox</label></td></tr>
			<tr><td><input type="checkbox" id="CallofDutyMWPC-PC"><label for="CallofDutyMWPC-PC">CallofDuty(COD)MW - PC</label></td></tr>
			<tr><td><input type="checkbox" id="CallofDutyMWPS-PC"><label for="CallofDutyMWPS-PC">CallofDuty COD)MW - Playst..</label></td></tr>
			<tr><td><input type="checkbox" id="CallofDutyMWXBox-PC"><label for="CallofDutyMWXBox-PC">CallofDuty (COD)MW - XBox </label></td></tr>
			<tr><td><input type="checkbox" id="ClashofGod-PC"><label for="ClashofGod-PC">Clash of God - PC</label></td></tr>
			<tr><td><input type="checkbox" id="CSGOPlaystation-PC"><label for="CSGOPlaystation-PC">CSGO Playstation</label></td></tr>
			<tr><td><input type="checkbox" id="CSGOSteam-PC"><label for="CSGOSteam-PC">CSGO Steam - PC</label></td></tr>
			<tr><td><input type="checkbox" id="CSGOXbox-PC"><label for="CSGOXbox-PC">CSGO Xbox - PC</label></td></tr>
			<tr><td><input type="checkbox" id="CrossFireindonesia-PC"><label for="CrossFireindonesia-PC">Cross Fire indonesia - PC</label></td></tr>
			<tr><td><input type="checkbox" id="DiabloIII-PC"><label for="DiabloIII-PC">Diablo III - PC</label></td></tr>
			<tr><td><input type="checkbox" id="DiabloII-PC"><label for="DiabloII-PC">Diablo II - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Diablo-PC"><label for="Diablo-PC">Diablo I - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Dota2Steam-PC"><label for="Dota2Steam-PC">DOTA 2 Steam - PC</label></td></tr>
			<tr><td><input type="checkbox" id="DragonNest-PC"><label for="DragonNest-PC">DragonNest - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Dragona-PC"><label for="Dragona-PC">Dragona - PC</label></td></tr>
			<tr><td><input type="checkbox" id="FifaOnline3-PC"><label for="FifaOnline3-PC">Fifa Online 3 (FO3) - PC</label></td></tr>
			<tr><td><input type="checkbox" id="FORTNITEEPICGAMES-PC"><label for="FORTNITEEPICGAMES-PC">Fortnite Epic - PC</label></td></tr>
			<tr><td><input type="checkbox" id="LeagueofLegend-PC"><label for="LeagueofLegend-PC">League of Legend (LOL) - PC</label></td></tr>
			<tr><td><input type="checkbox" id="GrandChase-PC"><label for="GrandChase-PC">Grand Chase - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Hearthstone-PC"><label for="Hearthstone-PC">Hearthstone - PC</label></td></tr>
			<tr><td><input type="checkbox" id="HeroesoftheStorm-PC"><label for="HeroesoftheStorm-PC">Heroes of the Storm - PC</label></td></tr>
			<tr><td><input type="checkbox" id="HONHeroesofNewerth-PC"><label for="HONHeroesofNewerth-PC">Heroes of Newerth - PC</label></td></tr>
			<tr><td><input type="checkbox" id="HeroesofAtarsia-PC"><label for="HeroesofAtarsia-PC">Heroes of Atarsia - PC</label></td></tr>
			<tr><td><input type="checkbox" id="IdolStreet-PC"><label for="IdolStreet-PC">Idol Street - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Left4Dead2Steam-PC"><label for="Left4Dead2Steam-PC">Left4Dead 2 Steam - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Lineage2-PC"><label for="Lineage2-PC">Lineage 2 - PC</label></td></tr>
			<tr><td><input type="checkbox" id="LostSaga-PC"><label for="LostSaga-PC">LostSaga (LS) - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Luneofeden-PC"><label for="Luneofeden-PC">Lune of eden - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Mercenaryops-PC"><label for="Mercenaryops-PC">Mercenary ops - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Mircovolt-PC"><label for="Mircovolt-PC">Mircovolt - PC</label></td></tr>
			<tr><td><input type="checkbox" id="ModoMarble-PC"><label for="ModoMarble-PC">Modo Marble - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Overwatch-PC"><label for="Overwatch-PC">Overwatch - PC</label></td></tr>
			<tr><td><input type="checkbox" id="PaladinsHiRez-PC"><label for="PaladinsHiRez-PC">Paladins Hi-Rez - PC</label></td></tr>
			<tr><td><input type="checkbox" id="PointBlank-PC"><label for="PointBlank-PC">PointBlank - PC</label></td></tr>
			<tr><td><input type="checkbox" id="PUBG-PC"><label for="PUBG-PC">PUBG - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Ragnarok2-PC"><label for="Ragnarok2-PC">Ragnarok 2 - PC</label></td></tr>
			<tr><td><input type="checkbox" id="RFRisingForce-PC"><label for="RFRisingForce-PC">RF Rising Force - PC</label></td></tr>
			<tr><td><input type="checkbox" id="ROERingofElysium-PC"><label for="ROERingofElysium-PC">Ring of Elysium (ROE) - PC</label></td></tr>
			<tr><td><input type="checkbox" id="ROSRulesOfSurvival-PC"><label for="ROSRulesOfSurvival-PC">Rules Of Survival (ROS) - PC</label></td></tr>
			<tr><td><input type="checkbox" id="SealOnline-PC"><label for="SealOnline-PC">Seal Online - PC</label></td></tr>
			<tr><td><input type="checkbox" id="SpecialForce-PC"><label for="SpecialForce-PC">Special Force - PC</label></td></tr>
			<tr><td><input type="checkbox" id="StarCraftII-PC"><label for="StarCraftII-PC">StarCraft II - PC</label></td></tr>
			<tr><td><input type="checkbox" id="StarCraft-PC"><label for="StarCraft-PC">StarCraft - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Valorant-PC"><label for="Valorant-PC">Valorant (RIOT) - PC</label></td></tr>
			<tr><td><input type="checkbox" id="WarcraftIIBattleNet-PC"><label for="WarcraftIIBattleNet-PC">Warcraft II - PC</label></td></tr>
			<tr><td><input type="checkbox" id="WarcraftIII-PC"><label for="WarcraftIII-PC">Warcraft III - PC</label></td></tr>
			<tr><td><input type="checkbox" id="Warframe-PC"><label for="Warframe-PC">Warframe - PC</label></td></tr>
			<tr><td><input type="checkbox" id="WorldofTanks-PC"><label for="WorldofTanks-PC">World of Tanks (WOT) - PC</label></td></tr>
			<tr><td><input type="checkbox" id="WorldofWarcraft-PC"><label for="WorldofWarcraft-PC">World of Warcraft - PC</label></td></tr>
			<tr><td><input type="checkbox" id="XshotIndonesia-PC"><label for="XshotIndonesia-PC">X-shot Indonesia - PC</label></td></tr>
	     </table>
         </div>
         <br>
         <button style="margin-right:2px" type="button" onclick="myFunction()">Generate</button> 
		 <button type="button" onclick="location.reload()">Clear All</button> 
         <br>
    </div>
    <div class="content">
        <span style="margin-left:2px">Script Generator Results</span>
         <div class="list-mangle">
		<div style="width:100%; height:90px; margin-top:10px; margin-left:10px; margin-bottom:5px;">	
			<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
			<!-- MIKROTIK SCRIPT DB3 -->
			<ins class="adsbygoogle"
			style="display:inline-block;width:728px;height:90px"
			data-ad-client="ca-pub-3815059393374366"
			data-ad-slot="4637807296"></ins>
			<script>
			(adsbygoogle = window.adsbygoogle || []).push({});
			</script> 
		</div>
            <table id="showScript" style="padding:5px;">
               <tr>
                  <td>
                     <span style="color:green;">
                     ######################################################<br>
                     # Vpn Game Script Generator By mikrotiktool.Github.io<br>
                     # Date/Time: <span id="tanggalwaktu"></span><br>
                     # mikrotiktool.Github.io +  <br>
                     # VPN Type -> <span id="vpn-list-text-info" style="font-weight:bold;text-transform: uppercase">PPTP</span><br>
                     ######################################################<br><br>
                     </span>
                     <span style="color:black;font-weight:bold">/interface <span id="vpn-list-text">pptp</span>-client</span><br>
                     add connect-to="<span id="vpn-ip-text">x.x.x.x</span>" disabled=no name="<span id="vpn-name-text1">VPN-GAME</span>" user="<span id="vpn-username-text">******</span>" password="<span id="vpn-password-text">******</span>" comment="<span id="vpn-name-text4">VPN-GAME</span>"<br>
                     <span style="color:black;font-weight:bold">/ip firewall nat</span><br>
                     add chain=srcnat out-interface="<span id="vpn-name-text2">VPN-GAME</span>" action=masquerade comment="<span id="vpn-name-text5">VPN-GAME</span>"<br>
                     <span style="color:black;font-weight:bold">/ip route</span><br>
                     add gateway="<span id="vpn-name-text3">VPN-GAME</span>" routing-mark=vpn-routing-game comment="<span id="vpn-name-text6">VPN-GAME</span>"<br>
                     <span id="ip-gateway-text" style="display:none">
                     <span style="color:black;font-weight:bold">/ip route</span><br>
                     add dst-address="<span id="vpn-ip-text2">x.x.x.x.x</span>" gateway="<span id="ip-gateway-game">x.x.x.x.x</span>" comment="<span id="vpn-name-text7">VPN-GAME</span>"
                     </span>
                     <span style="color:black;font-weight:bold">/ip firewall mangle</span>
				<!-- MOBILE GAMES -->
				<span id="ArenaOfValorMobile" style="display:none"><span style="color:green"># Arena Of Valor - Mobile</span><br>add action=mark-routing chain=prerouting comment="Arena Of Valor - Mobile" dst-port=10001-10094 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=10080,17000 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="BooyaCapsaSusunMobile" style="display:none"><span style="color:green"># Booya Capsa Susun - Mobile</span><br>add action=mark-routing chain=prerouting comment="Booya Capsa Susun - Mobile" dst-port=7090-7100 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="BooyaDominoQiuqiuMobile" style="display:none"><span style="color:green"># Booya Domino Qiuqiu - Mobile</span><br>add action=mark-routing chain=prerouting comment="Booya Domino Qiuqiu - Mobile" dst-port=7020-7030 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="ClashOfClansMobile" style="display:none"><span style="color:green"># Clash Of Clans - Mobile</span><br>add action=mark-routing chain=prerouting comment="Clash Of Clans - Mobile" dst-port=9330-9340 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="ClashRoyaleMobile" style="display:none"><span style="color:green"># Clash Royale (Cry) - Mobile</span><br>add action=mark-routing chain=prerouting comment="Clash Royale (Cry) - Mobile" dst-port=9330-9340 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=9330-9340 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="CODCallOfDutyMobile" style="display:none"><span style="color:green"># Call Of Duty - Mobile</span><br>add action=mark-routing chain=prerouting comment="Call Of Duty - Mobile" dst-port=3013,10000-10019,50000,65010,65050 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=7085-7995,8700,9030,10010-10019 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="DominoQqMobile" style="display:none"><span style="color:green"># Domino Qq - Mobile</span><br>add action=mark-routing chain=prerouting comment="Domino Qq - Mobile" dst-port=9122,11000-11150 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="DragonNestMobile" style="display:none"><span style="color:green"># Dragon Nest - Mobile</span><br>add action=mark-routing chain=prerouting comment="Dragon Nest - Mobile" dst-port=10514 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="DreamLeagueSoccerMobile" style="display:none"><span style="color:green"># Dream League Soccer - Mobile</span><br>add action=mark-routing chain=prerouting comment="Dream League Soccer - Mobile" dst-port=60970-60980 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="FreeFireMobile" style="display:none"><span style="color:green"># Free Fire - Mobile</span><br>add action=mark-routing chain=prerouting comment="Free Fire - Mobile" dst-port=6006,7006,8006,9006,11000-11019,39003,39006,39698,39779,10000-10007 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=6008,7008,8008,9008,10000-10009,11000-11019 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="LastEmpireWarZMobile" style="display:none"><span style="color:green"># Last Empire War Z - Mobile</span><br>add action=mark-routing chain=prerouting comment="Last Empire War Z - Mobile" dst-port=9930-9940 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="LineLetsGetRichMobile" style="display:none"><span style="color:green"># Line Lets Get Rich - Mobile</span><br>add action=mark-routing chain=prerouting comment="Line Lets Get Rich - Mobile" dst-port=10500-10515 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="MobileLegendsMobile" style="display:none"><span style="color:green"># Mobile Legends - Mobile</span><br>add action=mark-routing chain=prerouting comment="Mobile Legends - Mobile" dst-port=5001-5180,5501-5680,9443,30000-30220,9001 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=5001-5180,5501-5680,9992,30020-30220,9001 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="MostlyMobile" style="display:none"><span style="color:green"># Mostly - Mobile</span><br>add action=mark-routing chain=prerouting comment="Mostly - Mobile" dst-port=9933 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="NarutoLittleNinjaMobile" style="display:none"><span style="color:green"># Naruto Little Ninja (China) - Mobile</span><br>add action=mark-routing chain=prerouting comment="Naruto Little Ninja (China) - Mobile" dst-port=6170-6180 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="NetmarbleMobile" style="display:none"><span style="color:green"># Netmarble - Mobile</span><br>add action=mark-routing chain=prerouting comment="Netmarble - Mobile" dst-port=12000-12010 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="PointBlankMobile" style="display:none"><span style="color:green"># Point Blank - Mobile</span><br>add action=mark-routing chain=prerouting comment="Point Blank - Mobile" dst-port=44590-44610 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="PUBGMobile" style="display:none"><span style="color:green"># PUBG - Mobile</span> <br>add action=mark-routing chain=prerouting comment="PUBG - Mobile" dst-port=14000,17000,17500,18081,20000-20002,10012,17500 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=10020-14000,17000,17500,20000-20002,7086-7995,12070-12460,41182-42474 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="RpgToramOnlineMobile" style="display:none"><span style="color:green"># Rpg Toram Online - Mobile</span><br>add action=mark-routing chain=prerouting comment="Rpg Toram Online - Mobile" dst-port=30100-30110 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="RulesOfSurvivalMobile" style="display:none"><span style="color:green"># Rules Of Survival - Mobile</span><br>add action=mark-routing chain=prerouting comment="Rules Of Survival - Mobile" dst-port=24000-24050 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="ShinobiHeroesMobile" style="display:none"><span style="color:green"># Shinobi Heroes - Mobile</span><br>add action=mark-routing chain=prerouting comment="Shinobi Heroes - Mobile" dst-port=10005-10020 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<!-- WEB GAMES -->
				<span id="BallPoolWebGames" style="display:none"><span style="color:green"># 8 Ball Pool - Web Games</span><br>add action=mark-routing chain=prerouting comment="8 Ball Pool (Miniclips - Web Games" dst-port=4000 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="CastleVilleWebGames" style="display:none"><span style="color:green">#CastleVille - Web Games</span><br>add action=mark-routing chain=prerouting comment="CastleVille - Web Games" dst-port=8890 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="HeadshotWebGames" style="display:none"><span style="color:green"># Headshot - Web Games</span><br>add action=mark-routing chain=prerouting comment="Headshot - Web Games" dst-port=1800-1810 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=1845-1860 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="EmpireAlliesWebGames" style="display:none"><span style="color:green"># Empire & Allies - Web Games</span><br>add action=mark-routing chain=prerouting comment="Empire & Allies - Web Games" dst-port=8890 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="LeagueofAngelsWebGames" style="display:none"><span style="color:green"># League of Angels 2 - Web Games</span><br>add action=mark-routing chain=prerouting comment="League of Angels 2 - Web Games" dst-port=51700-51715 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="MegarealmRiseOfChaosWebGames" style="display:none"><span style="color:green"># Megarealm: Rise Of Chaos - Web Games</span><br>add action=mark-routing chain=prerouting comment="Megarealm: Rise Of Chaos" dst-port=26590-26600 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="PerjuanganSemutWebGames" style="display:none"><span style="color:green"># Perjuangan Semut  - Web Games</span><br>add action=mark-routing chain=prerouting comment="Perjuangan Semut  - Web Games" dst-port=7200-7210,7450-7460 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="RobloxWebGames" style="display:none"><span style="color:green"># Roblox - Web Games</span><br>add action=mark-routing chain=prerouting comment="Roblox - Web Games" dst-port=49152-65535 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="SwordofAngelsWebGames" style="display:none"><span style="color:green"># Sword of Angels - Web Games</span><br>add action=mark-routing chain=prerouting comment="Sword of Angels - Web Games" dst-port=15490-15510 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="TexasHoldEmPokerWebGames" style="display:none"><span style="color:green"># Texas HoldEm Poker - Games Web</span><br>add action=mark-routing chain=prerouting comment="Texas HoldEm Poker - Games Web" dst-port=9339 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="WarflareWebGames" style="display:none"><span style="color:green"># Warflare - Web Games</span><br>add action=mark-routing chain=prerouting comment="Warflare - Web Games" dst-port=64990-65010 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="WildOnesWebGames" style="display:none"><span style="color:green"># WildOnes - Web Games</span><br>add action=mark-routing chain=prerouting comment="WildOnes - Web Games" dst-port=8000 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<!-- DEKSTOP / PC GAMES -->
				<span id="AtlanticaPC" style="display:none"><span style="color:green"># Atlantica - PC</span><br>add action=mark-routing chain=prerouting comment="Atlantica - PC" dst-port=4300 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="AuraKingdomPC" style="display:none"><span style="color:green"># Aura Kingdom - PC</span><br>add action=mark-routing chain=prerouting comment="Aura Kingdom - PC" dst-port=5540-5580 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="AyodancePC" style="display:none"><span style="color:green"># Ayo Dance - PC</span><br>add action=mark-routing chain=prerouting comment="Ayodance - PC" dst-port=18900-18910 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="OriginAPEXLegendsPC" style="display:none"><span style="color:green"># Origin APEX Legends - PC</span><br>add action=mark-routing chain=prerouting comment="Origin APEX Legends - PC" dst-port=9960-9969,1024-1124,3216,18000,18120,18060,27900,28910,29900 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=1024-1124,18000,29900,37000-40000 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="BlackDesertOnlinePC" style="display:none"><span style="color:green"># Black Desert Online - PC</span><br>add action=mark-routing chain=prerouting comment="Black Desert Online - PC" dst-port=8888,9991-9993 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=8888,9991-9993 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="BlackDesertOnlinePSPC" style="display:none"><span style="color:green"># Black Desert Online - Playstation </span><br>add action=mark-routing chain=prerouting comment="Black Desert Online Playstation" dst-port=1935,3478-3480 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=3074,3478-3479 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="BlackDesertOnlineSteamPC" style="display:none"><span style="color:green"># Black Desert Online Steam - PC</span><br>add action=mark-routing chain=prerouting comment="Black Desert Online Steam - PC" dst-port=8888,9991-9993,27015-27030,27036-27037 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=4380,8888,9991-9993,27000-27031,27036 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="BlackDesertOnlineXboxPC" style="display:none"><span style="color:green"># Black Desert Online - Xbox</span><br>add action=mark-routing chain=prerouting comment="Black Desert Online Xbox - PC" dst-port=3074 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=88,500,3074,3544,4500 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="BlackretributionPC" style="display:none"><span style="color:green"># Black retribution - PC</span><br>add action=mark-routing chain=prerouting comment="Blackretribution - PC" dst-port=7020-7050,8200-8220,9000-9020 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="BlizzardBattleNetPC" style="display:none"><span style="color:green"># Blizzard BattleNet - PC</span><br>add action=mark-routing chain=prerouting comment="Blizzard BattleNet - PC" dst-port=1119 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=1119 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="BountyHoundPC" style="display:none"><span style="color:green"># Bounty Hound - PC</span><br>add action=mark-routing chain=prerouting comment="Bounty Hound - PC" dst-port=9810-9860 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="CabalExtremePrivatePC" style="display:none"><span style="color:green"># Cabal Extreme Private - PC</span><br>add action=mark-routing chain=prerouting comment="Cabal Extreme Private - PC" dst-port=60170-60180,63000-64000,38101,38110-38600 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="CabalIndonesiaPC" style="display:none"><span style="color:green"># Cabal Indonesia - PC</span><br>add action=mark-routing chain=prerouting comment="Cabal Indonesia - PC" dst-port=63000-64000,38101,38110-38130 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="CallofDutyBOPC" style="display:none"><span style="color:green"># Call of Duty BO - PC</span><br>add action=mark-routing chain=prerouting comment="Call of Duty BO - PC" dst-port=3074,27014-27050 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=3478,4379-4380,27000-27031,27036 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="CallofDutyBOPSPC" style="display:none"><span style="color:green"># Callof Duty BO - Playstation</span><br>add action=mark-routing chain=prerouting comment="Call of Duty BO - Playstation" dst-port=80,443,1935,3478-3480 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=3478-3479 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="CallofDutyBOXBoxPC" style="display:none"><span style="color:green"># Call of Duty BO - XBox</span><br>add action=mark-routing chain=prerouting comment="Call of Duty BO - XBox" dst-port=53,80,3074 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=88,500,3074,3075,3544,4500 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="CallofDutyMWPCPC" style="display:none"><span style="color:green"># Call of Duty MW - PC</span><br>add action=mark-routing chain=prerouting comment="Call of Duty MW PC - PC" dst-port=3074,27014-27050 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=3074,3478,4379-4380,27000-27031,27036 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="CallofDutyMWPSPC" style="display:none"><span style="color:green"># Call of Duty MW - Playstation</span><br>add action=mark-routing chain=prerouting comment="Call of Duty MW Playstation" dst-port=1935,3478-3480 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=3074,3478-3479 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="CallofDutyMWXBoxPC" style="display:none"><span style="color:green"># Call of Duty MW - XBox</span><br>add action=mark-routing chain=prerouting comment="Call of Duty MW - XBox" dst-port=3074 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=88,500,3074,3075,3544,4500 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="ClashofGodPC" style="display:none"><span style="color:green"># Clash of God - PC</span><br>add action=mark-routing chain=prerouting comment="Clash of God - PC" dst-port=9430-9450,5220-5230 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="CSGOPlaystationPC" style="display:none"><span style="color:green"># CSGO - Playstation </span><br>add action=mark-routing chain=prerouting comment="CSGO - Playstation" dst-port=3478-3480,5223,8080 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=3074,3478-3479,3658 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="CSGOSteamPC" style="display:none"><span style="color:green"># CSGO Steam - PC</span><br>add action=mark-routing chain=prerouting comment="CSGO Steam - PC" dst-port=27015-27030,27036-27037 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=4380,27000-27031,27036 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="CSGOXboxPC" style="display:none"><span style="color:green"># CSGO - Xbox</span><br>add action=mark-routing chain=prerouting comment="CSGO - Xbox" dst-port=3074 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=88,3074 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="CrossFireindonesiaPC" style="display:none"><span style="color:green"># CrossFire indonesia - PC</span><br>add action=mark-routing chain=prerouting comment="Cross Fire indonesia - PC" dst-port=10009,13008,16666,28012 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=12020-12080,13000-13080 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="DiabloIIIPC" style="display:none"><span style="color:green"># Diablo III - PC</span><br>add action=mark-routing chain=prerouting comment="Diablo III - PC" dst-port=1119 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=1119,6120 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="DiabloIIPC" style="display:none"><span style="color:green"># Diablo II - PC</span><br>add action=mark-routing chain=prerouting comment="Diablo II - PC" dst-port=6112,4000 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="DiabloPC" style="display:none"><span style="color:green"># Diablo I - PC</span><br>add action=mark-routing chain=prerouting comment="Diablo - PC" dst-port=6112-6119 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=6112-6119 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="Dota2SteamPC" style="display:none"><span style="color:green"># DOTA2 Steam - PC</span><br>add action=mark-routing chain=prerouting comment="Dota 2 Steam - PC" dst-port=9100-9200,8230-8250,8110-8120 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=28010-28200,27010-27200,39000 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="DragonNestPC" style="display:none"><span style="color:green"># Dragon Nest - PC</span><br>add action=mark-routing chain=prerouting comment="Dragon Nest - PC" dst-port=14300-15512 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=15000-15500 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="DragonaPC" style="display:none"><span style="color:green"># Dragona - PC</span><br>add action=mark-routing chain=prerouting comment="Dragona - PC" dst-port=10000-10030 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="FifaOnline3PC" style="display:none"><span style="color:green"># Fifa Online 3 - PC</span><br>add action=mark-routing chain=prerouting comment="Fifa Online 3 - PC" dst-port=7770-7790 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=16300-16350 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="FORTNITEEPICGAMESPC" style="display:none"><span style="color:green"># FORTNITE EPIC GAMES - PC</span><br>add action=mark-routing chain=prerouting comment="FORTNITE EPIC GAMES - PC" dst-port=9000-9100 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="LeagueofLegendPC" style="display:none"><span style="color:green"># League of Legend - PC</span><br>add action=mark-routing chain=prerouting comment="League of Legend - PC" dst-port=2080-2099 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port="5100 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="GrandChasePC" style="display:none"><span style="color:green"># Grand Chase - PC</span><br>add action=mark-routing chain=prerouting comment="Grand Chase - PC" dst-port=9300,9400,9700 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="HearthstonePC" style="display:none"><span style="color:green"># Hearthstone - PC</span><br>add action=mark-routing chain=prerouting comment="Hearthstone - PC" dst-port=1119,3724 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port="1119,3724 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="HeroesoftheStormPC" style="display:none"><span style="color:green"># Heroes of the Storm - PC</span><br>add action=mark-routing chain=prerouting comment="Heroes of the Storm - PC" dst-port=1119-1120,3724,6113 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=1119-1120,3478-3479,3724,5060,5062,6113,6250,12000-64000 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="HONHeroesofNewerthPC" style="display:none"><span style="color:green"># HON Heroes of Newerth - PC</span><br>add action=mark-routing chain=prerouting comment="HON Heroes of Newerth - PC" dst-port=9100-9200,11200-11500 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="HeroesofAtarsiaPC" style="display:none"><span style="color:green"># Heroes of Atarsia - PC</span><br>add action=mark-routing chain=prerouting comment="Heroes of Atarsia - PC" dst-port=7777,9400 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="IdolStreetPC" style="display:none"><span style="color:green"># Idol Street - PC</span><br>add action=mark-routing chain=prerouting comment="Idol Street - PC" dst-port=2001-2010 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="Left4Dead2SteamPC" style="display:none"><span style="color:green"># Left4Dead 2 Steam - PC</span><br>add action=mark-routing chain=prerouting comment="Left4Dead 2 Steam - PC" dst-port=4360-4390 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="Lineage2PC" style="display:none"><span style="color:green"># Lineage 2 - PC</span><br>add action=mark-routing chain=prerouting comment="Lineage 2 - PC" dst-port=7777,10000,11000,13000 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="LostSagaPC" style="display:none"><span style="color:green"># Lost Saga - PC</span><br>add action=mark-routing chain=prerouting comment="Lost Saga - PC" dst-port=14000-14010 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=14000-14010 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="LuneofedenPC" style="display:none"><span style="color:green"># Lune of eden - PC</span><br>add action=mark-routing chain=prerouting comment="Lune of eden - PC" dst-port=8400 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="MercenaryopsPC" style="display:none"><span style="color:green"># Mercenary ops - PC</span><br>add action=mark-routing chain=prerouting comment="Mercenary ops - PC" dst-port=6000-6125 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="MircovoltPC" style="display:none"><span style="color:green"># Mircovolt - PC</span><br>add action=mark-routing chain=prerouting comment="Mircovolt - PC" dst-port=13000 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="ModoMarblePC" style="display:none"><span style="color:green"># Modo Marble - PC</span><br>add action=mark-routing chain=prerouting comment="Modo Marble - PC" dst-port=28900-28914 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="OverwatchPC" style="display:none"><span style="color:green"># Overwatch - PC</span><br>add action=mark-routing chain=prerouting comment="Overwatch - PC" dst-port=1119,3724,6113 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=3478-3479,5060,5062,6250,12000-64000 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="PaladinsHiRezPC" style="display:none"><span style="color:green"># Paladins Hi-Rez - PC</span><br>add action=mark-routing chain=prerouting comment="Paladins Hi-Rez - PC" dst-port=9000-9999 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=9002-9999 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="PointBlankPC" style="display:none"><span style="color:green"># Point Blank - PC</span><br>add action=mark-routing chain=prerouting comment="Point Blank - PC" dst-port=39190-39200 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=40000-40010 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="PUBGPC" style="display:none"><span style="color:green"># PUBG Playerunknown Battlegrounds - PC</span><br>add action=mark-routing chain=prerouting comment="PUBG Playerunknown Battlegrounds - PC" dst-port=7080-8000 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="Ragnarok2PC" style="display:none"><span style="color:green"># Ragnarok 2 - PC</span><br>add action=mark-routing chain=prerouting comment="Ragnarok 2 - PC" dst-port=7201-7210,7401-7410 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="RFRisingForcePC" style="display:none"><span style="color:green"># RF Rising Force - PC</span><br>add action=mark-routing chain=prerouting comment="RF Rising Force - PC" dst-port=27780 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="ROERingofElysiumPC" style="display:none"><span style="color:green"># ROE Ring of Elysium - PC</span><br>add action=mark-routing chain=prerouting comment="ROE Ring of Elysium - PC" dst-port=9002,10000-10015 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="ROSRulesOfSurvivalPC" style="display:none"><span style="color:green"># ROS Rules Of Survival - PC</span><br>add action=mark-routing chain=prerouting comment="ROS Rules Of Survival - PC" dst-port=24000-24100 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="SealOnlinePC" style="display:none"><span style="color:green"># Seal Online - PC</span><br>add action=mark-routing chain=prerouting comment="Seal Online - PC" dst-port=1818 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="SpecialForcePC" style="display:none"><span style="color:green"># Special Force - PC</span><br>add action=mark-routing chain=prerouting comment="Special Force - PC" dst-port=27920-27940 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=30000-30030 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="StarCraftIIPC" style="display:none"><span style="color:green"># StarCraft II - PC</span><br>add action=mark-routing chain=prerouting comment="StarCraft II - PC" dst-port=1119,6113,1120,80,3724 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=1119,6113,1120,80,3724 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="StarCraftPC" style="display:none"><span style="color:green"># StarCraft - PC</span><br>add action=mark-routing chain=prerouting comment="StarCraft - PC" dst-port=6112 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=9401,9600,16440-16450 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="ValorantPC" style="display:none"><span style="color:green"># Valorant - PC Games</span><br>add action=mark-routing chain=prerouting comment="Valorant - PC Games" dst-port=2099,5222-5223,8088,8393-8400 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=7000-7500,8088 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="WarcraftIIBattleNetPC" style="display:none"><span style="color:green"># Warcraft II BattleNet - PC</span><br>add action=mark-routing chain=prerouting comment="Warcraft II BattleNet - PC" dst-port=6112-6119 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=6112-6119 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="WarcraftIIIPC" style="display:none"><span style="color:green"># Warcraft III - PC</span><br>add action=mark-routing chain=prerouting comment="Warcraft III - PC" dst-port=6112,6113-6119 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp</span>
				<span id="WarframePC" style="display:none"><span style="color:green"># Warframe - PC</span><br>add action=mark-routing chain=prerouting comment="Warframe - PC" dst-port=4950-4955 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=6695-6699 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="WorldofTanksPC" style="display:none"><span style="color:green"># World of Tanks - PC</span><br>add action=mark-routing chain=prerouting comment="World of Tanks - PC" dst-port=20000-25000,8081,8088,32801,3280 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=12000-29999,32801-32825, 5060,5062,3478,3479,20014 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="WorldofWarcraftPC" style="display:none"><span style="color:green"># World of Warcraft - PC</span><br>add action=mark-routing chain=prerouting comment="World of Warcraft - PC" dst-port=3724,1119,6012 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=3724,1119,6012 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				<span id="XshotIndonesiaPC" style="display:none"><span style="color:green"># Xshot Indonesia - PC</span><br>add action=mark-routing chain=prerouting comment="Xshot Indonesia - PC" dst-port=7320-7350 new-routing-mark=vpn-routing-game passthrough=no protocol=tcp<br>add action=mark-routing chain=prerouting dst-port=7800-7850,30000 new-routing-mark=vpn-routing-game passthrough=no protocol=udp</span>
				</td>
				</tr>			
			</table>
         </div>
         <br>
         <button id="copyscript" type="button" onclick="selectElementContents(document.getElementById('showScript'))">Copy Script</button><span style="padding:7px; font-weight:normal;margin-left:10px"><b>Copy-Paste Script to Terminal, make sure Script on the Top position!</b></span>
    </div>
     </div>
   <div class="footer">
<div style="float:right">
© Copyright 2020-2021 <a href="https://mikrotiktool.github.io/">mikrotiktool.github.io</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a><br> Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a>  
</div>    
<span style="color:#4CAF50">
<b># This Script for remove all Script from this Generator</b></span><br>
/interface pptp-client remove [find comment="VPN-GAME"]<br>
/interface l2tp-client remove [find comment="VPN-GAME"]<br>
/ip route remove [find comment="VPN-GAME"]<br>
/ip firewall nat remove [find comment="VPN-GAME"]<br>
/ip firewall mangle remove [find new-routing-mark="vpn-routing-game"]
</div> 
</div>
 	 	 
	<script type="text/javascript">
var modal = document.getElementById("myModal");
var span = document.getElementsByClassName("close")[0];
function callmodal() {
  modal.style.display = "block";
}
span.onclick = function() {
  modal.style.display = "none";
}
window.onclick = function(event) {
  if (event.target == modal) {
    modal.style.display = "none";
  }
}
</script> 
<script>  
var _0xffd3=['ShinobiHeroes-Mobile','MobileLegends-Mobile','WorldofTanksPC','205289RuNMoC','textContent','match','AuraKingdom-PC','BooyaCapsaSusun-Mobile','BlackDesertOnlineSteamPC','WarcraftIIBattleNet-PC','TexasHoldEmPokerWebGames','CallofDutyBO-PC','vpn-username-text','CSGOPlaystationPC','toLocaleString','ROSRulesOfSurvivalPC','execCommand','AuraKingdomPC','BlackDesertOnlinePC','Warframe-PC','ip-gateway-game','CabalIndonesia-PC','FORTNITEEPICGAMES-PC','BooyaCapsaSusunMobile','HONHeroesofNewerthPC','DiabloIIIPC','BallPool-WebGames','WarframePC','HeroesoftheStormPC','RpgToramOnline-Mobile','BlackDesertOnlineXboxPC','166193XjkBzA','BlackDesertOnlineSteam-PC','Atlantica-PC','OriginAPEXLegendsPC','DiabloII-PC','createRange','ModoMarble-PC','length','vpn-ip-text2','CallofDutyMWPCPC','ip-gateway-check','getElementById','PUBG-PC','PointBlankMobile','CrossFireindonesia-PC','LeagueofLegendPC','none','RFRisingForcePC','181435qWPrtB','BooyaDominoQiuqiuMobile','WarcraftIIBattleNetPC','42501GTwcPT','addRange','selectNode','vpn-ip-text','CSGOPlaystation-PC','gateway','CODCallOfDutyMobile','OriginAPEXLegends-PC','FORTNITEEPICGAMESPC','HONHeroesofNewerth-PC','EmpireAllies-WebGames','Roblox-WebGames','15383mRXjHW','LineLetsGetRichMobile','CallofDutyBOPSPC','LostSagaPC','select','value','CallofDutyMWXBoxPC','413826GNcIwj','ip-gateway-input','body','toUpperCase','WorldofTanks-PC','SwordofAngelsWebGames','LastEmpireWarZMobile','DragonaPC','CSGOSteam-PC','RulesOfSurvivalMobile','PUBG-Mobile','CSGOSteamPC','DiabloPC','DragonNestMobile','PUBGMobile','PointBlankPC','createTextRange','ClashRoyaleMobile','SealOnline-PC','ClashRoyale-Mobile','style','Attention!\x20IP\x20Address\x20Validation\x20Incorrect!\x20Please\x20check\x20your\x20IP\x20address\x20again!','Dragona-PC','DreamLeagueSoccerMobile','MegarealmRiseOfChaosWebGames','vpn-password-text','RulesOfSurvival-Mobile','CallofDutyBOPS-PC','BlackDesertOnlinePS-PC','EmpireAlliesWebGames','vpn-name-text2','NarutoLittleNinja-Mobile','BallPoolWebGames','DominoQqMobile','Hearthstone-PC','WarcraftIII-PC','13295Cdmgrp','PointBlank-PC','ROERingofElysiumPC','indexOf','BountyHound-PC','MercenaryopsPC','CrossFireindonesiaPC','StarCraft-PC','71FFXROT','SealOnlinePC','DreamLeagueSoccer-Mobile','CastleVille-WebGames','17VuWDmW','TexasHoldEmPoker-WebGames','Dota2SteamPC','CODCallOfDuty-Mobile','innerHTML','BlizzardBattleNet-PC','BlackDesertOnlineXbox-PC','ClashOfClans-Mobile','CallofDutyMWPS-PC','XshotIndonesia-PC','FifaOnline3PC','OverwatchPC','innerText','disabled','GrandChase-PC','myTable','LineLetsGetRich-Mobile','SpecialForce-PC','DominoQq-Mobile','StarCraftPC','PerjuanganSemutWebGames','Left4Dead2SteamPC','MobileLegendsMobile','vpn-name-text3','Left4Dead2Steam-PC','1AJuZKs','Ragnarok2-PC','ip-gateway-text','CastleVilleWebGames','AyodancePC','CabalExtremePrivate-PC','LeagueofAngelsWebGames','<span\x20style=\x27color:red;\x20font-weight:bold\x27>[empty]<span>','Overwatch-PC','RpgToramOnlineMobile','BlizzardBattleNetPC','FifaOnline3-PC','StarCraftIIPC','selectNodeContents','PaladinsHiRez-PC','getElementsByTagName','WarcraftIIIPC','DragonNest-Mobile','block','LeagueofLegend-PC','NetmarbleMobile','SwordofAngels-WebGames','BooyaDominoQiuqiu-Mobile','vpn-name-text1','CSGOXboxPC','CallofDutyBOPC','ModoMarblePC','DragonNest-PC','WarflareWebGames','CallofDutyMWPSPC','getSelection','BlackDesertOnline-PC','IdolStreet-PC','CabalIndonesiaPC','ipaddress','HeroesofAtarsiaPC','vpn-ip','Diablo-PC','HearthstonePC','HeroesofAtarsia-PC','display','DiabloIII-PC','MircovoltPC','CallofDutyMWXBox-PC','FreeFireMobile','ClashofGodPC','checked','RobloxWebGames'];var _0x1043=function(_0x15e82b,_0x12912f){_0x15e82b=_0x15e82b-0x7e;var _0xffd30a=_0xffd3[_0x15e82b];return _0xffd30a;};var _0x117878=_0x1043;(function(_0x1c2cd2,_0xeee3e1){var _0x540916=_0x1043;while(!![]){try{var _0x32f53f=parseInt(_0x540916(0xd8))+-parseInt(_0x540916(0x10f))*parseInt(_0x540916(0x11b))+parseInt(_0x540916(0xc3))+-parseInt(_0x540916(0xeb))+-parseInt(_0x540916(0x134))*parseInt(_0x540916(0xa7))+-parseInt(_0x540916(0xd5))+-parseInt(_0x540916(0x117))*-parseInt(_0x540916(0xe4));if(_0x32f53f===_0xeee3e1)break;else _0x1c2cd2['push'](_0x1c2cd2['shift']());}catch(_0x4ac6b2){_0x1c2cd2['push'](_0x1c2cd2['shift']());}}}(_0xffd3,0x42f92));var dt=new Date();document[_0x117878(0xce)]('tanggalwaktu')[_0x117878(0x11f)]=dt[_0x117878(0xb2)](),document[_0x117878(0xce)](_0x117878(0xcd))['onchange']=function(){var _0x29026c=_0x117878;document[_0x29026c(0xce)](_0x29026c(0xec))[_0x29026c(0x128)]=!this[_0x29026c(0xa2)];var _0x723c08=document[_0x29026c(0xce)](_0x29026c(0xcd)),_0x48ecae=document[_0x29026c(0xce)](_0x29026c(0x136));_0x723c08['checked']?_0x48ecae[_0x29026c(0xff)][_0x29026c(0x9c)]='block':_0x48ecae[_0x29026c(0xff)][_0x29026c(0x9c)]=_0x29026c(0xd3);};function myFunction(){var _0x474387=_0x117878,_0x364939=document[_0x474387(0xce)]('vpn-list')[_0x474387(0xe9)];document['getElementById']('vpn-list-text')['innerHTML']=_0x364939,document[_0x474387(0xce)]('vpn-list-text-info')['innerHTML']=_0x364939;var _0x38dbf9=document['getElementById']('vpn-name')['value'];_0x38dbf9!=''&&_0x38dbf9!=null?(document['getElementById']('vpn-name-text1')['innerHTML']=_0x38dbf9,document['getElementById']('vpn-name-text2')['innerHTML']=_0x38dbf9,document[_0x474387(0xce)](_0x474387(0x132))['innerHTML']=_0x38dbf9):(document[_0x474387(0xce)](_0x474387(0x8b))[_0x474387(0x11f)]=_0x474387(0x13b),document[_0x474387(0xce)](_0x474387(0x109))['innerHTML']=_0x474387(0x13b),document[_0x474387(0xce)](_0x474387(0x132))[_0x474387(0x11f)]=_0x474387(0x13b));var _0x452804=document[_0x474387(0xce)](_0x474387(0x98))[_0x474387(0xe9)];_0x452804!=''&&_0x452804!=null?(document['getElementById'](_0x474387(0xdb))[_0x474387(0x11f)]=_0x452804,document[_0x474387(0xce)](_0x474387(0xcb))[_0x474387(0x11f)]=_0x452804):document[_0x474387(0xce)](_0x474387(0xdb))[_0x474387(0x11f)]=_0x474387(0x13b);var _0x1ca8de=document['getElementById']('vpn-username')[_0x474387(0xe9)];_0x1ca8de!=''&&_0x1ca8de!=null?document['getElementById'](_0x474387(0xb0))[_0x474387(0x11f)]=_0x1ca8de:document['getElementById'](_0x474387(0xb0))[_0x474387(0x11f)]=_0x474387(0x13b);var _0x20014c=document[_0x474387(0xce)]('vpn-password')['value'];_0x20014c!=''&&_0x20014c!=null?document[_0x474387(0xce)](_0x474387(0x104))['innerHTML']=_0x20014c:document['getElementById'](_0x474387(0x104))[_0x474387(0x11f)]='<span\x20style=\x27color:red;\x20font-weight:bold\x27>[empty]<span>';var _0x4667ac=document[_0x474387(0xce)](_0x474387(0xec))['value'];_0x4667ac!=''&&_0x4667ac!=null?document[_0x474387(0xce)](_0x474387(0xb8))[_0x474387(0x11f)]=_0x4667ac:document[_0x474387(0xce)]('ip-gateway-game')[_0x474387(0x11f)]=_0x474387(0x13b);var _0x37cbdc=document[_0x474387(0xce)]('ArenaOfValor-Mobile');toggleSub(_0x37cbdc,'ArenaOfValorMobile');var _0xc24662=document[_0x474387(0xce)](_0x474387(0xab));toggleSub(_0xc24662,_0x474387(0xbb));var _0x58004e=document['getElementById'](_0x474387(0x8a));toggleSub(_0x58004e,_0x474387(0xd6));var _0x47aeea=document[_0x474387(0xce)](_0x474387(0x122));toggleSub(_0x47aeea,'ClashOfClansMobile');var _0x392156=document[_0x474387(0xce)](_0x474387(0xfe));toggleSub(_0x392156,_0x474387(0xfc));var _0x12f05c=document[_0x474387(0xce)](_0x474387(0x11e));toggleSub(_0x12f05c,_0x474387(0xde));var _0xc6522e=document[_0x474387(0xce)](_0x474387(0x12d));toggleSub(_0xc6522e,_0x474387(0x10c));var _0x588bee=document[_0x474387(0xce)](_0x474387(0x119));toggleSub(_0x588bee,'DreamLeagueSoccerMobile');var _0x58eb8f=document[_0x474387(0xce)](_0x474387(0x85));toggleSub(_0x58eb8f,_0x474387(0xf8));var _0x588bee=document[_0x474387(0xce)](_0x474387(0x119));toggleSub(_0x588bee,_0x474387(0x102));var _0x1c539c=document['getElementById']('FreeFire-Mobile');toggleSub(_0x1c539c,_0x474387(0xa0));var _0xe64a7a=document['getElementById']('LastEmpireWarZ-Mobile');toggleSub(_0xe64a7a,_0x474387(0xf1));var _0xbeec59=document[_0x474387(0xce)](_0x474387(0x12b));toggleSub(_0xbeec59,_0x474387(0xe5));var _0x47ece0=document[_0x474387(0xce)](_0x474387(0xa5));toggleSub(_0x47ece0,_0x474387(0x131));var _0x28b846=document['getElementById']('Mostly-Mobile');toggleSub(_0x28b846,'MostlyMobile');var _0x2287e3=document[_0x474387(0xce)](_0x474387(0x10a));toggleSub(_0x2287e3,'NarutoLittleNinjaMobile');var _0x47fb3d=document['getElementById']('Netmarble-Mobile');toggleSub(_0x47fb3d,_0x474387(0x88));var _0x553d=document[_0x474387(0xce)]('PointBlank-Mobile');toggleSub(_0x553d,_0x474387(0xd0));var _0x35f148=document[_0x474387(0xce)](_0x474387(0xf5));toggleSub(_0x35f148,_0x474387(0xf9));var _0x5b767c=document[_0x474387(0xce)](_0x474387(0xc1));toggleSub(_0x5b767c,_0x474387(0x13d));var _0x1d872e=document[_0x474387(0xce)](_0x474387(0x105));toggleSub(_0x1d872e,_0x474387(0xf4));var _0x146a84=document[_0x474387(0xce)](_0x474387(0xa4));toggleSub(_0x146a84,'ShinobiHeroesMobile');var _0x631baa=document['getElementById'](_0x474387(0xbe));toggleSub(_0x631baa,_0x474387(0x10b));var _0x2f5a10=document[_0x474387(0xce)](_0x474387(0x11a));toggleSub(_0x2f5a10,_0x474387(0x137));var _0x3d73e7=document['getElementById']('Headshot-WebGames');toggleSub(_0x3d73e7,'HeadshotWebGames');var _0x30d835=document[_0x474387(0xce)](_0x474387(0xe2));toggleSub(_0x30d835,_0x474387(0x108));var _0x3dbc5a=document['getElementById']('LeagueofAngels-WebGames');toggleSub(_0x3dbc5a,_0x474387(0x13a));var _0x335671=document['getElementById']('MegarealmRiseOfChaos-WebGames');toggleSub(_0x335671,_0x474387(0x103));var _0x3ed780=document[_0x474387(0xce)]('PerjuanganSemut-WebGames');toggleSub(_0x3ed780,_0x474387(0x12f));var _0x17e584=document['getElementById'](_0x474387(0xe3));toggleSub(_0x17e584,_0x474387(0xa3));var _0xf08972=document['getElementById'](_0x474387(0x89));toggleSub(_0xf08972,_0x474387(0xf0));var _0x32e1d2=document['getElementById'](_0x474387(0x11c));toggleSub(_0x32e1d2,_0x474387(0xae));var _0x47fbea=document[_0x474387(0xce)]('Warflare-WebGames');toggleSub(_0x47fbea,_0x474387(0x90));var _0x252450=document[_0x474387(0xce)]('WildOnes-WebGames');toggleSub(_0x252450,'WildOnesWebGames');var _0x252822=document[_0x474387(0xce)](_0x474387(0xc5));toggleSub(_0x252822,'AtlanticaPC');var _0x29ad0d=document[_0x474387(0xce)](_0x474387(0xaa));toggleSub(_0x29ad0d,_0x474387(0xb5));var _0x4e9f9b=document['getElementById']('Ayodance-PC');toggleSub(_0x4e9f9b,_0x474387(0x138));var _0x3c431a=document[_0x474387(0xce)](_0x474387(0xdf));toggleSub(_0x3c431a,_0x474387(0xc6));var _0x5c04d7=document['getElementById'](_0x474387(0x93));toggleSub(_0x5c04d7,_0x474387(0xb6));var _0x3e8bd7=document['getElementById'](_0x474387(0x107));toggleSub(_0x3e8bd7,'BlackDesertOnlinePSPC');var _0x1a1938=document[_0x474387(0xce)](_0x474387(0xc4));toggleSub(_0x1a1938,_0x474387(0xac));var _0x3fc836=document[_0x474387(0xce)](_0x474387(0x121));toggleSub(_0x3fc836,_0x474387(0xc2));var _0x165758=document[_0x474387(0xce)]('Blackretribution-PC');toggleSub(_0x165758,'BlackretributionPC');var _0x2476b7=document[_0x474387(0xce)](_0x474387(0x120));toggleSub(_0x2476b7,_0x474387(0x7e));var _0x5b36c4=document['getElementById'](_0x474387(0x113));toggleSub(_0x5b36c4,'BountyHoundPC');var _0x48f7fb=document['getElementById'](_0x474387(0x139));toggleSub(_0x48f7fb,'CabalExtremePrivatePC');var _0x43b983=document[_0x474387(0xce)](_0x474387(0xb9));toggleSub(_0x43b983,_0x474387(0x95));var _0x4bcce5=document[_0x474387(0xce)](_0x474387(0xaf));toggleSub(_0x4bcce5,_0x474387(0x8d));var _0x400dc7=document[_0x474387(0xce)](_0x474387(0x106));toggleSub(_0x400dc7,_0x474387(0xe6));var _0x35b813=document['getElementById']('CallofDutyBOXBox-PC');toggleSub(_0x35b813,'CallofDutyBOXBoxPC');var _0x5566aa=document['getElementById']('CallofDutyMWPC-PC');toggleSub(_0x5566aa,_0x474387(0xcc));var _0x328fdf=document['getElementById'](_0x474387(0x123));toggleSub(_0x328fdf,_0x474387(0x91));var _0x23c00f=document[_0x474387(0xce)](_0x474387(0x9f));toggleSub(_0x23c00f,_0x474387(0xea));var _0x5a0596=document[_0x474387(0xce)]('ClashofGod-PC');toggleSub(_0x5a0596,_0x474387(0xa1));var _0x41355a=document['getElementById'](_0x474387(0xdc));toggleSub(_0x41355a,_0x474387(0xb1));var _0x33ad13=document['getElementById'](_0x474387(0xf3));toggleSub(_0x33ad13,_0x474387(0xf6));var _0x4a0f8b=document[_0x474387(0xce)]('CSGOXbox-PC');toggleSub(_0x4a0f8b,_0x474387(0x8c));var _0x5971fb=document['getElementById'](_0x474387(0xd1));toggleSub(_0x5971fb,_0x474387(0x115));var _0xdd2cc0=document['getElementById'](_0x474387(0x9d));toggleSub(_0xdd2cc0,_0x474387(0xbd));var _0xd4e446=document[_0x474387(0xce)](_0x474387(0xc7));toggleSub(_0xd4e446,'DiabloIIPC');var _0x502ed3=document[_0x474387(0xce)](_0x474387(0x99));toggleSub(_0x502ed3,_0x474387(0xf7));var _0x32fb9e=document['getElementById']('Dota2Steam-PC');toggleSub(_0x32fb9e,_0x474387(0x11d));var _0x31e13a=document[_0x474387(0xce)](_0x474387(0x8f));toggleSub(_0x31e13a,'DragonNestPC');var _0x3d5fe=document['getElementById'](_0x474387(0x101));toggleSub(_0x3d5fe,_0x474387(0xf2));var _0xcbbf6=document['getElementById'](_0x474387(0x7f));toggleSub(_0xcbbf6,_0x474387(0x125));var _0x138521=document[_0x474387(0xce)](_0x474387(0xba));toggleSub(_0x138521,_0x474387(0xe0));var _0x5c87b8=document[_0x474387(0xce)](_0x474387(0x87));toggleSub(_0x5c87b8,_0x474387(0xd2));var _0x2353a8=document[_0x474387(0xce)](_0x474387(0x129));toggleSub(_0x2353a8,'GrandChasePC');var _0x1d88c3=document['getElementById'](_0x474387(0x10d));toggleSub(_0x1d88c3,_0x474387(0x9a));var _0x51f6a6=document[_0x474387(0xce)]('HeroesoftheStorm-PC');toggleSub(_0x51f6a6,_0x474387(0xc0));var _0xd25ed3=document[_0x474387(0xce)](_0x474387(0xe1));toggleSub(_0xd25ed3,_0x474387(0xbc));var _0x20d504=document['getElementById'](_0x474387(0x9b));toggleSub(_0x20d504,_0x474387(0x97));var _0x276ca8=document['getElementById'](_0x474387(0x94));toggleSub(_0x276ca8,'IdolStreetPC');var _0x22a035=document[_0x474387(0xce)](_0x474387(0x133));toggleSub(_0x22a035,_0x474387(0x130));var _0x546609=document[_0x474387(0xce)]('Lineage2-PC');toggleSub(_0x546609,'Lineage2PC');var _0x118f63=document[_0x474387(0xce)]('LostSaga-PC');toggleSub(_0x118f63,_0x474387(0xe7));var _0x9f824d=document[_0x474387(0xce)]('Luneofeden-PC');toggleSub(_0x9f824d,'LuneofedenPC');var _0x26b546=document[_0x474387(0xce)]('Mercenaryops-PC');toggleSub(_0x26b546,_0x474387(0x114));var _0x581ab3=document[_0x474387(0xce)]('Mircovolt-PC');toggleSub(_0x581ab3,_0x474387(0x9e));var _0x354da2=document[_0x474387(0xce)](_0x474387(0xc9));toggleSub(_0x354da2,_0x474387(0x8e));var _0x59297f=document['getElementById'](_0x474387(0x13c));toggleSub(_0x59297f,_0x474387(0x126));var _0x4361e5=document['getElementById'](_0x474387(0x82));toggleSub(_0x4361e5,'PaladinsHiRezPC');var _0x502687=document[_0x474387(0xce)](_0x474387(0x110));toggleSub(_0x502687,_0x474387(0xfa));var _0x214090=document[_0x474387(0xce)](_0x474387(0xcf));toggleSub(_0x214090,'PUBGPC');var _0x1d2d85=document[_0x474387(0xce)](_0x474387(0x135));toggleSub(_0x1d2d85,'Ragnarok2PC');var _0x489255=document[_0x474387(0xce)]('RFRisingForce-PC');toggleSub(_0x489255,_0x474387(0xd4));var _0x47d308=document[_0x474387(0xce)]('ROERingofElysium-PC');toggleSub(_0x47d308,_0x474387(0x111));var _0xf2bce2=document['getElementById']('ROSRulesOfSurvival-PC');toggleSub(_0xf2bce2,_0x474387(0xb3));var _0x13735e=document[_0x474387(0xce)](_0x474387(0xfd));toggleSub(_0x13735e,_0x474387(0x118));var _0x1de5dd=document[_0x474387(0xce)](_0x474387(0x12c));toggleSub(_0x1de5dd,'SpecialForcePC');var _0x59a2da=document['getElementById']('StarCraftII-PC');toggleSub(_0x59a2da,_0x474387(0x80));var _0x1c0dcf=document[_0x474387(0xce)](_0x474387(0x116));toggleSub(_0x1c0dcf,_0x474387(0x12e));var _0x2e2d05=document[_0x474387(0xce)]('Valorant-PC');toggleSub(_0x2e2d05,'ValorantPC');var _0x1590e6=document['getElementById'](_0x474387(0xad));toggleSub(_0x1590e6,_0x474387(0xd7));var _0x366467=document[_0x474387(0xce)](_0x474387(0x10e));toggleSub(_0x366467,_0x474387(0x84));var _0x203355=document[_0x474387(0xce)](_0x474387(0xb7));toggleSub(_0x203355,_0x474387(0xbf));var _0x3a83d3=document['getElementById'](_0x474387(0xef));toggleSub(_0x3a83d3,_0x474387(0xa6));var _0x537808=document[_0x474387(0xce)]('WorldofWarcraft-PC');toggleSub(_0x537808,'WorldofWarcraftPC');var _0x5dbb27=document[_0x474387(0xce)](_0x474387(0x124));toggleSub(_0x5dbb27,'XshotIndonesiaPC');}function selectElementContents(_0x1f4f99){var _0x1a404e=_0x117878,_0xd27833=document[_0x1a404e(0xed)],_0x4fa0ad,_0x5af286;if(document[_0x1a404e(0xc8)]&&window[_0x1a404e(0x92)]){_0x4fa0ad=document[_0x1a404e(0xc8)](),_0x5af286=window[_0x1a404e(0x92)](),_0x5af286['removeAllRanges']();try{_0x4fa0ad[_0x1a404e(0x81)](_0x1f4f99),_0x5af286[_0x1a404e(0xd9)](_0x4fa0ad);}catch(_0x158979){_0x4fa0ad[_0x1a404e(0xda)](_0x1f4f99),_0x5af286[_0x1a404e(0xd9)](_0x4fa0ad);}}else _0xd27833[_0x1a404e(0xfb)]&&(_0x4fa0ad=_0xd27833['createTextRange'](),_0x4fa0ad['moveToElementText'](_0x1f4f99),_0x4fa0ad[_0x1a404e(0xe8)]());document[_0x1a404e(0xb4)]('copy');}function toggleSub(_0x401002,_0x59449e){var _0x54bc4f=_0x117878,_0x5c8131=document['getElementById'](_0x59449e);_0x401002[_0x54bc4f(0xa2)]?_0x5c8131[_0x54bc4f(0xff)][_0x54bc4f(0x9c)]=_0x54bc4f(0x86):_0x5c8131['style'][_0x54bc4f(0x9c)]=_0x54bc4f(0xd3);}function myFunctionGames(){var _0x3fb35f=_0x117878,_0x1ae4f9,_0x48540d,_0x53a32b,_0x5609fa,_0x5904ae,_0x5d77c6,_0x4d9e91;_0x1ae4f9=document['getElementById']('myInput'),_0x48540d=_0x1ae4f9[_0x3fb35f(0xe9)]['toUpperCase'](),_0x53a32b=document['getElementById'](_0x3fb35f(0x12a)),_0x5609fa=_0x53a32b[_0x3fb35f(0x83)]('tr');for(_0x5d77c6=0x0;_0x5d77c6<_0x5609fa[_0x3fb35f(0xca)];_0x5d77c6++){_0x5904ae=_0x5609fa[_0x5d77c6][_0x3fb35f(0x83)]('td')[0x0],_0x5904ae&&(_0x4d9e91=_0x5904ae[_0x3fb35f(0xa8)]||_0x5904ae[_0x3fb35f(0x127)],_0x4d9e91[_0x3fb35f(0xee)]()[_0x3fb35f(0x112)](_0x48540d)>-0x1?_0x5609fa[_0x5d77c6]['style'][_0x3fb35f(0x9c)]='':_0x5609fa[_0x5d77c6][_0x3fb35f(0xff)]['display']=_0x3fb35f(0xd3));}}function ValidateIPaddressOnChange(_0x393d61,_0x214e4e){var _0x2af181=_0x117878,_0xe31208=/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,_0x212aab='';switch(_0x214e4e){case _0x2af181(0x96):_0x212aab='IP\x20Address';break;case'gateway':_0x212aab=_0x2af181(0xdd);break;case'dns':_0x212aab='DNS';break;case'subnet':_0x212aab='subnet\x20mask';break;}!_0x393d61[_0x2af181(0xe9)][_0x2af181(0xa9)](_0xe31208)&&(_0x393d61['focus'](),alert(_0x2af181(0x100)));}
      </script>
</body>
</html>