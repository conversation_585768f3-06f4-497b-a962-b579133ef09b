<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Use Mikrotik as Fail2ban firewall - MikroTik Script RouterOS</title>
<meta content='Use Mikrotik as Fail2ban firewall - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Use Mikrotik as Fail2ban firewall - MikroTik Script RouterOS</h1>
<pre>
The Explanation
This tutorial is about how to configure Fail2ban to use Mikrotik as Firewall. Fail2ban is very halpfull application Its allows system administrators easily detect and prevent attack attempts. It's scaning log files (e.g. /var/log/auth.log) and bans IPs that show the malicious signs (too many password failures, seeking for exploits, etc..). By default Fail2ban using IPTables as firewall software but today I will show you how to configure system to put all firewall rules in one place.

P.S Fail2Ban comes with filters for various services (apache, curier, ssh, postfix, asterisk, etc).

OK lets start :-)

Preparing
Our first point must be generation SSH key for secure remote login

Note that RouterOS 2.9.13 and upper versions supporting SSH logins.

<code class="routeros">Note: New RouterOS versions v6 and up requires RSA keys</code>
Use this command to generate keys.

<code class="routeros">admin@linux:/$ ssh-keygen -t dsa

Generating public/private dsa key pair.
Enter file in which to save the key (/root/.ssh/id_dsa):
Enter passphrase (empty for no passphrase):
Enter same passphrase again:
Your identification has been saved in /root/.ssh/id_dsa.
Your public key has been saved in /root/.ssh/id_dsa.pub.
The key fingerprint is:
b8:ea:79:ad:61:c4:e0:1a:66:46:5b:0e:70:b6:aa:38 <EMAIL>
The key's randomart image is:
+--[ DSA 1024]----+
|. o        |
| + .        |
|  + o        |
| o * o .         |
|. * o + S    |
|o+ o . .         |
|E .   +.      |
| .   +...         |
|   .+...       |
+---------+</code>
DO NOT WRITE ANY PASSPHRASE. Now we need to upload and import id_dsa.pub key to mikrotik. File is located at /home/<USER>/.ssh/id_dsa.pub if you are using root account then /root/.ssh/id_dsa.pub

Configuration on Mikrotik side

<code class="routeros">[admin@mikrotik] > user add name=linux address=LINUX-SERVER-IP-ADDRESS group=full</code>
This command will add a user without password with full permissions login allowed from only your linux machine.

<code class="routeros">[admin@mikrotik]> user ssh-keys import public-key-file=id_dsa.pub user=linux</code>
This command will import your uploaded id_dsa public key to key mikrotik store.

Configuration on Linux side
On Linux side we must create a file named mikrotik to /usr/bin/ dir.

<code class="routeros">touch /usr/bin/mikrotik</code>
and put this bash script into this file.

<code class="routeros">#!/bin/bash
ssh -l linux -p22 -i /root/.ssh/id_dsa MIKROTIK-IP-ADDRESS "$1"</code>
OK.

<code class="routeros">now create a new file in /etc/fail2ban/action.d/ directory </code>
with name mikrotik.conf

<code class="routeros">nano /etc/fail2ban/action.d/mikrotik.conf</code> 
and put this text to that file.

<code class="routeros"># Fail2Ban configuration file
#
# Author: Ludwig Markosyan
# Release 09/02/2013
#
# $Version: 1.0 BETA $
#

[Definition]

# Option:  actionstart
# Notes.:  command executed once at the start of Fail2Ban.
# Values:  CMD
#
actionstart =


# Option:  actionstop
# Notes.:  command executed once at the end of Fail2Ban
# Values:  CMD
#
actionstop =


# Option:  actioncheck
# Notes.:  command executed once before each actionban command
# Values:  CMD
#
actioncheck =


# Option:  actionban
# Notes.:  command executed when banning an IP. Take care that the
#          command is executed with Fail2Ban user rights.
# Tags:    <ip>  IP address
#          <failures>  number of failures
#          <time>  unix timestamp of the ban time
# Values:  CMD
#
actionban = mikrotik ":ip firewall filter add action=drop chain=forward dst-address=<ip> comment=AutoFail2ban-<ip>"


# Option:  actionunban
# Notes.:  command executed when unbanning an IP. Take care that the
#          command is executed with Fail2Ban user rights.
# Tags:    <ip>  IP address
#          <failures>  number of failures
#          <time>  unix timestamp of the ban time
# Values:  CMD
#

actionunban =  mikrotik ":ip firewall filter remove [:ip firewall filter find comment=AutoFail2ban-<ip>]"</code>

Note: Instead of adding drop rule for each IP, you could use single drop rule and address list to save CPU resources

OK now we configured ban and unban actions

Then we must edit jail.conf file to tell Fail2ban to use mikrotik as ban action.

<code class="routeros">nano /etc/fail2ban/jail.conf</code>
I will show you example for ASTERISK jail you can use any other as you want.

<code class="routeros">[ASTERISK]
enabled  = true
filter   = asterisk
action = mikrotik
          sendmail-whois[name=ASTERISK, dest=<EMAIL>, sender=<EMAIL>]
logpath  = /var/log/asterisk/full
maxretry = 10
bantime = 3600</code>
OK It's all. I'm opened to listen any questions and remarks about this script. you can write me at "<EMAIL>'

Thanks for your interest.

Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
