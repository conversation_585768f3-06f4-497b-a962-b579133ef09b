<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Block Download Based Download Sizes - MikroTik Script RouterOS</title>
<meta content='Block Download Based Download Sizes - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Block Download Based Download Sizes - MikroTik Script RouterOS</h1>
<pre>In This Articles, i want describe, how to block download more than 10 MB per Bytes.

Everybody Want To Download Big Size Files, After Downloaded 10 MB, Download Would Stop.

in This Example, i Want block download per Source And Destination.

For Example, Everybody can download file from one server, and he want open a new session with a new server for other works.

for this reason, we sign Source And Destinations with Srd Address List And Dst Address List, And Then Download More Than 10 MB stop for These Addresses.

We Need Three Rules.

Rule 1: Match And Assign Source Of Download To A New Address List. 
Rule 2: Match And Assign Destination of Download To A new Address list. 
Rule 3: Block Every Session ( Download ), That Size Is More That 10 MB.


<code class="routeros">/ip firewall filter
add action=add-src-to-address-list address-list=Src address-list-timeout=1h \
chain=forward connection-bytes=1970000-0 disabled=no protocol=tcp \
src-address=***********/24

add action=add-dst-to-address-list address-list=Dst address-list-timeout=1h \
chain=forward connection-bytes=1970000-0 disabled=no protocol=tcp \
src-address=***********/24

add action=drop chain=forward disabled=no dst-address-list=Dst protocol=tcp \
src-address-list=Src</code>

In My Example, I Assign Every Users In ***********/24 Subnet, They have more that 10 MB size to Download After that, in Last Rule, I Block That Session To block Download for 1 Hour.

If users want to download 40 MB file, every 10MB file downloaded, they need wait to 1 Hours, Also You can Change Connection Bytes Value And Address list Time out Value.

Also You can start this strategy base on File Extensions, Such as ( mp3,avi,flv,zip, ... )

Reza Moghadam

--MikroTik Certified Trainer 16:14, 12 April 2013 (UTC)

Credit: Mikrotik.com
</pre>
<br>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

