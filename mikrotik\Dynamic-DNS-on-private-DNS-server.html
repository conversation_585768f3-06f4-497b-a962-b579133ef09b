<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Dynamic DNS on private DNS server (Router OS, Bind, Apache, and Shell script) - MikroTik Script RouterOS</title>
<meta content='Dynamic DNS on private DNS server (Router OS, Bind, Apache, and Shell script) - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>

<h1>Dynamic DNS on private DNS server (Router OS, Bind, Apache, and Shell script) - MikroTik Script RouterOS</h1>
<pre>Original and updates here: [eurekamoment.eu] If you constantly connect to your home network (as I do), you have, on occasion, experienced that frustrating moment when the dynamic dns service is down for whatever reason. Let's assume that you are a person who likes to have more controll over the services you use, and have setup your private DNS server on a cheap VPS like [DigitalOcean]. If so, you're at the right place.

For this to work, we will need a smarter router that can fetch http data - Mikrotik in my case, and a private DNS server on the public network hosting your domain and under your control. If you can't access the shell, then you can't complete this tutorial.

Note that this isn't very secure and I will be modifying the process to get a secure solution

Setup

1.Setup a web server on your DNS server

2.Create a php update page

<code class="routeros">&lt;?php
  $ip=$_SERVER[REMOTE_ADDR];
  if ($_GET[&#039;hash&#039;]=&quot;ae2b1fca515949e5d54fb22b8ed95575&quot;) {
    file_put_contents(&quot;deviceupdate.log&quot;,&quot;DATE: &quot;.date(&quot;Y-m-d H:i:s&quot;).&quot; IP: &quot;.$ip.&quot;n&quot;,FILE_APPEND | LOCK_EX);
    file_put_contents(&quot;homeip.log&quot;,$ip,LOCK_EX);
    print &quot;DATE: &quot;.date(&quot;Y-m-d H:i:s&quot;).&quot; IP: &quot;.$ip;
  }
?&gt;</code>
3.Setup Mikrotik schetuler that runs every few minutes

<code class="routeros"> /tool fetch keep-result=no mode=http url="http://www.my-dns-servers-web-server.com/mikrotik/deviceupdate.php?hash=ae2b1fca515949e5d54fb22b8ed95575"</code>
4.Create a shell script called dyndns.sh to update the DNS zone

<code class="routeros"> #!/bin/bash
newip=$(cat /var/www/homeip.log)
md5old=$(cat /var/www/homeip.md5)
md5new=$(md5sum /var/www/homeip.log)

#echo "$(date) RunTime" >> /var/log/dyndns.log
if test "$md5old" = "$md5new"
then
        echo "$(date) - No change" >> /var/log/dyndns.log
else
        sed -i "s/^home.*/home IN A $newip/" /etc/bind/db.somedomain.com
        rndc reload somedomain.com
        md5sum /var/www/homeip.log > /var/www/homeip.md5
        echo "$(date) - Updated ip $newip" >> /var/log/dyndns.log
fi</code>
5.Create a cron job to run every few minutes. Run crontab -e and type

<code class="routeros"> */5 * * * * /scripts/dyndns.sh</code>
<b>How it works</b>

1. The router tries to access the specific web page on the web server that is located on the DNS server.
2. Web server reads the IP from the router and if the hash is ok, it writes the IP into two files in the same folder
   1. One file for history purposes (with date and everything)
   2. One file with IP address only
3. Cron runs a script that calculates the MD5 hash from the file containing only the IP address and compares it to the MD5 hash from before the change. If the two are different - it updates the zone file by replacing the one record and saves the new hash to a file for future comparison (so we don't update and reload the zone all the time
4. Zone is reloaded and propagated.

Credit: https://wiki.mikrotik.com/wiki/Dynamic_DNS_on_private_DNS_server_(Router_OS,_Bind,_Apache,_and_Shell_script)
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
