<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Super Mario Bross Theme Sound Beep - MikroTik Script RouterOS</title>
<meta content='Super Mario Bross Theme Sound Beep - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Super Mario Bross Theme Sound Beep - MikroTik Script RouterOS</h1>
<pre>This Super Mario Bross Theme Sound Beep script was made without calculating the actual notes (made off the top of my head without listening to the song recently), so it may sound a bit off on a few notes. The next version will have the fully calculated frequencies. I, Ian Harker, wrote this script May 13th and May 14th of 2008 during my spare time at the Chicago, IL MikroTik Essentials Training. The best way to add this script is to just copy/paste the code below into a new script (System -> Scripts -> +), give it a name, and then click "Apply":

<code class="routeros">:beep frequency=660 length=100ms;
:delay 150ms;
:beep frequency=660 length=100ms;
:delay 300ms;
:beep frequency=660 length=100ms;
:delay 300ms;
:beep frequency=510 length=100ms;
:delay 100ms;
:beep frequency=660 length=100ms;
:delay 300ms;
:beep frequency=770 length=100ms;
:delay 550ms;
:beep frequency=380 length=100ms;
:delay 575ms;

:beep frequency=510 length=100ms;
:delay 450ms;
:beep frequency=380 length=100ms;
:delay 400ms;
:beep frequency=320 length=100ms;
:delay 500ms;
:beep frequency=440 length=100ms;
:delay 300ms;
:beep frequency=480 length=80ms;
:delay 330ms;
:beep frequency=450 length=100ms;
:delay 150ms;
:beep frequency=430 length=100ms;
:delay 300ms;
:beep frequency=380 length=100ms;
:delay 200ms;
:beep frequency=660 length=80ms;
:delay 200ms;
:beep frequency=760 length=50ms;
:delay 150ms;
:beep frequency=860 length=100ms;
:delay 300ms;
:beep frequency=700 length=80ms;
:delay 150ms;
:beep frequency=760 length=50ms;
:delay 350ms;
:beep frequency=660 length=80ms;
:delay 300ms;
:beep frequency=520 length=80ms;
:delay 150ms;
:beep frequency=580 length=80ms;
:delay 150ms;
:beep frequency=480 length=80ms;
:delay 500ms;

:beep frequency=510 length=100ms;
:delay 450ms;
:beep frequency=380 length=100ms;
:delay 400ms;
:beep frequency=320 length=100ms;
:delay 500ms;
:beep frequency=440 length=100ms;
:delay 300ms;
:beep frequency=480 length=80ms;
:delay 330ms;
:beep frequency=450 length=100ms;
:delay 150ms;
:beep frequency=430 length=100ms;
:delay 300ms;
:beep frequency=380 length=100ms;
:delay 200ms;
:beep frequency=660 length=80ms;
:delay 200ms;
:beep frequency=760 length=50ms;
:delay 150ms;
:beep frequency=860 length=100ms;
:delay 300ms;
:beep frequency=700 length=80ms;
:delay 150ms;
:beep frequency=760 length=50ms;
:delay 350ms;
:beep frequency=660 length=80ms;
:delay 300ms;
:beep frequency=520 length=80ms;
:delay 150ms;
:beep frequency=580 length=80ms;
:delay 150ms;
:beep frequency=480 length=80ms;
:delay 500ms;

:beep frequency=500 length=100ms;
:delay 300ms;

:beep frequency=760 length=100ms;
:delay 100ms;
:beep frequency=720 length=100ms;
:delay 150ms;
:beep frequency=680 length=100ms;
:delay 150ms;
:beep frequency=620 length=150ms;
:delay 300ms;

:beep frequency=650 length=150ms;
:delay 300ms;
:beep frequency=380 length=100ms;
:delay 150ms;
:beep frequency=430 length=100ms;
:delay 150ms;

:beep frequency=500 length=100ms;
:delay 300ms;
:beep frequency=430 length=100ms;
:delay 150ms;
:beep frequency=500 length=100ms;
:delay 100ms;
:beep frequency=570 length=100ms;
:delay 220ms;

:beep frequency=500 length=100ms;
:delay 300ms;

:beep frequency=760 length=100ms;
:delay 100ms;
:beep frequency=720 length=100ms;
:delay 150ms;
:beep frequency=680 length=100ms;
:delay 150ms;
:beep frequency=620 length=150ms;
:delay 300ms;

:beep frequency=650 length=200ms;
:delay 300ms;

:beep frequency=1020 length=80ms;
:delay 300ms;
:beep frequency=1020 length=80ms;
:delay 150ms;
:beep frequency=1020 length=80ms;
:delay 300ms;

:beep frequency=380 length=100ms;
:delay 300ms;
:beep frequency=500 length=100ms;
:delay 300ms;

:beep frequency=760 length=100ms;
:delay 100ms;
:beep frequency=720 length=100ms;
:delay 150ms;
:beep frequency=680 length=100ms;
:delay 150ms;
:beep frequency=620 length=150ms;
:delay 300ms;

:beep frequency=650 length=150ms;
:delay 300ms;
:beep frequency=380 length=100ms;
:delay 150ms;
:beep frequency=430 length=100ms;
:delay 150ms;

:beep frequency=500 length=100ms;
:delay 300ms;
:beep frequency=430 length=100ms;
:delay 150ms;
:beep frequency=500 length=100ms;
:delay 100ms;
:beep frequency=570 length=100ms;
:delay 420ms;

:beep frequency=585 length=100ms;
:delay 450ms;

:beep frequency=550 length=100ms;
:delay 420ms;

:beep frequency=500 length=100ms;
:delay 360ms;

:beep frequency=380 length=100ms;
:delay 300ms;
:beep frequency=500 length=100ms;
:delay 300ms;
:beep frequency=500 length=100ms;
:delay 150ms;
:beep frequency=500 length=100ms;
:delay 300ms;

:beep frequency=500 length=100ms;
:delay 300ms;

:beep frequency=760 length=100ms;
:delay 100ms;
:beep frequency=720 length=100ms;
:delay 150ms;
:beep frequency=680 length=100ms;
:delay 150ms;
:beep frequency=620 length=150ms;
:delay 300ms;

:beep frequency=650 length=150ms;
:delay 300ms;
:beep frequency=380 length=100ms;
:delay 150ms;
:beep frequency=430 length=100ms;
:delay 150ms;

:beep frequency=500 length=100ms;
:delay 300ms;
:beep frequency=430 length=100ms;
:delay 150ms;
:beep frequency=500 length=100ms;
:delay 100ms;
:beep frequency=570 length=100ms;
:delay 220ms;

:beep frequency=500 length=100ms;
:delay 300ms;

:beep frequency=760 length=100ms;
:delay 100ms;
:beep frequency=720 length=100ms;
:delay 150ms;
:beep frequency=680 length=100ms;
:delay 150ms;
:beep frequency=620 length=150ms;
:delay 300ms;

:beep frequency=650 length=200ms;
:delay 300ms;

:beep frequency=1020 length=80ms;
:delay 300ms;
:beep frequency=1020 length=80ms;
:delay 150ms;
:beep frequency=1020 length=80ms;
:delay 300ms;

:beep frequency=380 length=100ms;
:delay 300ms;
:beep frequency=500 length=100ms;
:delay 300ms;

:beep frequency=760 length=100ms;
:delay 100ms;
:beep frequency=720 length=100ms;
:delay 150ms;
:beep frequency=680 length=100ms;
:delay 150ms;
:beep frequency=620 length=150ms;
:delay 300ms;

:beep frequency=650 length=150ms;
:delay 300ms;
:beep frequency=380 length=100ms;
:delay 150ms;
:beep frequency=430 length=100ms;
:delay 150ms;

:beep frequency=500 length=100ms;
:delay 300ms;
:beep frequency=430 length=100ms;
:delay 150ms;
:beep frequency=500 length=100ms;
:delay 100ms;
:beep frequency=570 length=100ms;
:delay 420ms;

:beep frequency=585 length=100ms;
:delay 450ms;

:beep frequency=550 length=100ms;
:delay 420ms;

:beep frequency=500 length=100ms;
:delay 360ms;

:beep frequency=380 length=100ms;
:delay 300ms;
:beep frequency=500 length=100ms;
:delay 300ms;
:beep frequency=500 length=100ms;
:delay 150ms;
:beep frequency=500 length=100ms;
:delay 300ms;

:beep frequency=500 length=60ms;
:delay 150ms;
:beep frequency=500 length=80ms;
:delay 300ms;
:beep frequency=500 length=60ms;
:delay 350ms;
:beep frequency=500 length=80ms;
:delay 150ms;
:beep frequency=580 length=80ms;
:delay 350ms;
:beep frequency=660 length=80ms;
:delay 150ms;
:beep frequency=500 length=80ms;
:delay 300ms;
:beep frequency=430 length=80ms;
:delay 150ms;
:beep frequency=380 length=80ms;
:delay 600ms;

:beep frequency=500 length=60ms;
:delay 150ms;
:beep frequency=500 length=80ms;
:delay 300ms;
:beep frequency=500 length=60ms;
:delay 350ms;
:beep frequency=500 length=80ms;
:delay 150ms;
:beep frequency=580 length=80ms;
:delay 150ms;
:beep frequency=660 length=80ms;
:delay 550ms;

:beep frequency=870 length=80ms;
:delay 325ms;
:beep frequency=760 length=80ms;
:delay 600ms;

:beep frequency=500 length=60ms;
:delay 150ms;
:beep frequency=500 length=80ms;
:delay 300ms;
:beep frequency=500 length=60ms;
:delay 350ms;
:beep frequency=500 length=80ms;
:delay 150ms;
:beep frequency=580 length=80ms;
:delay 350ms;
:beep frequency=660 length=80ms;
:delay 150ms;
:beep frequency=500 length=80ms;
:delay 300ms;
:beep frequency=430 length=80ms;
:delay 150ms;
:beep frequency=380 length=80ms;
:delay 600ms;

:beep frequency=660 length=100ms;
:delay 150ms;
:beep frequency=660 length=100ms;
:delay 300ms;
:beep frequency=660 length=100ms;
:delay 300ms;
:beep frequency=510 length=100ms;
:delay 100ms;
:beep frequency=660 length=100ms;
:delay 300ms;
:beep frequency=770 length=100ms;
:delay 550ms;
:beep frequency=380 length=100ms;
:delay 575ms; </code>
Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
