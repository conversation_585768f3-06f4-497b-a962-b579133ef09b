<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Zoom Meeting Mangle and Queue - MikroTik Script RouterOS</title>
<meta content='Zoom Meeting Mangle and Queue - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Zoom Meeting Mangle and Queue - MikroTik Script RouterOS</h1>
<pre>
There are various media used to hold meetings or video conferences, one of the most widely used is Zoom Meeting. In this article, we will try to optimize the network by prioritizing the connection used by video conferencing so that it can be used properly without any interference. The bandwidth used will be prioritized so as not to be disturbed when other clients browse the internet.

<code class="routeros">/ip firewall filter 
add action=add-dst-to-address-list address-list=zoom-ip address-list-timeout=1d chain=forward dst-address-list=!LOCAL-IP protocol=tcp dst-port=3478,3479,5090,5091,8801-8810 comment="ZOOM"
add action=add-dst-to-address-list address-list=zoom-ip address-list-timeout=1d chain=forward dst-address-list=!LOCAL-IP protocol=udp dst-port=3478,3479,5090,5091,8801-8810

/ip firewall mangle
add action=mark-connection chain=prerouting dst-address-list=zoom-ip new-connection-mark="conn-zoom" passthrough=yes comment="ZOOM"
add action=mark-packet chain=forward connection-mark="conn-zoom" new-packet-mark=zoom-pkt passthrough=no src-address-list=LOCAL-IP
add action=mark-packet chain=forward connection-mark="conn-zoom" new-packet-mark=zoom-pkt passthrough=no dst-address-list=LOCAL-IP

/queue simple 
add name="Zoom" packet-marks=zoom-pkt target=10.0.0.0/8,***********/16,**********/12 comment="ZOOM"

/ip firewall address-list
add address=10.0.0.0/8 list=LOCAL-IP
add address=**********/12 list=LOCAL-IP
add address=***********/16 list=LOCAL-IP</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>


    

