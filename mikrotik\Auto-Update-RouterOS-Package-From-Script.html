<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Auto Update RouterOS Package From Script - MikroTik Script RouterOS</title>
<meta content='Auto Update RouterOS Package From Script - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Auto Update RouterOS Package From Script - MikroTik Script RouterOS</h1>
<pre>If you administer only a few MikroTik devices, you can manually update them one by one using the WinBox tool. This is a very easy task if you install only the long-term versions. That version is rarely updated and contains only the major bug fixes.

In case that you have larger number of devices under your control, you will probably want more automation. The good news is that you can use the magic of the command line to perform such task. This means that you can also write the update script.

Contrary to the scenario with your local update source point, if your routers are independent to each other and on the separate Internet links, you will want to use the different process to update them all at once.

<code class="routeros"># Simple RouterOS upgrade script
# version 1 (09.03.2020)
# written by Srdjan Stanisic 
# go to the update context
/system package update 
# set the channel
set channel=long-term
# check for updates
check-for-updates
# we can log the status
:log info "$[get status]"
#
# check for the new version
# if there is a newer version, then download it and reboot the router
# you can omit automatic reboot and send email instead
# additionally, we can omit the ELSE part
# 
:if (installed-version != latest-version) do={
    download;
} else={
    :put "System is already up to date"
}</code>
This script is ready to be used. Copy it into the text file and save it with .rsc extension

CREDIT: <a href='https://mivilisnet.wordpress.com/2020/03/31/updating-mikrotik-router-from-the-command-line/'>https://mivilisnet.wordpress.com/2020/03/31/updating-mikrotik-router-from-the-command-line/</a>
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

