<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Complete Bandwidth Monitoring - MikroTik Script RouterOS</title>
<meta content='Complete Bandwidth Monitoring - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Complete Bandwidth Monitoring - MikroTik Script RouterOS</h1>
<pre>
Complete Bandwidth Monitoring 

This Bandwidth monitoring script is 100% accurate, maybe as accurate as Cacti Monitoring, actually I originally planned to use it for almost FUP Quota Indihome, but I didn't understand what kind of algorithm the telecom system uses, so the results are always different, maybe this is related to streaming that is not included in the FUP system they.

Please paste this script in the proxy terminal

<code class="routeros">/system scheduler add interval="00:00:00" name="RXByte.log" on-event="1" start-time="00:00:00"
/system scheduler add interval="00:00:00" name="RXByteCur.log" on-event="1" start-time="00:00:00"
/system scheduler add interval="00:00:00" name="TXByte.log" on-event="1" start-time="00:00:00"
/system scheduler add interval="00:00:00" name="TXByteCur.log" on-event="1" start-time="00:00:00"
/system scheduler add interval="00:01:00" name="RESET-RXTX" start-time=startup
/system scheduler add interval="00:0:030" name="BANDWIDTH-MONITORING" start-time=startup</code>

Copy Paste this monthly reset script in the RESET-RXTX Scheduler file

<code class="routeros">################################################################
 
# SCRIPT MIKROTIK BANDWIDTH MONITORING
# Version 1.0
################################################################
# Function for Monthly reset Every 1st
################################################################
:local varDate;
:local varDay;
:set varDate [/system clock get date];
:set varDay [:pick $varDate 4 6];
:if ($varDay = "01") do={ 
# if today is the 1st reset the RXTX to its initial value
/system scheduler set RXByte.log comment="1" on-event="1"
/system scheduler set RXByteCur.log comment="1" on-event="1"
/system scheduler set TXByte.log comment="1" on-event=$RXByteCount
/system scheduler set TXByteCur.log comment="1" on-event="1"
/system scheduler disable [/system scheduler find name="RESET-RXTX"]
}
################################################################</code>
Copy Paste this Monitoring Script in the BANDWIDTH-MONITORING Scheduler file

<code class="routeros">################################################################
 
# SCRIPT MIKROTIK BANDWIDTH MONITORING
# Version 1.0
################################################################
:local INTMon WAN-WARNET;
# silahkan ganti dengan interface (ether) yang ingin dipantau
################################################################
:local TOTQuota 500;
# Set total quota dalam GB misalkan ISP hanya memberikan hanya 500GB
################################################################
:local RXByteCur [/interface get $INTMon rx-byte];
# Mengambil nilai RX-Byte saat ini pada interface terpilih
################################################################
:local RXByteCount [/system scheduler get RXByteCur.log on-event];
# Mengambil nilai RX-Byte dalam file log RXByteCur
################################################################
:local RXByte [/system scheduler get RXByte.log on-event];
# Mengambil nilai RX-Byte sebelumnya dalam file log RXByte
################################################################
:local TXByteCur [/interface get $INTMon tx-byte];
# Mengambil nilai TX-Byte saat ini pada interface terpilih
################################################################
:local TXByteCount [/system scheduler get TXByteCur.log on-event];
# Mengambil nilai TX-Byte saat ini dalam file log TXByteCur
################################################################
:local TXByte [/system scheduler get TXByte.log on-event];
# Mengambil nilai TX-Byte saat ini dalam file Log TXByte
################################################################
:local ifReboot 0;
# kita perlu mengetahui apakah router reboot  
################################################################
:if ($RXByteCur>=$RXByteCount) do={} else={:set $ifReboot ($ifReboot+1);}
:if ($TXByteCur>=$TXByteCount) do={} else={:set $ifReboot ($ifReboot+1);}
# Tandai jika nilai RXTX-Byte saat ini lebih besar dari RXTX-Byte pada log
################################################################
:if ($ifReboot>=1) do={
# Cek Jika Router Reboot
################################################################
:set $RXByte ($RXByte+$RXByteCount);
/system scheduler set RXByte.log comment=$RXByte on-event=$RXByte
# jika komputer reboot jumlahkan total RX-Byte
################################################################
:set $TXByte ($TXByte+$TXByteCount);
/system scheduler set TXByte.log comment=$TXByte on-event=$TXByte
} else={
# jika komputer reboot jumlahkan total TX-Byte
################################################################
}
:set RXByteCount ($RXByteCur);
/system scheduler set RXByteCur.log comment=$RXByteCount on-event=$RXByteCount
# Perbaharui nilai RX-Byte saat ini pada file log RXByteCur
################################################################
:set TXByteCount ($TXByteCur);
/system scheduler set TXByteCur.log comment=$TXByteCount on-event=$TXByteCount
# Perbaharui nilai TX-Byte saat ini pada file log TXByteCur
################################################################
:local RXTot ($RXByte+$RXByteCur);
:local RXMB ($RXTot / 1024 / 1024);
:local RXGB ($RXTot  / 1024 / 1024 / 1024);
# kalkulasi nilai RX-BYTE dalam MB dan GB
################################################################
:local TXTot ($TXByte+$TXByteCur);
:local TXMB ($TXTot / 1024 / 1024);
:local TXGB ($TXTot / 1024 / 1024 / 1024);
# kalkulasi nilai TX-BYTE dalam MB dan GB
################################################################
:local RXTX ($RXTot+$TXTot);
:local RXTXMB ($RXMB+$TXMB);
:local RXTXGB ($RXGB+$TXGB);
# Total kalkulasi nilai Total RXTX
################################################################
:log warning "###############################################";
:log warning "BANDWIDTH MONITORING [ Router Identity: $[/system identity get name] ]";
:log warning "###############################################";
:log warning "Interface Monitoring For: $INTMon";
/interface monitor-traffic [/interface find name=$INTMon] once do={
:local tx (tx-bits-per-second / 1024);
:local rx (rx-bits-per-second / 1024);
:log warning "Live Monitor RX = $rx kbps / TX = $tx kbps";
}
# hanya untuk menampilkan rxtx saat ini
###############################################################
:log warning "Total RX = $RXGB GB / $RXMB MB / $RXTot Bytes";
:log warning "Total TX = $TXGB GB / $TXMB MB / $TXTot Bytes";
:log warning "Total (RX+TX) = $RXTXGB GB / $RXTXMB MB / $RXTX Bytes";
:local percent ($RXTXGB*100 / $TOTQuota);
:log error "Used Quota On This Month = $RXTXGB GB = $percent% from $TOTQuota GB";
:log warning "###############################################";
# Tampilkan Info pada LOG Mikrotik 
################################################################
:local varDate;
:local varDay;
:set varDate [/system clock get date];
:set varDay [:pick $varDate 4 6];
:if ($varDay = "29") do={ 
# jika hari ini tanggal 29 aktifkan RESET-RXTX
/system scheduler enable [/system scheduler find name="RESET-RXTX"];
}
################################################################</code>
don't forget to change the ether name in the script according to the interface you want to monitor 

<code class="routeros">:local INTMon WAN-WARNET;</code>
Don't forget to change the total quota given by the ISP for example, is 500GB

<code class="routeros">:local TOTQuota 500;</code>

<h2>What if we want to monitor bandwidth monitoring and display Monitoring View in the log so that each can be set the time?</h2>

<code class="routeros">/system scheduler add interval = "00: 0: 030" name = "VIEW-LOG" start-time = startup</code>

Then copy and paste the script below into the VIEW-LOG scheduler file 

<code class="routeros">################################################################
 
# SCRIPT MIKROTIK BANDWIDTH MONITORING
# Version 1.0
################################################################
:local INTMon WAN-WARNET;
# silahkan ganti dengan interface (ether) yang ingin dipantau
:local TOTQuota 500;
# Set total quota dalam GB misalkan ISP hanya memberi hanya 500GB
################################################################
:local RXByteCur [/interface get $INTMon rx-byte];
# Mengambil nilai RX-Byte saat ini pada interface terpilih
:local RXByte [/system scheduler get RXByte.log on-event];
# Mengambil nilai RX-Byte sebelumnya dalam file log RXByte
################################################################
:local TXByteCur [/interface get $INTMon tx-byte];
# Mengambil nilai TX-Byte saat ini pada interface terpilih
:local TXByte [/system scheduler get TXByte.log on-event];
# Mengambil nilai TX-Byte saat ini dalam file Log TXByte
################################################################
:local RXTot ($RXByte+$RXByteCur);
:local RXMB ($RXTot / 1024 / 1024);
:local RXGB ($RXTot  / 1024 / 1024 / 1024);
# kalkulasi nilai RX-BYTE dalam MB dan GB
################################################################
:local TXTot ($TXByte+$TXByteCur);
:local TXMB ($TXTot / 1024 / 1024);
:local TXGB ($TXTot / 1024 / 1024 / 1024);
# kalkulasi nilai TX-BYTE dalam MB dan GB
################################################################
:local RXTX ($RXTot+$TXTot);
:local RXTXMB ($RXMB+$TXMB);
:local RXTXGB ($RXGB+$TXGB);
# Total kalkulasi nilai Total RXTX
################################################################
:log warning "###############################################";
:log warning "BANDWIDTH MONITORING [ Router: $[/system identity get name] ]";
:log warning "###############################################";
:log warning "Interface Monitoring For Ether: $INTMon";
/interface monitor-traffic [/interface find name=$INTMon] once do={
:local tx (tx-bits-per-second / 1024);
:local rx (rx-bits-per-second / 1024);
:log warning "Live Monitor RX = $rx kbps TX = $tx kbps";
}
# hanya untuk menampilkan rxtx saat ini
###############################################################
:log warning "Total RX = $RXGB GB / $RXMB MB / $RXTot Bytes";
:log warning "Total TX = $TXGB GB / $TXMB MB / $TXTot Bytes";
:log warning "Total (RX+TX) = $RXTXGB GB / $RXTXMB MB / $RXTX Bytes";
:local percent ($RXTXGB*100 / $TOTQuota);
:log error "Used Quota on This Month = $RXTXGB GB = $percent% from $TOTQuota GB";
:log warning "###############################################";
# Tampilkan Info pada LOG Mikrotik 
################################################################
:local varDate;
:local varDay;
:set varDate [/system clock get date];
:set varDay [:pick $varDate 4 6];
:if ($varDay = "29") do={ 
# jika har ini tanggal 29 aktfikan RESET-RXTX
/system scheduler enable [/system scheduler find name="RESET-RXTX"];
}
################################################################</code>

and finally replace the contents of the BANDWIDTH-MONITORING Scheduler file with the script below

<code class="routeros">################################################################
 
# SCRIPT MIKROTIK BANDWIDTH MONITORING
# Version 1.0
################################################################
:local INTMon WAN-WARNET;
# silahkan ganti dengan interface (ether) yang ingin dipantau
:local RXByteCur [/interface get $INTMon rx-byte];
# Mengambil nilai RX-Byte saat ini pada interface terpilih
################################################################
:local RXByteCount [/system scheduler get RXByteCur.log on-event];
# Mengambil nilai RX-byte dalam file log RXByteCur
################################################################
:local RXByte [/system scheduler get RXByte.log on-event];
# Mengambil nilai RX-Byte sebelumnya dalam file log RXByte
################################################################
:local TXByteCur [/interface get $INTMon tx-byte];
# Mengambil nilai TX-Byte saat ini pada interface terpilih
################################################################
:local TXByteCount [/system scheduler get TXByteCur.log on-event];
# Mengambil nilai TX-Byte saat ini dalam file log TXByteCur
################################################################
:local TXByte [/system scheduler get TXByte.log on-event];
# Mengambil nilai TX-Byte saat ini dalam file Log TXByte
################################################################
:local ifReboot 0;
# kita perlu mengetahui apakah router reboot dengan memberi flag 0 
################################################################
:if ($RXByteCur>=$RXByteCount) do={} else={:set $ifReboot ($ifReboot+1);}
:if ($TXByteCur>=$TXByteCount) do={} else={:set $ifReboot ($ifReboot+1);}
# Tandai jika nilai RXTX-Byte saat ini lebih besar dari RXTX-Byte pada log
################################################################
:if ($ifReboot>=1) do={
# Cek Jika Router Rebbot
################################################################
:set $RXByte ($RXByte+$RXByteCount);
/system scheduler set RXByte.log comment=$RXByte on-event=$RXByte
# jika komputer reboot jumlahkan total RX-Byte
################################################################
:set $TXByte ($TXByte+$TXByteCount);
/system scheduler set TXByte.log comment=$TXByte on-event=$TXByte
} else={
# jika komputer reboot jumlahkan total TX-Byte
################################################################
}
:set RXByteCount ($RXByteCur);
/system scheduler set RXByteCur.log comment=$RXByteCount on-event=$RXByteCount
# Perbaharui nilai RX-Byte saat ini pada file log RXByteCur
################################################################
:set TXByteCount ($TXByteCur);
/system scheduler set TXByteCur.log comment=$TXByteCount on-event=$TXByteCount
# Perbaharui nilai TX-Byte saat ini pada file log TXByteCur
################################################################</code>

Now we can change each time between the monitoring and the View log so they don't run together

<h2>If you want to display monitoring info in the system note terminal</h2>

First add a scheduler named VIEW-LOG-NOTE

<code class="routeros">/system scheduler add interval="01:00:000" name="VIEW-LOG-NOTE" start-time=startup</code>

Then copy and paste the script below into the VIEW-LOG-NOTE scheduler file

<code class="routeros">################################################################
 
# SCRIPT MIKROTIK BANDWIDTH MONITORING
# Version 1.0
################################################################
:local INTMon WAN_WARNET;
# silahkan ganti dengan interface (ether) yang ingin dipantau
:local TOTQuota 500;
# Set total quota dalam GB misalkan ISP hanya memberi hanya 500GB
################################################################
:local RXByteCur [/interface get $INTMon rx-byte];
# Mengambil nilai RX-Byte saat ini pada interface terpilih
:local RXByte [/system scheduler get RXByte.log on-event];
# Mengambil nilai RX-Byte sebelumnya dalam file log RXByte
################################################################
:local TXByteCur [/interface get $INTMon tx-byte];
# Mengambil nilai TX-Byte saat ini pada interface terpilih
:local TXByte [/system scheduler get TXByte.log on-event];
# Mengambil nilai TX-Byte saat ini dalam file Log TXByte
################################################################
:local RXTot ($RXByte+$RXByteCur);
:local RXMB ($RXTot / 1024 / 1024);
:local RXGB ($RXTot  / 1024 / 1024 / 1024);
# kalkulasi nilai RX-BYTE dalam MB dan GB
################################################################
:local TXTot ($TXByte+$TXByteCur);
:local TXMB ($TXTot / 1024 / 1024);
:local TXGB ($TXTot / 1024 / 1024 / 1024);
# kalkulasi nilai TX-BYTE dalam MB dan GB
################################################################
:local RXTX ($RXTot+$TXTot);
:local RXTXMB ($RXMB+$TXMB);
:local RXTXGB ($RXGB+$TXGB);
# Total kalkulasi nilai Total RXTX
################################################################
:local percent ($RXTXGB*100 / $TOTQuota);
:local logcontenttemp "" 
:local logcontent "" 
:set logcontenttemp ""
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
:set logcontenttemp "#######################################################"
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
:set logcontenttemp "BANDWIDTH MONITORING [ Router Identity: $[/system identity get name] ]"
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
:set logcontenttemp "#######################################################"
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
:set logcontenttemp "Interface Monitoring For: $INTMon"
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
:set logcontenttemp "Total RX = $RXGB GB / $RXMB MB / $RXTot Bytes"
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
:set logcontenttemp "Total TX = $TXGB GB / $TXMB MB / $TXTot Bytes"
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
:set logcontenttemp "Total (RX+TX) = $RXTXGB GB / $RXTXMB MB / $RXTX Bytes"
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
:set logcontenttemp "Used Quota on This Month $RXTXGB GB = $percent% from $TOTQuota GB"
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
:set logcontenttemp "#######################################################"
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
:if ($RXTXGB >= $TOTQuota) do={
:set logcontenttemp "Your Quota is OVERLOAD :("
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
} else={
:set logcontenttemp "Your Quota is SAVE :)"
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
}
:set logcontenttemp ""
:set logcontent ("$logcontent" ."$logcontenttemp" ."")
/system note set note=$logcontent
# Show Info on the Mikrotik LOG
################################################################
:local varDate;
:local varDay;
:set varDate [/system clock get date];
:set varDay [:pick $varDate 4 6];
:if ($varDay = "29") do={ 
# if today is the 29th activate RESET-RXTX
/system scheduler enable [/system scheduler find name="RESET-RXTX"];
}</code>

good luck, good luck :)

Credit: <a href="https://www.o-om.com/2017/06/mikrotik-bandwidth-monitoring-script.html">https://www.o-om.com/2017/06/mikrotik-bandwidth-monitoring-script.html</a>
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

