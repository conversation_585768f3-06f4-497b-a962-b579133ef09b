<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Update Static DNS Entries Every 10 Minutes - MikroTik Script RouterOS</title>
<meta content='Update Static DNS Entries Every 10 Minutes - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Update Static DNS Entries Every 10 Minutes - MikroTik Script RouterOS</h1>
<pre>
Add a script named SMTP-Updater containting the following code:

<code class="routeros">:log info "Updating SMTP Address"

:local latestsmtp
:set latestsmtp [:resolve smtp.YOURUPSTREAMISP.com]
:log error "Latest SMTP Address is :$latestsmtp"
:local testingsmtp

/ip dns static
#:log info "Latest SMTP Server Address is: $latestsmtp"

# Starting Loop Below:

:foreach i 

# Add your local static DNS entries below:
in=smtp.itblanket.net,smtp.saix.net,smtp.afrihost.co.za,smtp.afrihost.com do={

:set testingsmtp $i

#:log info "Testing this SMTP: $testingsmtp"
:if ([:len [/ip dns static find name=$testingsmtp]] >0 ) do={

#:log info "Entry exists, updating DNS"
/ip dns static set [find name=$testingsmtp] address=$latestsmtp ttl=600 ;

} else={
#:log info "Entry does not exist, creating the entry"
/ip dns static add name=$testingsmtp address=$latestsmtp ttl=600 ;

};

}
#Loop ends above


#:log info "Latest SMTP: $latestsmtp"
:log info "Static SMTP DNS has been updated"</code>
Now schedule it to run eg every 10 minutes.

Regards, G

Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
