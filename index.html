<!DOCTYPE html>
<html lang="en">
<head>
<title>Free MikroTik RouterOS Online Tools - By mikrotiktool.Github.io</title>
<meta name="google-site-verification" content="google-site-verification=v9jfwyACp9axJqzwm1GlUgutNsmz-gLAg37rIqsnln0">
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta content='Free MikroTik RouterOS Online Tools, the most complete Router tools to make it easier for you maker RouterOS Mikrotik scripts!
By mikrotiktool.Github.io' name='description'/>
<meta content='tool, pcc, queue, vpn, setting mikrotik, mikrotik, router, winbox, termimal, rsc, script, hotspot, wirelessm rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<meta property="og:title" content="MikroTik Online Tools (Unofficial) ">
<meta property="og:description" content="MikroTik Online Tools (Unofficial) ">
<meta property="og:image:alt" content="MikroTik Online Tools (Unofficial) ">
<meta property="og:type" content="website">
<meta property="og:url" content="https://mikrotiktool.github.io">
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3815059393374366" crossorigin="anonymous"></script>
<style> 
body {
background: #8E2DE2;  /* fallback for old browsers */
background: -webkit-linear-gradient(to left, #8E2DE2, #4A00E0);  /* Chrome 10-25, Safari 5.1-6 */
background: linear-gradient(to left, #8E2DE2, #4A00E0 ); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
-ms-text-size-adjust: 100%;
-webkit-text-size-adjust:100%;
line-height:2.0;
font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;
font-size: 16px;
word-wrap: break-word;
}
h1, h2 {font-size: 25px; border-bottom:0px solid #ddd; margin-bottom:20px; color:#000;line-height:1.5}
h3, h4, h5, h6{font-size: 20px; border-bottom:1px solid #ddd; margin-bottom:10px; padding-botto:10px; color:#000;line-height:1.5}

pre {
white-space: -moz-pre-wrap; /* Mozilla, supported since 1999 */
white-space: -pre-wrap; /* Opera */
white-space: -o-pre-wrap; /* Opera */
white-space: pre-wrap; /* CSS3 - Text module (Candidate Recommendation) http://www.w3.org/TR/css3-text/#white-space */
word-wrap: break-word; /* IE 5.5+ */
}
b{font-size:17px}
p {
padding:0;
margin:5px 5px 0 17px;
display:list-item;
list-style: square;
}
.logo {
padding-top:10px;
padding-bottom:15px;  
margin-bottom:0px;
color: #111;
text-decoration:none;
font-size:30px;
font-weight:bold; 
border-bottom:0px solid #ccc;
text-align:center;
}	
.logo a{
color:#111 !important;
}
* {
box-sizing: border-box;
}
.content {
max-width:900px;
min-width:200px;
margin:0 auto;
padding: 20px;
border-radius: 3px;
box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
background:#fff;
}
/* unvisited link */
a:link {
  color: #8E2DE2;
  text-decoration:none;
}
/* visited link */
a:visited {
  color: #8E2DE2;
  text-decoration:none;
}
/* mouse over link */
a:hover {
  color: #000;
}
@media screen and (max-width: 1024px){
body{
background: #fff !important;
padding:10px;  
}
.content {
max-width:100%;
padding:0;
border-radius: 3px;
box-shadow: 0 0 0 0;
background:#fff;
}  
.logo {font-size:28px;}  
}  
/* selected link */
a:active {
  color: #8E2DE2;
  text-decoration:none;
  margin-top:10px !important;
}
.resp-sharing-button__link,
.resp-sharing-button__icon {
  display: inline-block
}
.resp-sharing-button__link {
  text-decoration: none;
  color: #fff;
  margin: 0.5em
}
.resp-sharing-button {
  border-radius: 5px;
  transition: 25ms ease-out;
  padding: 0.5em 0.75em;
  font-family: Helvetica Neue,Helvetica,Arial,sans-serif
}
.resp-sharing-button__icon svg {
  width: 1em;
  height: 1em;
  margin-right: 0.4em;
  vertical-align: top
}
.resp-sharing-button--small svg {
  margin: 0;
  vertical-align: middle
}
/* Non solid icons get a stroke */
.resp-sharing-button__icon {
  stroke: #fff;
  fill: none
}
/* Solid icons get a fill */
.resp-sharing-button__icon--solid,
.resp-sharing-button__icon--solidcircle {
  fill: #fff;
  stroke: none
}
.resp-sharing-button--twitter {
  background-color: #55acee
}
.resp-sharing-button--twitter:hover {
  background-color: #2795e9
}
.resp-sharing-button--pinterest {
  background-color: #bd081c
}
.resp-sharing-button--pinterest:hover {
  background-color: #8c0615
}
.resp-sharing-button--facebook {
  background-color: #3b5998
}
.resp-sharing-button--facebook:hover {
  background-color: #2d4373
}
.resp-sharing-button--tumblr {
  background-color: #35465C
}
.resp-sharing-button--tumblr:hover {
  background-color: #222d3c
}
.resp-sharing-button--reddit {
  background-color: #5f99cf
}
.resp-sharing-button--reddit:hover {
  background-color: #3a80c1
}
.resp-sharing-button--google {
  background-color: #dd4b39
}
.resp-sharing-button--google:hover {
  background-color: #c23321
}
.resp-sharing-button--linkedin {
  background-color: #0077b5
}
.resp-sharing-button--linkedin:hover {
  background-color: #046293
}
.resp-sharing-button--email {
  background-color: #777
}
.resp-sharing-button--email:hover {
  background-color: #5e5e5e
}
.resp-sharing-button--xing {
  background-color: #1a7576
}
.resp-sharing-button--xing:hover {
  background-color: #114c4c
}
.resp-sharing-button--whatsapp {
  background-color: #25D366
}
.resp-sharing-button--whatsapp:hover {
  background-color: #1da851
}
.resp-sharing-button--hackernews {
background-color: #8E2DE2
}
.resp-sharing-button--hackernews:hover, .resp-sharing-button--hackernews:focus {   background-color: #FB6200 }
.resp-sharing-button--vk {
  background-color: #507299
}
.resp-sharing-button--vk:hover {
  background-color: #43648c
}
.resp-sharing-button--facebook {
  background-color: #3b5998;
  border-color: #3b5998;
}
.resp-sharing-button--facebook:hover,
.resp-sharing-button--facebook:active {
  background-color: #2d4373;
  border-color: #2d4373;
}
.resp-sharing-button--twitter {
  background-color: #55acee;
  border-color: #55acee;
}
.resp-sharing-button--twitter:hover,
.resp-sharing-button--twitter:active {
  background-color: #2795e9;
  border-color: #2795e9; 
}
.resp-sharing-button--tumblr {
  background-color: #35465C;
  border-color: #35465C;
}
.resp-sharing-button--tumblr:hover,
.resp-sharing-button--tumblr:active {
  background-color: #222d3c;
  border-color: #222d3c;
}
.resp-sharing-button--email {
  background-color: #777777;
  border-color: #777777;
}
.resp-sharing-button--email:hover,
.resp-sharing-button--email:active {
  background-color: #5e5e5e;
  border-color: #5e5e5e;
}
.resp-sharing-button--pinterest {
  background-color: #bd081c;
  border-color: #bd081c;
}
.resp-sharing-button--pinterest:hover,
.resp-sharing-button--pinterest:active {
  background-color: #8c0615;
  border-color: #8c0615;
}

/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}
/* Modal Content */
.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
}

/* The Close Button */
.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}
</style>
</head>
<body onLoad="callmodal()">
<div class="content">
<div class="logo">	  
<a href="https://mikrotiktool.github.io"><span style="color:#8E2DE2 !important">mikrotiktool</span>.Github.io</a> <img style="margin-left:10px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false" alt="Hits"/>
</div>	
<script async src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<!-- mikrotiktool -->
<ins class="adsbygoogle"
     style="display:inline-block;width:728px;height:90px"
     data-ad-client="ca-pub-3815059393374366"
     data-ad-slot="3896250138"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M5TL99Q9M2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-M5TL99Q9M2');
</script>
<span style="line-height:0;float:left; display:inline;margin-right:6px; margin-top:25px">
<img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="HOME">
</span>	 
<h2>FREE MIKROTIK ROUTEROS TOOLS</h2>
<h3>MIKROTIK <span style="color:red">[</span> SCRIPT <span style="color:red">]</span> ROUTEROS DATABASE</h3>
<p><a target="_blank"  href="https://mikrotiktool.github.io/mikrotik">Complete Mikrotik RouterOS Script Database</a> - Enjoy the most complete script to make it easier for you to learn Mikrotik RouterOS scripts!</p>

<h3>MIKROTIK SCRIPT GENERATOR </h3>
<p><a target="_blank"  href="/mikrotik-expired-isolate-pppoe-hotspot.html">Mikrotik Notification Expired For Hotspot, PPPoE And Static IP</a> - Mikrotik All-In-One Notification Isolated and Expired For Hotspot, PPPoE And Static IP For RouterOS</p>
<p><a target="_blank"  href="/mikrotik-burst-limit-calculator.html">MikroTik Burst Limit Calculator</a> - Simple MikroTik Burst Limit Calculator For RouterOS</p>
<p><a target="_blank"  href="/mikrotik-pcq-burst-rate-queue-size-generator.html">MikroTik PCQ Burst Rate + Queue Size Calculator</a> - MikroTik PCQ Burst Rate + Queue Size Calculator For RouterOS</p>
<p><a target="_blank"  href="/vpn-game-generator.html">VPN Routing Port Game Script Generator</a> - Static Routing Vpn Game Script Generator or Script Maker (Port Game Routing Method) </p>
<p><a target="_blank"  href="/vpn-game-generator2.html">VPN Routing IP Address Games Script Generator</a> - Static Routing Vpn Game Script Generator or Script Maker (Ip Address Game Routing Method)</p>
<p><a target="_blank"  href="/ecmp.html">Load Balancing ECMP Script Generator</a> - Load Balancing ECMP (Equal Cost Multi Path) Script Generator or Script Maker For Mikrotik Routeros</p>
<p><a target="_blank"  href="/pcc.html">Load Balancing PCC Script Generator</a> - LB PCC / Load Balancing PCC Script Generator or Script Maker For Mikrotik Routeros</p>
<p><a target="_blank"  href="/nth.html">Load Balancing NTH Script Generator</a> - LB NTH / Load Balancing NTH Script Generator or Script Maker For Mikrotik Routeros</p>  
<p><a target="_blank"  href="/simple-queue-generator.html">Simple Queue Script Generator For Mikrotik + Tocken Bucket</a> - Simple Queue Script Generator or Script Maker For Mikrotik Plus Token Bucket - For IP Subnet /24 Only</p>
<p><a target="_blank"  href="/simple-queue-generator-shared.html">Simple Queue Script Generator For Mikrotik + Bandwidth Shared (UpTo)</a> - Simple Queue Script Generator Mikrotik + Bandwidth Shared (Upto) - For IP Subnet /24 Only</p>
<p><a target="_blank"  href="/queue-tree-generator-shared.html">Queue Tree Script Generator For Mikrotik + Bandwidth Shared (UpTo)</a> - Queue Tree Script Generator Mikrotik + Bandwidth Shared (Upto) - For IP Subnet /24 Only</p>
<p><a target="_blank"  href="/remote-ip-public-static.html">Remote IP Public Static For Secondary Gateway Mikrotik</a> - Simple Script how Remote IP Public Static For Secondary Gateway Mikrotik</p>
<p><a target="_blank"  href="/port-knocking-icmp.html">Port Knocking Generator with (ping) ICMP + Packet Size</a> - Mikrotik Port Knocking Generator or Knocking Script Maker With Icmp + Packet Size</p>
<p><a target="_blank"  href="/pcc-calculation.html">LB PCC Calculation / Load Balancing PCC Calculation</a> - Simple LB PCC (Load Balancing Per Connection Classifier) Calculator</p>
<p><a target="_blank"  href="/mikrotik-pcq-generator.html">PCQ Generator For Queue Tree And Queue Simple</a> - Mikrotik PCQ Generator For Queue Tree And Queue Simple, very easy</p>
<p><a target="_blank"  href="/vpn-tunnel-all-traffic-script-generator.html">VPN Tunnel All Traffic Script Generator</a> - VPN Tunnel All Traffic Script Generator For Mikrotik RouterOS</p>
<p><a target="_blank"  href="/wifid-wms-seamless.html">Wifi.id WMS and Seamless Auto Login Script Generator</a> - Wifi.id WMS and Seamless Auto Login Script Generator For Mikrotik RouterOS</p>

<h3>MIKROTIK TOOLS BY MIKROTIK.COM</h3>
<p><a target="_blank"  href="https://mt.lv/winbox64">Winbox-64 (64bit)</a> - Winbox 64 bit is a small utility that allows administration of MikroTik RouterOS using a fast and simple GUI t is a native Win64 binary</p>
<p><a target="_blank"  href="https://mt.lv/winbox">Winbox-32 (32bit)</a> - Winbox 32 bit is a small utility that allows administration of MikroTik RouterOS using a fast and simple GUI t is a native Win32 binary</p>
<p><a target="_blank"  href="https://mikrotik.com/download">The Dude</a> - The Dude network monitor is a new application by MikroTik which can dramatically improve the way you manage your network environment</p>
<p><a target="_blank"  href="https://mikrotik.com/download">Netinstall</a> - Netinstall is a tool designed for Windows operating systems to reinstall MikroTik devices running RouterOS (except for non-MikroTik x86 devices)</p>
<p><a target="_blank"  href="https://mt.lv/btest">Bandwidth Test</a> - The Bandwidth Tester can be used to measure the throughput to another MikroTik router (either wired or wireless) and thereby help to discover network "bottlenecks".</p>
<p><a target="_blank"  href="https://i.mt.lv/files/exe/MT_Syslog.exe">MT Syslog Daemon</a> - MT Syslog Daemon Is a server log system, to receive log messages from the router</p>
<p><a target="_blank"  href="https://mikrotik.com/download/TrafficCounter.zip">MT Traffic Counter</a> - This traffic monitor can be used to monitor traffic running on an interface on a router.</p>

<h3>MIKROTIK MOBILE TOOLS BY MIKROTIK.COM</h3>
<p><a target="_blank"  href="https://apps.apple.com/id/app/mikrotik/id1323064830?l=id">Mikrotik Winbox iPhone</a> - Use the MikroTik smartphone app on iPhone or IOS to configure your router in the field, or to apply the most basic initial settings for your MikroTik home access point.</p>
<p><a target="_blank"  href="https://play.google.com/store/apps/details?id=com.mikrotik.android.tikapp&hl=in">Mikrotik Winbox Android</a> - Use the MikroTik smartphone app on Android to configure your router in the field, or to apply the most basic initial settings for your MikroTik home access point.</p>

<h3>MIKROTIK ROUTEROS SOFTWARE DOWNLOAD BY MIKROTIK.COM</h3>
<p><a target="_blank"  href="https://mikrotik.com/download">Mikrotik.com/download</a> - If you are already running RouterOS, upgrading to the latest version can be done by clicking on "Check For Updates" in QuickSet or System > Packages menu in WebFig or WinBox. </p>
<p><a target="_blank"  href="http://www.routeros.co.id">Routeros.co.id</a> - Free Download Mikrotik RouterOS all Version</p>

<h3>MIKROTIK WIRELESS TOOLS BY MIKROTIK</h3>
<p><a target="_blank"  href="http://www.mikrotik.co.id/test_tower.php">Antenna Height Calculation</a> - This application can be used to calculate the recommended antenna height, taking into account the frequency used</p>
<p><a target="_blank"  href="https://shop.duxtel.com.au/article_info.php?articles_id=53">MikroTik Link Planner</a> - MikroTik Link Planner To calculate what maximum link speed you can achieve using specific equipment</p>
<p><a target="_blank"  href="http://www.mikrotik.co.id/getfile.php?nf=wireless_abg.zip">Wireless MiniPCI A/B/G</a> - Mikrotik Driver Wireless MiniPCI A/B/G For windows.</p>
<p><a target="_blank"  href="https://mikrotik.com/calculator">Wireless link calculator (need login)</a> - a tool for calculating the link (link posibility) of wireless connections from MikroTik devices</p>
<p><a target="_blank"  href="https://mikrotik.com/client/distributorLockpack">Lockpack creator (need login)</a> - Create frequency and other locks package</p>

<h3>MIKROTIK OTHER TOOLS LIKE CALCULATOR AND MONITOR</h3>
<p><a target="_blank"  href="https://www.mikrotik.id/artikel_lihat.php?id=215">Traffic Monitor with Cacti linux</a> - Cacti is an open source, web-based network monitoring application and is a complete network graphic creation solution designed to take advantage of the capabilities of RRDTool functions as data storage and graphics creation.</p>
<p><a target="_blank"  href="http://www.routerboard.co.id/">RouterBoard Comparison Table</a> - Simple Tool For Comparison Mikrotik Routerboard</p>
<p><a target="_blank"  href="http://www.mikrotik.co.id/test_proxy.php">RAM Proxy Mikrotik Calculator</a> - Calculation of Mikrotik Proxy RAM Requirements</p>
<p><a target="_blank"  href="https://wispcasts.com/tools/burst-calculator">MikroTik Burst Calculator</a> - Burst is a feature in MikroTik RouterOS that can allow users to exceed Max-Limit for a limited period of time.</p>
<p><a target="_blank"  href="http://www.mikrotik.co.id/getfile.php?nf=neighbour.zip">Neighbour Viewer dan Mac Telnet</a> - To see the Mikrotik that is directly connected to your computer, and telnet based on a mac address (no need for an ip address)</p>
<p><a target="_blank"  href="http://www.mikrotik.co.id/getfile.php?nf=trafr.tgz">Trafr Sniffer linux</a> - Traffic Sniffer stream reader for Linux</p> 
<p><a target="_blank"  href="https://forum.mikrotik.com/viewtopic.php?t=77193">ATTIX5 Traffic Monitor</a> - Attix5 - Traffice Real-Time Monitor Analysis Mikrotik</p>

<h3>MIKROTIK BILLING HOTSPOT / RADIUS / USERMANAGER CUSTOM TOOLS</h3>
<p><a target="_blank"  href="https://freeradius.org">FREERADIUS</a> - FreeRADIUS is a modular, high performance free RADIUS suite developed and distributed under the GNU General Public License, version 2, and is free for download and use. The FreeRADIUS Suite includes a RADIUS server, a BSD-licensed RADIUS client library, a PAM library, an Apache module, and numerous additional RADIUS related utilities and development libraries.</p>
<p><a target="_blank"  href="http://dmasoftlab.com">DMA Radius Manager</a> - DMA Radius Manager is a easy to use administration system for Mikrotik, Cisco, StarOS, Chillispot,DD-WRT, pfSense NAS devices and DOCSIS CMTS. It provides centralized authentication, accounting and billing functions.</p>
<p><a target="_blank"  href="https://billingku.net">Billingku</a> - BILLINGKU is a website and android based internet billing data management application. This application has been integrated with the payment gateway system, the mikrotik api system, the full whatsapp gateway system, and many others.</p>
<p><a target="_blank"  href="https://laksa19.github.io/">Mikhmon Billing Hotspot</a> - MikroTik Hotspot Monitor "MIKHMON" is a web-based application (MikroTik API PHP class) to assist MikroTik Hotspot management. Especially MikroTik which does not support User Manager. Mikhmon is not a server radius, so it doesn't have to be always active. Mikhmon can be activated when needed or as needed.</p>
<p><a target="_blank"  href="https://sarang.web.id/billing-hotspot-by-sarang-net/">Billing Hotspot By Sarang-Net</a> - Billing Hotspot By Sarang-Net is a desktop application use (MikroTik API PHP class) to assist MikroTik Hotspot management.</p>
<p><a target="_blank"  href="https://topsetting.com">MIXRadius</a> - MIXRADIUS has many features that are very powerful and very stable which will make it easier for you to manage internet customers and hotspot voucher sales. </p>
<p><a target="_blank"  href="https://rlradius.id/">RL-Radius</a> - RLRadius is a radius server, hotspot mikrotik billing and PPPoE integrated in one system, rich in features and can be used for many mikrotik routers on different networks and locations, with centralized db on one server.</p>
<p><a target="_blank"  href="https://sourceforge.net/projects/the-userman">TheUserman</a> - TheUserman is Mikrotik Hotspot User Management Application Based on Desktop and Android, Mikrotik Hotspot Voucher Maker Application.</p>
<p><a target="_blank"  href="https://github.com/ibnux/phpmixbill">PHPMixBill</a> - PHPMixBill is a Hotspot and PPPOE billing application for Mikrotik using the PHP programming language and using the Mikrotik API as communication with the router.</p>
<p><a target="_blank"  href="https://github.com/aviantorichad/MIKROSTATOR">MIKROSTATOR</a> - MIKROSTATOR is an application that is used to perform administration on an internet network using a mikrotik router.</p>
<p><a target="_blank"  href="https://mikbotam.net">MIKBOTAM</a> - Mikbotam is an application that makes it easy for hotspot services, because it makes it easier to generate usernames and passwords as well as easier user management with a pleasing web interface.</p>
<br>

 <script async src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<!-- mikrotiktool -->
<ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-3815059393374366"
     data-ad-slot="3756649336"
     data-ad-format="auto"
     data-full-width-responsive="true"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script> 
<div style="margin-top:20px; text-align:center; font-size:14px"
© Copyright 2020-2021 <a href="htpps://mikrotiktool.github.io">Free Mikrotik Routeros Tools</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a> <br> Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a>   
</div>
</div>
<script type="text/javascript">
var modal = document.getElementById("myModal");
var span = document.getElementsByClassName("close")[0];
function callmodal() {
  modal.style.display = "block";
}
span.onclick = function() {
  modal.style.display = "none";
}
window.onclick = function(event) {
  if (event.target == modal) {
    modal.style.display = "none";
  }
}
</script>
</body>
</html>
