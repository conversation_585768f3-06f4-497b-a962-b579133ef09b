<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Sending Your Self An E-Mail With Dsl Interface Ip Address - MikroTik Script RouterOS</title>
<meta content='Sending Your Self An E-Mail With Dsl Interface Ip Address - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Sending Your Self An E-Mail With Dsl Interface Ip Address - MikroTik Script RouterOS</h1>
<pre>The goal
The goal is to know yout DSL interface IP address at all time if you need to connect to your MT box for any reason. It is a simple script. (Based on Dynamic DNS Update Script for ChangeIP.com)

The script
Create a new script with the following source

<code class="routeros">:global ddnsip
:global ddnslastip
:if ([ :typeof $ddnslastip ] = nil ) do={ :global ddnslastip "0" }
:global ddnsinterface
:global ddnssystem ("Version-" . [/system package get system version] )
:local int
:foreach int in=[/ip route find dst-address=0.0.0.0/0 active=yes ] do={
  :if ([:typeof [/ip route get $int routing-mark ]] != str ) do={
     :global ddnsinterface [/ip route get $int interface]
  }
}
:global ddnsip [ /ip address get [/ip address find interface=$ddnsinterface ] address ]
:if ([ :typeof $ddnsip ] = nil ) do={
   :log info ("DDNS: No ip address present on " . $ddnsinterface . ", please check.")
} else={
  :if ($ddnsip != $ddnslastip) do={
    :global strDate [/system clock get date]
    :global strTime [/system clock get time]
    :global strSystemName [/system identity get name]
    /tool e-mail send from=from@email_here_com to=to@email_here_com subject="DSL IP $strDate $strTime $strSystemName" body="DSL IP $ddnsip" server=mail_server_ip_address
    :log info "DDNS: Sending UPDATE!"
    :global ddnslastip "$ddnsip"
  } else={
  }
}</code>
The schedule
Create a schedule that runs the script every ten or so minutes and save.

<code class="routeros">add comment="" disabled=no interval=10m name=schedule1 on-event="your_script_name_here" start-date=may/15/2008 start-time=09:00:00</code>
Since it is a simple script, you can schedule it to run every minute.

Shorter version
With no interface lookup.

<code class="routeros">:global ddnsip
:global ddnslastip
:global str_date [/system clock get date]
:global str_time [/system clock get time] 
:global str_system_name [/system identity get name]
:if ([ :typeof $ddnslastip ] = nil ) do={ :global ddnslastip "0" }
:global ddnssystem ("Version-" . [/system package get system version] )
:global ddnsip [ /ip address get [/ip address find interface=ADSL ] address ]
:if ([ :typeof $ddnsip ] = nil ) do={
   :log info ("DDNS: No ip address present on  ADSL interface, please check.")
} else={
  :if ($ddnsip != $ddnslastip) do={
    /tool e-mail send from=from@email_here_com to=to@email_here_com subject="DSL IP $str_date $str_time $str_system_name" body="DSL IP $ddnsip" server=mail_server_ip_address
    :log info "DDNS: Sending UPDATE!"
    :global ddnslastip "$ddnsip"
  } else={ 
#    :log info "DDNS: No update required."
    }
}</code>
For this to work, simple rename your interface to ADSL and create a schedule as for the first script.

Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
