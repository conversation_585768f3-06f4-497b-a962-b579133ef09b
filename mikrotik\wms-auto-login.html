<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Auto Login WMS or wifi.id - MikroTik Script RouterOS</title>
<meta content='Auto Login WMS or wifi.id - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Auto Login WMS or Seamless wifi.id - Mikrotik Script RouterOS</h1>
<pre>How to auto login WMS from wifi.id Telkom Indonesia

WMS stands for "Wifi Managed Service", which is a special wifi internet service from Telkom that allows customers to have 2-3 SSIDs, one of which is SSID for the owner, and the other two are SSID @ wifi.<NAME_EMAIL>. 

<code class="routeros">:local currentQueue 0;
:local nolock true;
:local gurl "https://welcome2.wifi.id";
:local guxx "http://www.msftconnecttest.com/redirect";
:local username "";
:local password "";
:local int "WMS2";
:local gwp "WAG-D1-PBR";
:local mdlan "GPON00-D1-TAK-3BSR%20pon%201/3/22/1:4090:<EMAIL>";
:local type "WMS";

:while (true) do={ 
 :do {
  :delay 15s;
  :if ([/ping address=******* count=1] = 0) do={
   :set currentQueue ($currentQueue +1);
   
   :if ($currentQueue > 3) do={
    :if (nolock) do={	
	 :set nolock false;
	 
     :global gip [/ip address get [/ip address find interface=$int] address];
     :global gip [put [:pick $gip 0 [:find $gip "/"]]];
     :global gmc  [/interface wireless get $int mac-address];   
     :global vrab "ipc=$gip&gw_id=$gwp&mac=$gmc&redirect=$guxx&wlan=$mdlan";
     :global url "$gurl/authnew/login/check_login.php\?$vrab";
     :global post "username=$username@spin2&password=$password";
   
     :if ($type = "WMS") do={
      :set username "userWMSmu"
      :set password "passWMSmu"
      :set url "$gurl/wms/auth/authnew/autologin/quarantine.php\?$vrab"
      :set post "username_=$username&username=$username.TrL1@wmslite.123456789.000&password=$password"
     };
	
     log warning ("Internet Mati Memulai Koneksi Ulang | $gip | $gmc | $type | $currentQueue"); 	
     :set currentQueue 0;
	
	 :do {
	  /interface disable $int
      :delay 15s;
      /interface enable $int
	  :delay 15s
	 } on-error={
      log warning ("Error set");
     };

     :do {
	  :local result [/tool fetch http-method=post http-data=$post url=$url http-header-field="User-Agent: Mozilla/5.0" as-value output=user];
      :if ([:find ($result->"data") "Sukses"] >= 0) do={
       log info ("WIFI Kembali Normal")
      } else={
       :if (($result->"data") = "") do={
        log info ("Wifi sudah konek");
       } else={
        log warning ($result->"data")
       };
      };
	 } on-error={
      log warning ("Error Cek Internet");
     };
	 
	 :set nolock true;
	 
	} else={
	 log warning ("Lock Proses");	 
	};
	
   };
   
  } else {
   # log info ("Wifi Normal");
   :set currentQueue 0;
  };
  
  } on-error={
   log warning ("Error big");
   :set currentQueue ($currentQueue +1);
  };
}</code>
Credit: <a href="https://gist.github.com/akbaryahya/c0b0d937473a757e5867134ea5532a6c">https://gist.github.com/akbaryahya</a> | <a href="https://www.tembolok.id/script-autologin-wms/">https://www.tembolok.id/script-autologin-wms</a>
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>