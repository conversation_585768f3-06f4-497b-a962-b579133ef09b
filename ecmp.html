<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset='utf-8' />
	<meta content='width=device-width, initial-scale=1, maximum-scale=1' name='viewport' />
	<title>Load Balancing ECMP Script Generator - mikrotiktool.Github.io</title>
	<meta content='Load Balancing ECMP Script Generator For Mikrotik Routeros' name='description' />
	<meta content='script generator, mikrotik, router, winbox, termimal, rsc, script, hotspot, wireless, rtrwnet' name='keywords' />
	<meta content='index, follow, noodp' name='robots' />
	<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon">
	<meta property="og:title" content="Load Balancing ECMP Script Generator ">
	<meta property="og:description" content="Load Balancing ECMP Script Generator ">
	<meta property="og:image:alt" content="Load Balancing ECMP Script Generator ">
	<meta property="og:type" content="website">
<meta property="og:url" content="https://mikrotiktool.github.io/ecmp.html">
	<script data-ad-client="ca-pub-3815059393374366" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
	<style>
 
*{-webkit-transition: all .3s ease-in;-moz-transition: all .3s ease-in;-ms-transition: all .3s ease-in;transition: all .3s ease-in;}
body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table{margin:0; padding:0}
body { font-size:16px; font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; margin:20px;}
html, body {
height: 100%;
width:100%;
margin-top: 0px;
padding:0;
}
* {
box-sizing: border-box;
}
.logo {
margin-top:0px;
color: #111;
text-decoration:none;
font-size:27px;
font-weight:bold;
}
.logo a{
color:#111 !important;
text-decoration:none !important;
}
.Menu{
width:100%;
border-bottom:1px solid #ccc;
height:100%;
padding-bottom: 15px;
margin-bottom: 20px;
font-size:14px;
font-weight:bold;
}
#wrap{
width:1120px;
margin:0 auto;
padding:10px;
background:#fff
}
.header{
height:auto;
width:100%;
margin-top:0px;
margin-bottom:10px;
border-top:1px solid #ccc;
}
.main-wrap{
display:flex;
}
.content{
flex:1;
float:right;
width:800px;
padding: 10px;
border-top:1px solid #bbb;
border-bottom:1px solid #bbb;
border-right:1px solid #bbb;
background:#ddd;
height:auto;
}
.sidebar{
float:left;
width:300px;
Padding-top:8px;
padding-left: 10px;
Padding-right:10px;
Padding-bottom:20px;
background-color:#ddd;
border:1px solid #bbb;
height:auto;
}
.footer{
font-size:14px;
padding-top:15px;
clear:both;
width:auto;
}
h1 {
color:#111;
font-size:22px;
margin-bottom:10px;
margin-top:10px;
}
a:link {
color: #a8328b;;
}
a:visited {
color: #a8328b;;
}
a:hover {
color: #a8328b;;
}
a:active {
color: #a8328b;;
}
.menu {
clear:both;
width:auto;
padding-bottom:13px;
}
.menu1 {
width:360px;
margin-bottom:1px;
font-weight:bold;
background-color:#4CAF50;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu1 a:link {
text-decoration:none;
color:white;
}
.menu1 a:visited{
text-decoration:none;
color:white;
}
.menu2 {
width:300px;
margin-bottom:1px;
font-weight:bold;
background-color:#bbb;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu2 a:link{
text-decoration:none;
color:white;
}
.menu2 a:visited{
text-decoration:none;
color:white;
}
button {
color: #fff;
background-color: #a8328b;
border-color: #a8328b;
border:none;
padding:8px;
width:130px;
font-size:16px;
font-weight:bold;
}
.row:after {
content: "";
display: table;
clear: both;
}
input[type=text], select, textarea {
width: 100%;
padding: 5px;
border: 1px solid #bbb;
border-radius: 1px;
resize: vertical;
margin-bottom:12px;
font-size:16px !important;
}
label {
padding: 5px 5px 5px 0;
display: inline-block;
}
input[type=submit] {
background-color: #a8328b;
color: white;
padding: 12px 20px;
border: none;
border-radius: 1px;
cursor: pointer;
float: right;
}
input[type=submit]:hover {
background-color: #45a049;
}
.col-25 {
float: left;
width: 25%;
margin-top: 6px;
}
.col-75 {
float: left;
width: 75%;
margin-top: 6px;
}
/* Clear floats after the columns */
.row:after {
content: "";
display: table;
clear: both;
}
.list-game {
height: 200px;
background:white;
overflow: scroll;
overflow-x: hidden;
width: 100%;
margin-top:2px;
padding: 3px;
border: 1px solid rgba(0,0,0,0.25);
}
.list-game label {
padding: 0;
display: inline-block;
}
.list-mangle {
height: 597px;
background:#fff;
overflow: scroll;
width: 100%;
padding: 4px;
margin-top:8px;
border: 1px solid #ccc;
}
table, tr, td {
border: none;
}

.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
}

/* The Close Button */
.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

</style>
</head>
<body onLoad="callmodal()">
<div id="wrap">
<div class="logo">
<a href="https://mikrotiktool.github.io"><span style="color:#a8328b !important">mikrotiktool</span>.Github.io</a>  <img style="float:right; padding-top:10px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=hits&edge_flat=false" alt="Hits"/>
</div>
		<div class="header">
			<span style="float:left; display:inline;margin-right:6px; margin-top:10px">
<a href="https://mikrotiktool.github.io/ecmp.html"><img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="HOME"></a>
</span>
			<h1>LOAD BALANCING <span style="color:#a8328b">ECMP</span> (Equal Cost Multi Path) SCRIPT GENERATOR FOR MIKROTIK ROUTEROS</h1>
		</div>
        <div class="main-wrap">
		<div class="sidebar">
			<label>Select Number Your ISP Line</label>
			<div style="clear: both;"></div>
			<select id="wan-list" onclick="myFunctionOuput()">
				<option value="line2">2 Line ISP</option>
				<option value="line3">3 Line ISP</option>
				<option value="line4">4 Line ISP</option>
				<option value="line5">5 Line ISP</option>
				<option value="line6">6 Line ISP</option>
			</select>
			<label style="font-size:16px;color:#a8328b; border:1px solid #a8328b; padding:7px;margin-top:5px;margin-bottom:10px">Atention! Please Change Your WAN Interface Name With Your Router Condition, Example: ether1 or E1-WAN or e1-indihome or pppoe-out1
			</label>
			<div style="clear: both;"></div>
			<div style="float:left; width:130px;">
				<label>WAN ISP-1</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan1-isp" placeholder="Ex: ether1">
			</div>
			<div style="float:left; width:130px;margin-left:10px">
				<label>Gateway ISP-1</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan1-isp-ip" placeholder="Ex: ***********">
			</div>
			<div style="clear: both;"></div>
			<div style="float:left; width:130px;">
				<label>WAN ISP-2</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan2-isp" placeholder="Ex: ether2">
			</div>
			<div style="float:left; width:130px;margin-left:10px">
				<label>Gateway ISP-2</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan2-isp-ip" placeholder="Ex: ***********">
			</div>
			<div style="clear: both;"></div>
			<div style="float:left; width:130px;">
				<label>WAN ISP-3</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan3-isp" placeholder="Ex: ether3" disabled>
			</div>
			<div style="float:left; width:130px;margin-left:10px">
				<label>Gateway ISP-3</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan3-isp-ip" placeholder="Ex: ***********" disabled>
			</div>
			<div style="clear: both;"></div>
			<div style="float:left; width:130px;">
				<label>WAN ISP-4</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan4-isp" placeholder="Ex: ether4" disabled>
			</div>
			<div style="float:left; width:130px;margin-left:10px">
				<label>Gateway ISP-4</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan4-isp-ip" placeholder="Ex: ***********" disabled>
			</div>
			<div style="clear: both;"></div>
			<div style="float:left; width:130px;">
				<label>WAN ISP-5</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan5-isp" placeholder="Ex: ether5" disabled>
			</div>
			<div style="float:left; width:130px;margin-left:10px">
				<label>Gateway ISP-5</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan5-isp-ip" placeholder="Ex: ***********" disabled>
			</div>
			<div style="clear: both;"></div>
			<div style="float:left; width:130px;">
				<label>WAN ISP-6</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan6-isp" placeholder="Ex: ether6" disabled>
			</div>
			<div style="float:left; width:130px;margin-left:10px;margin-bottom:10px">
				<label>Gateway ISP-6</label>
				<div style="clear: both;"></div>
				<input type="text" id="wan6-isp-ip" placeholder="Ex: ***********" disabled>
			</div>
			<button style="margin-right:2px" type="button" onclick="myFunctionInput()">Generate</button>
			<button type="button" onclick="location.reload()">Clear All</button>
			<div style="clear: both;"></div>
		</div>
		<div class="content"> <span style="margin-left:2px">Script Generator Result</span>
			<div class="list-mangle">
			<div style="width:100%; height:90px; margin-top:10px; margin-left:10px; margin-bottom:5px;">
			<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
			<!-- MIKROTIK SCRIPT DB3 -->
			<ins class="adsbygoogle"
			style="display:inline-block;width:728px;height:90px"
			data-ad-client="ca-pub-3815059393374366"
			data-ad-slot="4637807296"></ins>
			<script>
			(adsbygoogle = window.adsbygoogle || []).push({});
			</script>
			</div>
				<table id="showScript" style="padding:5px; white-space: nowrap;">
					<tr>
						<td> <span style="color:#a8328b;">
                     ################################################<br>
                     # LOAD BALANCING ECMP SCRIPT GENERATOR<br>
                     # Date/Time: <span id="tanggalwaktu"></span>
							<br>#  
							<br># Load Balancing <span id="vpn-list-text-info" style="font-weight:bold;text-transform: uppercase">ECMP (Equal Cost Multi Path)</span>
							<br>################################################
							<br>
							<br>
							</span>	<b>/ip firewall nat</b>
							<br>	<span id="wan1-nat">add chain=srcnat out-interface="<span id="wan1-nat-text"></span>" action=masquerade comment="LB By BNT"
							<br>
							</span>	<span id="wan2-nat">add chain=srcnat out-interface="<span id="wan2-nat-text"></span>" action=masquerade comment="LB By BNT"
							<br>
							</span>	<span id="wan3-nat" style="display:none">add chain=srcnat out-interface="<span id="wan3-nat-text"></span>" action=masquerade comment="LB By BNT"
							<br>
							</span>	<span id="wan4-nat" style="display:none">add chain=srcnat out-interface="<span id="wan4-nat-text"></span>" action=masquerade comment="LB By BNT"
							<br>
							</span>	<span id="wan5-nat" style="display:none">add chain=srcnat out-interface="<span id="wan5-nat-text"></span>" action=masquerade comment="LB By BNT"
							<br>
							</span>	<span id="wan6-nat" style="display:none">add chain=srcnat out-interface="<span id="wan6-nat-text"></span>" action=masquerade comment="LB By BNT"
							<br>
							</span>	<b>/ip route</b>
							<br>	<span id="nth-route">add check-gateway=ping distance=1 gateway="<span id="wan1-nth-route-text"></span>,<span id="wan2-nth-route-text"></span>,<span id="wan3-nth-route-text"></span>,<span id="wan4-nth-route-text"></span>,<span id="wan5-nth-route-text"></span>,<span id="wan6-nth-route-text"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan1-route">add check-gateway=ping distance=1 gateway="<span id="wan1-route-text"></span>" routing-mark="<span id="wan1-route-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan2-route">add check-gateway=ping distance=1 gateway="<span id="wan2-route-text"></span>" routing-mark="<span id="wan2-route-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan3-route" style="display:none">add check-gateway=ping distance=1 gateway="<span id="wan3-route-text"></span>" routing-mark="<span id="wan3-route-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan4-route" style="display:none">add check-gateway=ping distance=1 gateway="<span id="wan4-route-text"></span>" routing-mark="<span id="wan4-route-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan5-route" style="display:none">add check-gateway=ping distance=1 gateway="<span id="wan5-route-text"></span>" routing-mark="<span id="wan5-route-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan6-route" style="display:none">add check-gateway=ping distance=1 gateway="<span id="wan6-route-text"></span>" routing-mark="<span id="wan6-route-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<b>/ip firewall mangle</b>
							<br>	<span id="wan1-mangle-input">add action=mark-connection chain=input in-interface="<span id="wan1-mangle-input-text"></span>" new-connection-mark="<span id="wan1-mangle-input-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan2-mangle-input">add action=mark-connection chain=input in-interface="<span id="wan2-mangle-input-text"></span>" new-connection-mark="<span id="wan2-mangle-input-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan3-mangle-input" style="display:none">add action=mark-connection chain=input in-interface="<span id="wan3-mangle-input-text"></span>" new-connection-mark="<span id="wan3-mangle-input-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan4-mangle-input" style="display:none">add action=mark-connection chain=input in-interface="<span id="wan4-mangle-input-text"></span>" new-connection-mark="<span id="wan4-mangle-input-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan5-mangle-input" style="display:none">add action=mark-connection chain=input in-interface="<span id="wan5-mangle-input-text"></span>" new-connection-mark="<span id="wan5-mangle-input-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan6-mangle-input" style="display:none">add action=mark-connection chain=input in-interface="<span id="wan6-mangle-input-text"></span>" new-connection-mark="<span id="wan6-mangle-input-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan1-mangle-output">add action=mark-routing chain=output connection-mark="<span id="wan1-mangle-output-text"></span>" new-routing-mark="<span id="wan1-mangle-output-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan2-mangle-output">add action=mark-routing chain=output connection-mark="<span id="wan2-mangle-output-text"></span>" new-routing-mark="<span id="wan2-mangle-output-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan3-mangle-output" style="display:none">add action=mark-routing chain=output connection-mark="<span id="wan3-mangle-output-text"></span>" new-routing-mark="<span id="wan3-mangle-output-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan4-mangle-output" style="display:none">add action=mark-routing chain=output connection-mark="<span id="wan4-mangle-output-text"></span>" new-routing-mark="<span id="wan4-mangle-output-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan5-mangle-output" style="display:none">add action=mark-routing chain=output connection-mark="<span id="wan5-mangle-output-text"></span>" new-routing-mark="<span id="wan5-mangle-output-text-r"></span>" comment="LB By BNT"
							<br>
							</span>	<span id="wan6-mangle-output" style="display:none">add action=mark-routing chain=output connection-mark="<span id="wan6-mangle-output-text"></span>" new-routing-mark="<span id="wan6-mangle-output-text-r"></span>" comment="LB By BNT"
							<br>
							</span>
						</td>
					</tr>
				</table>
			</div>
			<br>
			<button style="margin-top:-5px" id="copyscript" type="button" onclick="selectElementContents(document.getElementById('showScript'))">Copy Script</button><span style="padding:7px; font-weight:normal;margin-left:10px"><b>Copy-Paste to Terminal, make sure this Mangle Script on the Top position!</b></span>
		</div>
        </div>
		<div class="footer">
		<div style="float:right">
	    © Copyright 2020-2021 <a href="https://mikrotiktool.github.io/">mikrotiktool.github.io</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a><br> Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a>  
		</div>
		<span style="color:#a8328b"><b># This script for remove all script from this Generator</b></span>
			<br>/ip firewall nat remove [find comment="LB By BNT"]
			<br>/ip route remove [find comment="LB By BNT"]
			<br>/ip firewall mangle remove [find comment="LB By BNT"]
		</div>
	</div>
 
	<script type="text/javascript">
var modal = document.getElementById("myModal");
var span = document.getElementsByClassName("close")[0];
function callmodal() {
  modal.style.display = "block";
}
span.onclick = function() {
  modal.style.display = "none";
}
window.onclick = function(event) {
  if (event.target == modal) {
    modal.style.display = "none";
  }
}
</script>
</script>
	<script>
	var _0x27b7=['wan6-route-text','display','subnet\x20mask','wan4-route','wan3-nat-text','block','</span>','wan5-mangle-output-text','wan1-mangle-input-text-r','wan3-isp-ip','wan4-nth-route-text','wan5-mangle-input','wan2-mangle-output-text-r','wan3-route','wan4-isp-ip','wan5-nat-text','<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>cm-','createRange','wan3-mangle-output','wan6-nat','line2','select','wan5-nat','wan3-nth-route-text','wan3-mangle-input','wan4-route-text-r','wan2-mangle-input-text','wan3-nat','wan4-mangle-input','wan3-mangle-input-text','style','innerHTML','wan4-nat-text','selectNode','wan5-mangle-output-text-r','focus','wan-list','<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>','wan5-route-text-r','wan4-isp','none','wan1-isp-ip','wan3-mangle-input-text-r','removeAllRanges','wan3-mangle-output-text-r','subnet','IP\x20Address','gateway','wan5-isp','wan4-mangle-output','wan2-nth-route-text','addRange','toLocaleString','wan6-mangle-output','wan6-mangle-input','wan1-route-text','wan5-mangle-input-text-r','ipaddress','wan5-mangle-output','wan1-mangle-input-text','line5','wan2-mangle-input-text-r','wan6-isp-ip','getElementById','disabled','wan5-nth-route-text','wan5-route','match','wan6-isp','wan1-mangle-output-text-r','wan1-isp','value','body','line4','wan4-route-text','moveToElementText','wan5-isp-ip','wan1-mangle-output-text','wan5-route-text','Attention!\x20IP\x20Address\x20Validation\x20Incorrect!\x20Please\x20check\x20your\x20IP\x20address\x20again!','wan2-route-text-r','wan6-nth-route-text','<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>to-','wan4-mangle-output-text','wan6-route','line3','wan4-nat','DNS','wan4-mangle-input-text-r','selectNodeContents','wan6-nat-text','wan6-mangle-output-text-r','wan3-mangle-output-text','wan3-isp','wan6-mangle-input-text','wan4-mangle-output-text-r','wan2-isp-ip','wan2-route-text','getSelection'];(function(_0x579f6d,_0x1cda53){var _0x27b70a=function(_0x41f120){while(--_0x41f120){_0x579f6d['push'](_0x579f6d['shift']());}};_0x27b70a(++_0x1cda53);}(_0x27b7,0x74));var _0x41f1=function(_0x579f6d,_0x1cda53){_0x579f6d=_0x579f6d-0xb9;var _0x27b70a=_0x27b7[_0x579f6d];return _0x27b70a;};var _0x353efd=_0x41f1,dt=new Date();document[_0x353efd(0xe7)]('tanggalwaktu')[_0x353efd(0xc7)]=dt[_0x353efd(0xdc)]();function myFunctionInput(){var _0x33c9c5=_0x353efd,_0x435a02=document['getElementById'](_0x33c9c5(0xee))[_0x33c9c5(0xef)],_0x57051c=document[_0x33c9c5(0xe7)](_0x33c9c5(0xd1))['value'];document['getElementById']('wan1-nat-text')[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x435a02+_0x33c9c5(0x111),document['getElementById'](_0x33c9c5(0xdf))[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x57051c+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)]('wan1-route-text-r')['innerHTML']='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>to-'+_0x435a02+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xe3))[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>'+_0x435a02+_0x33c9c5(0x111),document['getElementById'](_0x33c9c5(0x113))[_0x33c9c5(0xc7)]=_0x33c9c5(0x11b)+_0x435a02+'</span>',document[_0x33c9c5(0xe7)](_0x33c9c5(0xf5))[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>cm-'+_0x435a02+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xed))[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>to-'+_0x435a02+_0x33c9c5(0x111),document['getElementById']('wan1-nth-route-text')['innerHTML']=_0x33c9c5(0xcd)+_0x57051c+_0x33c9c5(0x111);var _0x42046b=document[_0x33c9c5(0xe7)]('wan2-isp')[_0x33c9c5(0xef)],_0x1b7f16=document[_0x33c9c5(0xe7)](_0x33c9c5(0x108))[_0x33c9c5(0xef)];document[_0x33c9c5(0xe7)]('wan2-nat-text')[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x42046b+_0x33c9c5(0x111),document['getElementById'](_0x33c9c5(0x109))[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x1b7f16+_0x33c9c5(0x111),document['getElementById'](_0x33c9c5(0xf8))['innerHTML']=_0x33c9c5(0xfa)+_0x42046b+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xc2))[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x42046b+_0x33c9c5(0x111),document['getElementById'](_0x33c9c5(0xe5))[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>cm-'+_0x42046b+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)]('wan2-mangle-output-text')[_0x33c9c5(0xc7)]=_0x33c9c5(0x11b)+_0x42046b+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0x117))[_0x33c9c5(0xc7)]=_0x33c9c5(0xfa)+_0x42046b+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xda))['innerHTML']='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>'+_0x1b7f16+'</span>';var _0x3d652f=document[_0x33c9c5(0xe7)](_0x33c9c5(0x105))[_0x33c9c5(0xef)],_0x4c54d5=document[_0x33c9c5(0xe7)]('wan3-isp-ip')[_0x33c9c5(0xef)];document[_0x33c9c5(0xe7)](_0x33c9c5(0x10f))[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x3d652f+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)]('wan3-route-text')['innerHTML']=_0x33c9c5(0xcd)+_0x4c54d5+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)]('wan3-route-text-r')['innerHTML']=_0x33c9c5(0xfa)+_0x3d652f+_0x33c9c5(0x111),document['getElementById'](_0x33c9c5(0xc5))[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>'+_0x3d652f+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xd2))['innerHTML']=_0x33c9c5(0x11b)+_0x3d652f+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0x104))[_0x33c9c5(0xc7)]=_0x33c9c5(0x11b)+_0x3d652f+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xd4))['innerHTML']='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>to-'+_0x3d652f+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xbf))[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x4c54d5+_0x33c9c5(0x111);var _0x331498=document[_0x33c9c5(0xe7)](_0x33c9c5(0xcf))['value'],_0x517d8b=document[_0x33c9c5(0xe7)]('wan4-isp-ip')[_0x33c9c5(0xef)];document[_0x33c9c5(0xe7)](_0x33c9c5(0xc8))['innerHTML']=_0x33c9c5(0xcd)+_0x331498+'</span>',document[_0x33c9c5(0xe7)](_0x33c9c5(0xf2))[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x517d8b+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xc1))['innerHTML']=_0x33c9c5(0xfa)+_0x331498+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)]('wan4-mangle-input-text')[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x331498+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0x100))[_0x33c9c5(0xc7)]=_0x33c9c5(0x11b)+_0x331498+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xfb))[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>cm-'+_0x331498+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0x107))['innerHTML']=_0x33c9c5(0xfa)+_0x331498+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0x115))[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x517d8b+_0x33c9c5(0x111);var _0x5acc48=document[_0x33c9c5(0xe7)](_0x33c9c5(0xd8))[_0x33c9c5(0xef)],_0x37e068=document[_0x33c9c5(0xe7)](_0x33c9c5(0xf4))[_0x33c9c5(0xef)];document[_0x33c9c5(0xe7)](_0x33c9c5(0x11a))[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>'+_0x5acc48+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xf6))[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>'+_0x37e068+_0x33c9c5(0x111),document['getElementById'](_0x33c9c5(0xce))[_0x33c9c5(0xc7)]=_0x33c9c5(0xfa)+_0x5acc48+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)]('wan5-mangle-input-text')[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>'+_0x5acc48+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xe0))[_0x33c9c5(0xc7)]=_0x33c9c5(0x11b)+_0x5acc48+_0x33c9c5(0x111),document['getElementById'](_0x33c9c5(0x112))[_0x33c9c5(0xc7)]=_0x33c9c5(0x11b)+_0x5acc48+'</span>',document[_0x33c9c5(0xe7)](_0x33c9c5(0xca))[_0x33c9c5(0xc7)]=_0x33c9c5(0xfa)+_0x5acc48+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xe9))[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x37e068+_0x33c9c5(0x111);var _0x336327=document[_0x33c9c5(0xe7)]('wan6-isp')['value'],_0x47f70d=document[_0x33c9c5(0xe7)](_0x33c9c5(0xe6))[_0x33c9c5(0xef)];document[_0x33c9c5(0xe7)](_0x33c9c5(0x102))[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x336327+'</span>',document[_0x33c9c5(0xe7)](_0x33c9c5(0x10b))[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>'+_0x47f70d+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)]('wan6-route-text-r')[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>to-'+_0x336327+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0x106))[_0x33c9c5(0xc7)]=_0x33c9c5(0xcd)+_0x336327+'</span>',document['getElementById']('wan6-mangle-input-text-r')[_0x33c9c5(0xc7)]=_0x33c9c5(0x11b)+_0x336327+'</span>',document[_0x33c9c5(0xe7)]('wan6-mangle-output-text')['innerHTML']=_0x33c9c5(0x11b)+_0x336327+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0x103))[_0x33c9c5(0xc7)]=_0x33c9c5(0xfa)+_0x336327+_0x33c9c5(0x111),document[_0x33c9c5(0xe7)](_0x33c9c5(0xf9))[_0x33c9c5(0xc7)]='<span\x20style=\x27color:#a8328b;\x20font-weight:bold\x27>'+_0x47f70d+'</span>';}function myFunctionOuput(){var _0x3f3f1a=_0x353efd;document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xcc))[_0x3f3f1a(0xef)]==_0x3f3f1a(0xbc)&&(document['getElementById']('wan3-isp')['disabled']=!![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x114))[_0x3f3f1a(0xe8)]=!![],document['getElementById']('wan3-nat')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById'](_0x3f3f1a(0x118))[_0x3f3f1a(0xc6)]['display']='none',document[_0x3f3f1a(0xe7)]('wan3-mangle-input')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xba))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById'](_0x3f3f1a(0xcf))[_0x3f3f1a(0xe8)]=!![],document[_0x3f3f1a(0xe7)]('wan4-isp-ip')['disabled']=!![],document[_0x3f3f1a(0xe7)]('wan4-nat')['style']['display']=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x10e))['style']['display']=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xc4))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById'](_0x3f3f1a(0xd9))['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xd8))[_0x3f3f1a(0xe8)]=!![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xf4))[_0x3f3f1a(0xe8)]=!![],document['getElementById'](_0x3f3f1a(0xbe))['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xea))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]='none',document[_0x3f3f1a(0xe7)]('wan5-mangle-input')[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0xd0),document['getElementById'](_0x3f3f1a(0xe2))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xec))[_0x3f3f1a(0xe8)]=!![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xe6))[_0x3f3f1a(0xe8)]=!![],document['getElementById'](_0x3f3f1a(0xbb))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)]('wan6-route')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById']('wan6-mangle-input')['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)]('wan6-mangle-output')['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0)),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xcc))[_0x3f3f1a(0xef)]==_0x3f3f1a(0xfd)&&(document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x105))['disabled']=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x114))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xc3))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]='block',document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x118))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)]('wan3-mangle-input')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xba))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xcf))[_0x3f3f1a(0xe8)]=!![],document['getElementById'](_0x3f3f1a(0x119))[_0x3f3f1a(0xe8)]=!![],document['getElementById']('wan4-nat')['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x10e))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xc4))['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xd9))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xd8))[_0x3f3f1a(0xe8)]=!![],document[_0x3f3f1a(0xe7)]('wan5-isp-ip')[_0x3f3f1a(0xe8)]=!![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xbe))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById'](_0x3f3f1a(0xea))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x116))['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById']('wan5-mangle-output')['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xec))[_0x3f3f1a(0xe8)]=!![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xe6))['disabled']=!![],document[_0x3f3f1a(0xe7)]('wan6-nat')['style'][_0x3f3f1a(0x10c)]='none',document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xfc))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById']('wan6-mangle-input')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById'](_0x3f3f1a(0xdd))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0)),document[_0x3f3f1a(0xe7)]('wan-list')['value']==_0x3f3f1a(0xf1)&&(document['getElementById']('wan3-isp')[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x114))['disabled']=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xc3))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x118))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xc0))['style']['display']=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xba))['style'][_0x3f3f1a(0x10c)]='block',document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xcf))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x119))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xfe))['style'][_0x3f3f1a(0x10c)]='block',document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x10e))['style']['display']=_0x3f3f1a(0x110),document['getElementById']('wan4-mangle-input')[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0x110),document['getElementById']('wan4-mangle-output')[_0x3f3f1a(0xc6)]['display']='block',document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xd8))[_0x3f3f1a(0xe8)]=!![],document[_0x3f3f1a(0xe7)]('wan5-isp-ip')[_0x3f3f1a(0xe8)]=!![],document['getElementById'](_0x3f3f1a(0xbe))['style']['display']=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xea))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)]('wan5-mangle-input')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]='none',document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xe2))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById']('wan6-isp')[_0x3f3f1a(0xe8)]=!![],document['getElementById'](_0x3f3f1a(0xe6))[_0x3f3f1a(0xe8)]=!![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xbb))['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xfc))['style']['display']=_0x3f3f1a(0xd0),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xde))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById']('wan6-mangle-output')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0)),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xcc))['value']==_0x3f3f1a(0xe4)&&(document['getElementById'](_0x3f3f1a(0x105))[_0x3f3f1a(0xe8)]=![],document['getElementById'](_0x3f3f1a(0x114))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xc3))['style']['display']='block',document['getElementById']('wan3-route')[_0x3f3f1a(0xc6)]['display']='block',document['getElementById'](_0x3f3f1a(0xc0))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)]('wan3-mangle-output')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]='block',document['getElementById'](_0x3f3f1a(0xcf))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x119))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x119))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xfe))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document['getElementById'](_0x3f3f1a(0x10e))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xc4))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]='block',document['getElementById'](_0x3f3f1a(0xd9))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xd8))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xf4))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xbe))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xea))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]='block',document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x116))['style'][_0x3f3f1a(0x10c)]='block',document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xe2))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xec))[_0x3f3f1a(0xe8)]=!![],document['getElementById'](_0x3f3f1a(0xe6))[_0x3f3f1a(0xe8)]=!![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xbb))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById']('wan6-route')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]='none',document[_0x3f3f1a(0xe7)]('wan6-mangle-input')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0xd0),document['getElementById'](_0x3f3f1a(0xdd))['style'][_0x3f3f1a(0x10c)]='none'),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xcc))[_0x3f3f1a(0xef)]=='line6'&&(document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x105))[_0x3f3f1a(0xe8)]=![],document['getElementById'](_0x3f3f1a(0x114))[_0x3f3f1a(0xe8)]=![],document['getElementById'](_0x3f3f1a(0xc3))['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x118))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0x110),document['getElementById'](_0x3f3f1a(0xc0))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xba))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]='block',document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xcf))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)]('wan4-isp-ip')[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xfe))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0x10e))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xc4))[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xd9))['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xd8))['disabled']=![],document['getElementById'](_0x3f3f1a(0xf4))['disabled']=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xbe))['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xea))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0x110),document['getElementById'](_0x3f3f1a(0x116))['style'][_0x3f3f1a(0x10c)]='block',document['getElementById']('wan5-mangle-output')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xec))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xe6))[_0x3f3f1a(0xe8)]=![],document[_0x3f3f1a(0xe7)]('wan6-nat')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)]('wan6-route')[_0x3f3f1a(0xc6)][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xde))['style'][_0x3f3f1a(0x10c)]=_0x3f3f1a(0x110),document[_0x3f3f1a(0xe7)](_0x3f3f1a(0xdd))[_0x3f3f1a(0xc6)]['display']=_0x3f3f1a(0x110));}function selectElementContents(_0x86dce){var _0x446d93=_0x353efd,_0x30dc23=document[_0x446d93(0xf0)],_0x354c15,_0x1b9bbd;if(document[_0x446d93(0xb9)]&&window[_0x446d93(0x10a)]){_0x354c15=document[_0x446d93(0xb9)](),_0x1b9bbd=window['getSelection'](),_0x1b9bbd[_0x446d93(0xd3)]();try{_0x354c15[_0x446d93(0x101)](_0x86dce),_0x1b9bbd['addRange'](_0x354c15);}catch(_0xb418c5){_0x354c15[_0x446d93(0xc9)](_0x86dce),_0x1b9bbd[_0x446d93(0xdb)](_0x354c15);}}else _0x30dc23['createTextRange']&&(_0x354c15=_0x30dc23['createTextRange'](),_0x354c15[_0x446d93(0xf3)](_0x86dce),_0x354c15[_0x446d93(0xbd)]());document['execCommand']('copy');}function ValidateIPaddressOnChange(_0x1f88a2,_0x529ccf){var _0x5b795b=_0x353efd,_0x3e50b6=/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,_0x22276b='';switch(_0x529ccf){case _0x5b795b(0xe1):_0x22276b=_0x5b795b(0xd6);break;case _0x5b795b(0xd7):_0x22276b='gateway';break;case'dns':_0x22276b=_0x5b795b(0xff);break;case _0x5b795b(0xd5):_0x22276b=_0x5b795b(0x10d);break;}!_0x1f88a2[_0x5b795b(0xef)][_0x5b795b(0xeb)](_0x3e50b6)&&(_0x1f88a2[_0x5b795b(0xcb)](),alert(_0x5b795b(0xf7)));}
	</script>
</body>
</html>
