<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Signal.org Mangle And Queue  - Mikrotik Script RouterOS</title>
<meta content='Signal.org Mangle And Queue  - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>    
</head> 
<body>
<div id="hidelink"></div>
<h1>Signal.org Mangle And Queue - Mikrotik Script RouterOS</h1>
<pre>The Signal application does not have a special port because what I know is that the port still uses secure TCP 443 and a random port for UDP, so we can simply mark it to the domain hosts * .signal.org and * .whispersystems.org For packet marking the Signal application can use tls-host or content or addlist or layer7

<code class="routeros">/ip firewall 
address-list add address=**********/12 list=IP-LAN
address-list add address=***********/16 list=IP-LAN
address-list add address=10.0.0.0/8 list=IP-LAN

/ip firewall filter 
add action=add-dst-to-address-list address-list=Signal address-list-timeout=1d chain=forward dst-address-list=!IP-LAN protocol=tcp tls-host=*.signal.org
add action=add-dst-to-address-list address-list=Signal address-list-timeout=1d chain=forward dst-address-list=!IP-LAN protocol=tcp tls-host=*.whispersystems.org

/ip firewall mangle 
add action=mark-connection chain=prerouting dst-address-list=Signal new-connection-mark=conn-Signal passthrough=yes src-address-list=IP-LAN
add action=mark-packet chain=prerouting connection-mark=conn-Signal new-packet-mark=Signal-pkt passthrough=no src-address-list=IP-LAN
action=mark-packet chain=postrouting connection-mark=conn-Signal dst-address-list=IP-LAN new-packet-mark=Signal-pkt passthrough=no

/queue simple 
add name="Signal" packet-marks=Signal-pkt queue=default/default target=10.0.0.0/8,***********/16,**********/12 total-queue=default</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
