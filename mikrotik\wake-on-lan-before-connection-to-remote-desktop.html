<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Wake On Lan Before Connection To Remote Desktop - MikroTik Script RouterOS</title>
<meta content='Wake On Lan Before Connection To Remote Desktop - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Wake On Lan Before Connection To Remote Desktop - MikroTik Script RouterOS</h1>
<pre>If you have a PC at home and you need to connect to it via RDP at random times, but don't want to spend money on electricity needed to keep the PC powered on all day long, then this solution is what you are looking for.

First, note my configuration:

First i check if there are any new connections to my forwarded RDP port:

<code class="routeros">/ip firewall nat add action=add-dst-to-address-list address-list=RDP250 chain=dstnat comment=HTPC dst-address=MY-PUBLIC-IP dst-port=MY-RDP-PORT in-interface=ether10 protocol=tcp to-addresses=********** to-ports=3389</code>
Then, i made 'Hairpin' NAT so i can reach my PC via my hostname from any location (internal network and the internet):

<code class="routeros">/ip firewall nat add action=dst-nat chain=dstnat comment=HTPC dst-address=MY-PUBLIC-IP dst-port=MY-RDP-PORT in-interface=ether10 protocol=tcp to-addresses=********** to-ports=3389
/ip firewall nat add action=masquerade chain=srcnat comment="HTPC - Hairpin" dst-address=********** dst-port=MY-RDP-PORT out-interface=bridge1 protocol=tcp src-address=10.0.0.0/24</code>
So, when I tryed to access the PC via RDP, Mikrotik added my public IP to an address list and then NATed me in. But what if the PC is in standby?

I have created this litle script to check if there any IP's on the RDP250 address list:

<code class="routeros">:foreach A in=[/ip firewall address-list find name=RDP250] do={
  if ([/ip firewall address-list get $A list]="RDP250") do={
    :log info "Sending WoL to $[/ip firewall address-list get $A address]"
    /tool wol mac=PC-MAC-ADDRESS interface=bridge1
    :log info "Removing IP $[/ip firewall address-list get $A address] from list"
    /ip firewall address-list remove $A
  }
}</code>
And a scheduler to run this script every 10 seconds.

In 99% of the cases, the RDP client connected on the first try, within 5 seconds. The worst case scenario is that you will have to try to connect twice.

Note that the script is rather small and simple and will not impact the CPU unless you have a large number of addresses on the list. So be careful when doing this. The alternative is to connect to the router and send WoL packet manualy.

Note that I don't have a static IP on my WAN interface. Instead, i update the IP via my already active ChangeIP scheduler with this extra line of code:

<code class="routeros">/ip firewall nat set [/ip firewall nat find comment=HTPC] dst-address=[:pick $ddnsip 0 [:find $ddnsip "/"] ]</code>
The full article on ChangeIP is here http://wiki.mikrotik.com/wiki/Dynamic_DNS_Update_Script_for_ChangeIP.com

Original article is http://www.plecko.com.hr/?permalink_static=123

Credit: https://wiki.mikrotik.com/wiki/Wake_on_Lan_before_connection_to_Remote_Desktop
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>