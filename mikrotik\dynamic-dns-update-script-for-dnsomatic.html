<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Dynamic DNS Update Script for DNSoMatic.com - MikroTik Script RouterOS</title>
<meta content='Dynamic DNS Update Script for DNSoMatic.com - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>

<h1>Dynamic DNS Update Script for DNSoMatic.com - MikroTik Script RouterOS</h1>
<pre>This script is a solution made of others solutions (nothing new). Much of this was adapted from the deprecated version of Dynamic DNS Update Script for DNSoMatic.com behind NAT.

The goal is to update your account on DNSoMatic.com. The main advantage of this solution is that DNSoMatic offers the possibility of propagating DNS updates to thirth party DNSlike systems like OpenDNS, DynDNS, Change IP and other 27 more.

Note: The script below is RouterOS 5.14 & 6.6 Tested!

The following permissions are required for this script to run:

- write
- test
- read
- policy (for ROS 6.0+)

<code class="routeros"># DNSoMatic automatic DNS updates
#--------------- Change Values in this section to match your setup ------------------
# User account info of DNSoMatic

:local maticuser "dnsomatic-username"
:local maticpass "dnsomatic-password"

# Set the hostname or label of network to be updated. This is the part after the double colon (::) on the DNSoMatic services page.
# Hostnames with spaces are unsupported. Replace the value in the quotations below with your host names.
# To specify multiple hosts, separate them with commas. 
# Use "all.dnsomatic.com" for the matichost to update all items in dnsomatic with this IP.

:local matichost "hostname1,hostname2"

# Change to the name of interface that gets the changing IP address

:local inetinterface "ether1-gateway"

#------------------------------------------------------------------------------------

# No more changes need

:global previousIP;

:if ([/interface get $inetinterface value-name=running]) do={
# Get the current IP on the interface
    :local currentIP [/ip address get [find interface="$inetinterface" disabled=no] address];
    
# Strip the net mask off the IP address
    :for i from=( [:len $currentIP] - 1) to=0 do={
        :if ( [:pick $currentIP $i] = "/") do={ 
            :set currentIP [:pick $currentIP 0 $i]
        } 
    }
    
    :if ($currentIP != $previousIP) do={
        :log info "DNSoMatic: Update needed"
        :set previousIP $currentIP
        
# The update URL. Note the "\3F" is hex for question mark (?). Required since ? is a special character in commands.
        :local url "http://updates.dnsomatic.com/nic/update\3Fmyip=$currentIP&wildcard=NOCHG&mx=NOCHG&backmx=NOCHG"
        :local matichostarray;
        :set matichostarray [:toarray $matichost];
        :foreach host in=$matichostarray do={
            :log info "DNSoMatic: Sending update for $host"
            /tool fetch url=($url . "&hostname=$host") user=$maticuser password=$maticpass mode=http dst-path=("dnsomaticupdate-" . $host . ".txt")
            :log info "DNSoMatic: Host $host updated on DNSoMatic with IP $currentIP"
        }
    }  else={
        :log info "DNSoMatic: Previous IP $previousIP and current IP equal, no update need"
    }
} else={
    :log info "DNSoMatic: $inetinterface is not currently running, so therefore will not update."
}</code>
This will also need you to configure scheduler entry for periodical runs (maybe every minute or so). You will probably want a second scheduler event run this script upon RouterOS startup.

If for whatever reason the update fails, the script will not update DNSoMatic until the IP address changes again. This is rare, but could happen. It would be recommended to set up a third scheduler with longer intervals (maybe 1 hour) to run a script with the following code:

<code class="routeros">:global previousIP;
:set previousIP ""

:log info "Cleared previousIP to force DNS-O-Matic update on next run."</code>
The following permissions are required for this script to run:

- write
- test (for ROS 6.0+)
- read (for ROS 6.0+)
- policy (for ROS 6.0+)

It will silently fail if it doesn't have this permission.
hjoelr 10/nov/2013

Credit: https://wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
