<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Setup And Troubleshooting Routeros Email Function - MikroTik Script RouterOS</title>
<meta content='Setup And Troubleshooting Routeros Email Function - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Setup And Troubleshooting Routeros Email Function - MikroTik Script RouterOS</h1>
<pre>Insure you have the basic router setup complete. Ip address, default route, and DNS. NTP client is recommended.

Enable logging for the email service.

<code class="routeros">/system logging add topics=e-mail action=memory</code>
This will put e-mail messages in the system log, and makes troubleshooting easier.
Go to "/tool e-mail".

Enter the email server ip. If your email service allows you to send email with a username and password, you may use the email server ip provided by your ISP. If not, use the ip of the email server that handles the domain for the destination email address. No user or password entered for this.

Enter the from address. This must be a valid format and domain. Insure you enter this from address in your address book or friends list in your email reader and provider, or it could be mistaken for spam.

Enter a username and password only if the email server requires it. If you are using the destination email address email server, leave the user and password blank

For gmail users, use the "tls=yes" setting below, and use port 587. You should resolve an ip for smtp.gmail.com locally to determine the ip to use for the server setting. Winbox will do this for you. The CLI will not. You must also go to your gmail account setup and enable POP.

Try a test email.

<code class="routeros">/tool e-mail send to=<EMAIL> subject="test" body="test"</code>
or if your email server requires a secure connection

<code class="routeros">/tool e-mail send to=<EMAIL> subject="test" body="test" tls=yes</code>
Check "/log", then your email account, for the result.

If you need to check a non-secure connection to the email server from the router:

<code class="routeros">/system telnet xx.xx.xx.xx 25</code>
Replace x's with the email server ip in "tool e-mail", and the server should respond with
220 server.domain.com ESMTP xxxxxxxxxx
Type 'quit' to terminate the telnet connection from the email server.
If the connection fails, it may be due to your ISP blocking port 25, or the server requires a secure connection.
If the connection is refused, it may be the email server not accepting email from your ip, either temporarily or permanently.
If the server responds with a domain name that is not what you would expect from that server, your ISP may be redirecting your outbound email, or the dns setting is incorrect.

Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
