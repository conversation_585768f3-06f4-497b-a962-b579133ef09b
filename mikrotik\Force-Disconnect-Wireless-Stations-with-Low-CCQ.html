<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Force Disconnect Wireless Stations with Low CCQ - MikroTik Script RouterOS</title>
<meta content='Force Disconnect Wireless Stations with Low CCQ - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Force Disconnect Wireless Stations with Low CCQ - MikroTik Script RouterOS</h1>
<pre>This script checks all registered stations and forces a disconnect for any station(s) which has a CCQ level less than that specified below (in this example it's set to 70% for TX and for RX).

The idea is that once they are forced to disconnect, the stations will attempt to reassociate with the best available AP, eliminating the 'sticky node' problem.

The only modifications you need to make is to change your minimum allowed CCQ level.

On your APs:

<code class="routeros">/system script 
add name="station-check" source="/interface \
wireless registration-table\r:foreach i in=[ /interface wireless registration-table find ap=no] \
do={\r   :if ([get \$i tx-ccq] < \"70\" && [get \$i rx-ccq] < \"70\") do={\r :log warning \
([get \$i radio-name] . \" was disconnected due to low CCQ - Tx: \" . [get \$i tx-ccq] . \"% / Rx: \" . \
[get \$i rx-ccq] . \"%\")\r /interface wireless registration-table remove \$i\r \
:delay 5s\r }\r}"</code>
Important: Remember that non-MikroTik stations will not report back their received CCQ, so the TX-CCQ will always be 0%.

Once you have configured the script, set up a scheduler to run the script everytime you want it to check for low signals. In the example below, the script will run every day at 1 second after midnight!

On your APs:

<code class="routeros">/system scheduler 
add disabled=no interval=1d name="station-check-schedule" on-event="/system script run \
   station-check ;" start-time=00:00:01</code>
Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
