/*
  Highlight.js 10.5.0 (af20048d)
  License: BSD-3-Clause
  Copyright (c) 2006-2020, <PERSON>
*/
var hljs=function(){"use strict";function e(t){
  return t instanceof Map?t.clear=t.delete=t.set=()=>{
  throw Error("map is read-only")}:t instanceof Set&&(t.add=t.clear=t.delete=()=>{
  throw Error("set is read-only")
  }),Object.freeze(t),Object.getOwnPropertyNames(t).forEach((n=>{var s=t[n]
  ;"object"!=typeof s||Object.isFrozen(s)||e(s)})),t}var t=e,n=e;t.default=n
  ;class s{constructor(e){void 0===e.data&&(e.data={}),this.data=e.data}
  ignoreMatch(){this.ignore=!0}}function r(e){
  return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")
  }function a(e,...t){const n=Object.create(null);for(const t in e)n[t]=e[t]
  ;return t.forEach((e=>{for(const t in e)n[t]=e[t]})),n}const i=e=>!!e.kind
  ;class o{constructor(e,t){
  this.buffer="",this.classPrefix=t.classPrefix,e.walk(this)}addText(e){
  this.buffer+=r(e)}openNode(e){if(!i(e))return;let t=e.kind
  ;e.sublanguage||(t=`${this.classPrefix}${t}`),this.span(t)}closeNode(e){
  i(e)&&(this.buffer+="</span>")}value(){return this.buffer}span(e){
  this.buffer+=`<span class="${e}">`}}class l{constructor(){this.rootNode={
  children:[]},this.stack=[this.rootNode]}get top(){
  return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){
  this.top.children.push(e)}openNode(e){const t={kind:e,children:[]}
  ;this.add(t),this.stack.push(t)}closeNode(){
  if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){
  for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}
  walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,t){
  return"string"==typeof t?e.addText(t):t.children&&(e.openNode(t),
  t.children.forEach((t=>this._walk(e,t))),e.closeNode(t)),e}static _collapse(e){
  "string"!=typeof e&&e.children&&(e.children.every((e=>"string"==typeof e))?e.children=[e.children.join("")]:e.children.forEach((e=>{
  l._collapse(e)})))}}class c extends l{constructor(e){super(),this.options=e}
  addKeyword(e,t){""!==e&&(this.openNode(t),this.addText(e),this.closeNode())}
  addText(e){""!==e&&this.add(e)}addSublanguage(e,t){const n=e.root
  ;n.kind=t,n.sublanguage=!0,this.add(n)}toHTML(){
  return new o(this,this.options).value()}finalize(){return!0}}function u(e){
  return e?"string"==typeof e?e:e.source:null}
  const g="[a-zA-Z]\\w*",d="[a-zA-Z_]\\w*",h="\\b\\d+(\\.\\d+)?",f="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",p="\\b(0b[01]+)",m={
  begin:"\\\\[\\s\\S]",relevance:0},b={className:"string",begin:"'",end:"'",
  illegal:"\\n",contains:[m]},x={className:"string",begin:'"',end:'"',
  illegal:"\\n",contains:[m]},E={
  begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/
  },v=(e,t,n={})=>{const s=a({className:"comment",begin:e,end:t,contains:[]},n)
  ;return s.contains.push(E),s.contains.push({className:"doctag",
  begin:"(?:TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):",relevance:0}),s
  },N=v("//","$"),w=v("/\\*","\\*/"),R=v("#","$");var y=Object.freeze({
  __proto__:null,IDENT_RE:g,UNDERSCORE_IDENT_RE:d,NUMBER_RE:h,C_NUMBER_RE:f,
  BINARY_NUMBER_RE:p,
  RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",
  SHEBANG:(e={})=>{const t=/^#![ ]*\//
  ;return e.binary&&(e.begin=((...e)=>e.map((e=>u(e))).join(""))(t,/.*\b/,e.binary,/\b.*/)),
  a({className:"meta",begin:t,end:/$/,relevance:0,"on:begin":(e,t)=>{
  0!==e.index&&t.ignoreMatch()}},e)},BACKSLASH_ESCAPE:m,APOS_STRING_MODE:b,
  QUOTE_STRING_MODE:x,PHRASAL_WORDS_MODE:E,COMMENT:v,C_LINE_COMMENT_MODE:N,
  C_BLOCK_COMMENT_MODE:w,HASH_COMMENT_MODE:R,NUMBER_MODE:{className:"number",
  begin:h,relevance:0},C_NUMBER_MODE:{className:"number",begin:f,relevance:0},
  BINARY_NUMBER_MODE:{className:"number",begin:p,relevance:0},CSS_NUMBER_MODE:{
  className:"number",
  begin:h+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",
  relevance:0},REGEXP_MODE:{begin:/(?=\/[^/\n]*\/)/,contains:[{className:"regexp",
  begin:/\//,end:/\/[gimuy]*/,illegal:/\n/,contains:[m,{begin:/\[/,end:/\]/,
  relevance:0,contains:[m]}]}]},TITLE_MODE:{className:"title",begin:g,relevance:0
  },UNDERSCORE_TITLE_MODE:{className:"title",begin:d,relevance:0},METHOD_GUARD:{
  begin:"\\.\\s*[a-zA-Z_]\\w*",relevance:0},END_SAME_AS_BEGIN:e=>Object.assign(e,{
  "on:begin":(e,t)=>{t.data._beginMatch=e[1]},"on:end":(e,t)=>{
  t.data._beginMatch!==e[1]&&t.ignoreMatch()}})});function _(e,t){
  "."===e.input[e.index-1]&&t.ignoreMatch()}function k(e,t){
  t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",
  e.__beforeBegin=_,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords)
  }function M(e,t){
  Array.isArray(e.illegal)&&(e.illegal=((...e)=>"("+e.map((e=>u(e))).join("|")+")")(...e.illegal))
  }function O(e,t){if(e.match){
  if(e.begin||e.end)throw Error("begin & end are not supported with match")
  ;e.begin=e.match,delete e.match}}function A(e,t){
  void 0===e.relevance&&(e.relevance=1)}
  const L=["of","and","for","in","not","or","if","then","parent","list","value"]
  ;function B(e,t){return t?Number(t):(e=>L.includes(e.toLowerCase()))(e)?0:1}
  function I(e,{plugins:t}){function n(t,n){
  return RegExp(u(t),"m"+(e.case_insensitive?"i":"")+(n?"g":""))}class s{
  constructor(){
  this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}
  addRule(e,t){
  t.position=this.position++,this.matchIndexes[this.matchAt]=t,this.regexes.push([t,e]),
  this.matchAt+=(e=>RegExp(e.toString()+"|").exec("").length-1)(e)+1}compile(){
  0===this.regexes.length&&(this.exec=()=>null)
  ;const e=this.regexes.map((e=>e[1]));this.matcherRe=n(((e,t="|")=>{
  const n=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;let s=0,r=""
  ;for(let a=0;a<e.length;a++){s+=1;const i=s;let o=u(e[a])
  ;for(a>0&&(r+=t),r+="(";o.length>0;){const e=n.exec(o);if(null==e){r+=o;break}
  r+=o.substring(0,e.index),
  o=o.substring(e.index+e[0].length),"\\"===e[0][0]&&e[1]?r+="\\"+(Number(e[1])+i):(r+=e[0],
  "("===e[0]&&s++)}r+=")"}return r})(e),!0),this.lastIndex=0}exec(e){
  this.matcherRe.lastIndex=this.lastIndex;const t=this.matcherRe.exec(e)
  ;if(!t)return null
  ;const n=t.findIndex(((e,t)=>t>0&&void 0!==e)),s=this.matchIndexes[n]
  ;return t.splice(0,n),Object.assign(t,s)}}class r{constructor(){
  this.rules=[],this.multiRegexes=[],
  this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){
  if(this.multiRegexes[e])return this.multiRegexes[e];const t=new s
  ;return this.rules.slice(e).forEach((([e,n])=>t.addRule(e,n))),
  t.compile(),this.multiRegexes[e]=t,t}resumingScanAtSamePosition(){
  return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(e,t){
  this.rules.push([e,t]),"begin"===t.type&&this.count++}exec(e){
  const t=this.getMatcher(this.regexIndex);t.lastIndex=this.lastIndex
  ;let n=t.exec(e)
  ;if(this.resumingScanAtSamePosition())if(n&&n.index===this.lastIndex);else{
  const t=this.getMatcher(0);t.lastIndex=this.lastIndex+1,n=t.exec(e)}
  return n&&(this.regexIndex+=n.position+1,
  this.regexIndex===this.count&&this.considerAll()),n}}
  if(e.compilerExtensions||(e.compilerExtensions=[]),
  e.contains&&e.contains.includes("self"))throw Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.")
  ;return e.classNameAliases=a(e.classNameAliases||{}),function t(s,i){const o=s
  ;if(s.compiled)return o
  ;[O].forEach((e=>e(s,i))),e.compilerExtensions.forEach((e=>e(s,i))),
  s.__beforeBegin=null,[k,M,A].forEach((e=>e(s,i))),s.compiled=!0;let l=null
  ;if("object"==typeof s.keywords&&(l=s.keywords.$pattern,
  delete s.keywords.$pattern),s.keywords&&(s.keywords=((e,t)=>{const n={}
  ;return"string"==typeof e?s("keyword",e):Object.keys(e).forEach((t=>{s(t,e[t])
  })),n;function s(e,s){t&&(s=s.toLowerCase()),s.split(" ").forEach((t=>{
  const s=t.split("|");n[s[0]]=[e,B(s[0],s[1])]}))}
  })(s.keywords,e.case_insensitive)),
  s.lexemes&&l)throw Error("ERR: Prefer `keywords.$pattern` to `mode.lexemes`, BOTH are not allowed. (see mode reference) ")
  ;return l=l||s.lexemes||/\w+/,
  o.keywordPatternRe=n(l,!0),i&&(s.begin||(s.begin=/\B|\b/),
  o.beginRe=n(s.begin),s.endSameAsBegin&&(s.end=s.begin),
  s.end||s.endsWithParent||(s.end=/\B|\b/),
  s.end&&(o.endRe=n(s.end)),o.terminatorEnd=u(s.end)||"",
  s.endsWithParent&&i.terminatorEnd&&(o.terminatorEnd+=(s.end?"|":"")+i.terminatorEnd)),
  s.illegal&&(o.illegalRe=n(s.illegal)),
  s.contains||(s.contains=[]),s.contains=[].concat(...s.contains.map((e=>(e=>(e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map((t=>a(e,{
  variants:null},t)))),e.cachedVariants?e.cachedVariants:T(e)?a(e,{
  starts:e.starts?a(e.starts):null
  }):Object.isFrozen(e)?a(e):e))("self"===e?s:e)))),s.contains.forEach((e=>{t(e,o)
  })),s.starts&&t(s.starts,i),o.matcher=(e=>{const t=new r
  ;return e.contains.forEach((e=>t.addRule(e.begin,{rule:e,type:"begin"
  }))),e.terminatorEnd&&t.addRule(e.terminatorEnd,{type:"end"
  }),e.illegal&&t.addRule(e.illegal,{type:"illegal"}),t})(o),o}(e)}function T(e){
  return!!e&&(e.endsWithParent||T(e.starts))}function j(e){const t={
  props:["language","code","autodetect"],data:()=>({detectedLanguage:"",
  unknownLanguage:!1}),computed:{className(){
  return this.unknownLanguage?"":"hljs "+this.detectedLanguage},highlighted(){
  if(!this.autoDetect&&!e.getLanguage(this.language))return console.warn(`The language "${this.language}" you specified could not be found.`),
  this.unknownLanguage=!0,r(this.code);let t={}
  ;return this.autoDetect?(t=e.highlightAuto(this.code),
  this.detectedLanguage=t.language):(t=e.highlight(this.language,this.code,this.ignoreIllegals),
  this.detectedLanguage=this.language),t.value},autoDetect(){
  return!(this.language&&(e=this.autodetect,!e&&""!==e));var e},
  ignoreIllegals:()=>!0},render(e){return e("",{},[e("code",{
  class:this.className,domProps:{innerHTML:this.highlighted}})])}};return{
  Component:t,VuePlugin:{install(e){e.component("highlightjs",t)}}}}const S={
  "after:highlightBlock":({block:e,result:t,text:n})=>{const s=D(e)
  ;if(!s.length)return;const a=document.createElement("div")
  ;a.innerHTML=t.value,t.value=((e,t,n)=>{let s=0,a="";const i=[];function o(){
  return e.length&&t.length?e[0].offset!==t[0].offset?e[0].offset<t[0].offset?e:t:"start"===t[0].event?e:t:e.length?e:t
  }function l(e){a+="<"+P(e)+[].map.call(e.attributes,(function(e){
  return" "+e.nodeName+'="'+r(e.value)+'"'})).join("")+">"}function c(e){
  a+="</"+P(e)+">"}function u(e){("start"===e.event?l:c)(e.node)}
  for(;e.length||t.length;){let t=o()
  ;if(a+=r(n.substring(s,t[0].offset)),s=t[0].offset,t===e){i.reverse().forEach(c)
  ;do{u(t.splice(0,1)[0]),t=o()}while(t===e&&t.length&&t[0].offset===s)
  ;i.reverse().forEach(l)
  }else"start"===t[0].event?i.push(t[0].node):i.pop(),u(t.splice(0,1)[0])}
  return a+r(n.substr(s))})(s,D(a),n)}};function P(e){
  return e.nodeName.toLowerCase()}function D(e){const t=[];return function e(n,s){
  for(let r=n.firstChild;r;r=r.nextSibling)3===r.nodeType?s+=r.nodeValue.length:1===r.nodeType&&(t.push({
  event:"start",offset:s,node:r}),s=e(r,s),P(r).match(/br|hr|img|input/)||t.push({
  event:"stop",offset:s,node:r}));return s}(e,0),t}const C=e=>{console.error(e)
  },H=(e,...t)=>{console.log("WARN: "+e,...t)},$=(e,t)=>{
  console.log(`Deprecated as of ${e}. ${t}`)},U=r,z=a,K=Symbol("nomatch")
  ;return(e=>{const n=Object.create(null),r=Object.create(null),a=[];let i=!0
  ;const o=/(^(<[^>]+>|\t|)+|\n)/gm,l="Could not find the language '{}', did you forget to load/include a language module?",u={
  disableAutodetect:!0,name:"Plain text",contains:[]};let g={
  noHighlightRe:/^(no-?highlight)$/i,
  languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",
  tabReplace:null,useBR:!1,languages:null,__emitter:c};function d(e){
  return g.noHighlightRe.test(e)}function h(e,t,n,s){const r={code:t,language:e}
  ;_("before:highlight",r);const a=r.result?r.result:f(r.language,r.code,n,s)
  ;return a.code=r.code,_("after:highlight",a),a}function f(e,t,r,o){const c=t
  ;function u(e,t){const n=w.case_insensitive?t[0].toLowerCase():t[0]
  ;return Object.prototype.hasOwnProperty.call(e.keywords,n)&&e.keywords[n]}
  function d(){null!=_.subLanguage?(()=>{if(""===O)return;let e=null
  ;if("string"==typeof _.subLanguage){
  if(!n[_.subLanguage])return void M.addText(O)
  ;e=f(_.subLanguage,O,!0,k[_.subLanguage]),k[_.subLanguage]=e.top
  }else e=p(O,_.subLanguage.length?_.subLanguage:null)
  ;_.relevance>0&&(A+=e.relevance),M.addSublanguage(e.emitter,e.language)
  })():(()=>{if(!_.keywords)return void M.addText(O);let e=0
  ;_.keywordPatternRe.lastIndex=0;let t=_.keywordPatternRe.exec(O),n="";for(;t;){
  n+=O.substring(e,t.index);const s=u(_,t);if(s){const[e,r]=s
  ;M.addText(n),n="",A+=r;const a=w.classNameAliases[e]||e;M.addKeyword(t[0],a)
  }else n+=t[0];e=_.keywordPatternRe.lastIndex,t=_.keywordPatternRe.exec(O)}
  n+=O.substr(e),M.addText(n)})(),O=""}function h(e){
  return e.className&&M.openNode(w.classNameAliases[e.className]||e.className),
  _=Object.create(e,{parent:{value:_}}),_}function m(e,t,n){let r=((e,t)=>{
  const n=e&&e.exec(t);return n&&0===n.index})(e.endRe,n);if(r){if(e["on:end"]){
  const n=new s(e);e["on:end"](t,n),n.ignore&&(r=!1)}if(r){
  for(;e.endsParent&&e.parent;)e=e.parent;return e}}
  if(e.endsWithParent)return m(e.parent,t,n)}function b(e){
  return 0===_.matcher.regexIndex?(O+=e[0],1):(T=!0,0)}function x(e){
  const t=e[0],n=c.substr(e.index),s=m(_,e,n);if(!s)return K;const r=_
  ;r.skip?O+=t:(r.returnEnd||r.excludeEnd||(O+=t),d(),r.excludeEnd&&(O=t));do{
  _.className&&M.closeNode(),_.skip||_.subLanguage||(A+=_.relevance),_=_.parent
  }while(_!==s.parent)
  ;return s.starts&&(s.endSameAsBegin&&(s.starts.endRe=s.endRe),
  h(s.starts)),r.returnEnd?0:t.length}let E={};function v(t,n){const a=n&&n[0]
  ;if(O+=t,null==a)return d(),0
  ;if("begin"===E.type&&"end"===n.type&&E.index===n.index&&""===a){
  if(O+=c.slice(n.index,n.index+1),!i){const t=Error("0 width match regex")
  ;throw t.languageName=e,t.badRule=E.rule,t}return 1}
  if(E=n,"begin"===n.type)return function(e){
  const t=e[0],n=e.rule,r=new s(n),a=[n.__beforeBegin,n["on:begin"]]
  ;for(const n of a)if(n&&(n(e,r),r.ignore))return b(t)
  ;return n&&n.endSameAsBegin&&(n.endRe=RegExp(t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"m")),
  n.skip?O+=t:(n.excludeBegin&&(O+=t),
  d(),n.returnBegin||n.excludeBegin||(O=t)),h(n),n.returnBegin?0:t.length}(n)
  ;if("illegal"===n.type&&!r){
  const e=Error('Illegal lexeme "'+a+'" for mode "'+(_.className||"<unnamed>")+'"')
  ;throw e.mode=_,e}if("end"===n.type){const e=x(n);if(e!==K)return e}
  if("illegal"===n.type&&""===a)return 1
  ;if(B>1e5&&B>3*n.index)throw Error("potential infinite loop, way more iterations than matches")
  ;return O+=a,a.length}const w=N(e)
  ;if(!w)throw C(l.replace("{}",e)),Error('Unknown language: "'+e+'"')
  ;const R=I(w,{plugins:a});let y="",_=o||R;const k={},M=new g.__emitter(g);(()=>{
  const e=[];for(let t=_;t!==w;t=t.parent)t.className&&e.unshift(t.className)
  ;e.forEach((e=>M.openNode(e)))})();let O="",A=0,L=0,B=0,T=!1;try{
  for(_.matcher.considerAll();;){
  B++,T?T=!1:_.matcher.considerAll(),_.matcher.lastIndex=L
  ;const e=_.matcher.exec(c);if(!e)break;const t=v(c.substring(L,e.index),e)
  ;L=e.index+t}return v(c.substr(L)),M.closeAllNodes(),M.finalize(),y=M.toHTML(),{
  relevance:A,value:y,language:e,illegal:!1,emitter:M,top:_}}catch(t){
  if(t.message&&t.message.includes("Illegal"))return{illegal:!0,illegalBy:{
  msg:t.message,context:c.slice(L-100,L+100),mode:t.mode},sofar:y,relevance:0,
  value:U(c),emitter:M};if(i)return{illegal:!1,relevance:0,value:U(c),emitter:M,
  language:e,top:_,errorRaised:t};throw t}}function p(e,t){
  t=t||g.languages||Object.keys(n);const s=(e=>{const t={relevance:0,
  emitter:new g.__emitter(g),value:U(e),illegal:!1,top:u}
  ;return t.emitter.addText(e),t})(e),r=t.filter(N).filter(R).map((t=>f(t,e,!1)))
  ;r.unshift(s);const a=r.sort(((e,t)=>{
  if(e.relevance!==t.relevance)return t.relevance-e.relevance
  ;if(e.language&&t.language){if(N(e.language).supersetOf===t.language)return 1
  ;if(N(t.language).supersetOf===e.language)return-1}return 0})),[i,o]=a,l=i
  ;return l.second_best=o,l}const m={"before:highlightBlock":({block:e})=>{
  g.useBR&&(e.innerHTML=e.innerHTML.replace(/\n/g,"").replace(/<br[ /]*>/g,"\n"))
  },"after:highlightBlock":({result:e})=>{
  g.useBR&&(e.value=e.value.replace(/\n/g,"<br>"))}},b=/^(<[^>]+>|\t)+/gm,x={
  "after:highlightBlock":({result:e})=>{
  g.tabReplace&&(e.value=e.value.replace(b,(e=>e.replace(/\t/g,g.tabReplace))))}}
  ;function E(e){let t=null;const n=(e=>{let t=e.className+" "
  ;t+=e.parentNode?e.parentNode.className:"";const n=g.languageDetectRe.exec(t)
  ;if(n){const t=N(n[1])
  ;return t||(H(l.replace("{}",n[1])),H("Falling back to no-highlight mode for this block.",e)),
  t?n[1]:"no-highlight"}return t.split(/\s+/).find((e=>d(e)||N(e)))})(e)
  ;if(d(n))return;_("before:highlightBlock",{block:e,language:n}),t=e
  ;const s=t.textContent,a=n?h(n,s,!0):p(s);_("after:highlightBlock",{block:e,
  result:a,text:s}),e.innerHTML=a.value,((e,t,n)=>{const s=t?r[t]:n
  ;e.classList.add("hljs"),s&&e.classList.add(s)})(e,n,a.language),e.result={
  language:a.language,re:a.relevance,relavance:a.relevance
  },a.second_best&&(e.second_best={language:a.second_best.language,
  re:a.second_best.relevance,relavance:a.second_best.relevance})}const v=()=>{
  v.called||(v.called=!0,document.querySelectorAll("code").forEach(E))}
  ;function N(e){return e=(e||"").toLowerCase(),n[e]||n[r[e]]}
  function w(e,{languageName:t}){"string"==typeof e&&(e=[e]),e.forEach((e=>{r[e]=t
  }))}function R(e){const t=N(e);return t&&!t.disableAutodetect}function _(e,t){
  const n=e;a.forEach((e=>{e[n]&&e[n](t)}))}Object.assign(e,{highlight:h,
  highlightAuto:p,fixMarkup:e=>{
  return $("10.2.0","fixMarkup will be removed entirely in v11.0"),
  $("10.2.0","Please see https://github.com/highlightjs/highlight.js/issues/2534"),
  t=e,
  g.tabReplace||g.useBR?t.replace(o,(e=>"\n"===e?g.useBR?"<br>":e:g.tabReplace?e.replace(/\t/g,g.tabReplace):e)):t
  ;var t},highlightBlock:E,configure:e=>{
  e.useBR&&($("10.3.0","'useBR' will be removed entirely in v11.0"),
  $("10.3.0","Please see https://github.com/highlightjs/highlight.js/issues/2559")),
  g=z(g,e)},initHighlighting:v,initHighlightingOnLoad:()=>{
  window.addEventListener("DOMContentLoaded",v,!1)},registerLanguage:(t,s)=>{
  let r=null;try{r=s(e)}catch(e){
  if(C("Language definition for '{}' could not be registered.".replace("{}",t)),
  !i)throw e;C(e),r=u}
  r.name||(r.name=t),n[t]=r,r.rawDefinition=s.bind(null,e),r.aliases&&w(r.aliases,{
  languageName:t})},listLanguages:()=>Object.keys(n),getLanguage:N,
  registerAliases:w,requireLanguage:e=>{
  $("10.4.0","requireLanguage will be removed entirely in v11."),
  $("10.4.0","Please see https://github.com/highlightjs/highlight.js/pull/2844")
  ;const t=N(e);if(t)return t
  ;throw Error("The '{}' language is required, but not loaded.".replace("{}",e))},
  autoDetection:R,inherit:z,addPlugin:e=>{a.push(e)},vuePlugin:j(e).VuePlugin
  }),e.debugMode=()=>{i=!1},e.safeMode=()=>{i=!0},e.versionString="10.5.0"
  ;for(const e in y)"object"==typeof y[e]&&t(y[e])
  ;return Object.assign(e,y),e.addPlugin(m),e.addPlugin(S),e.addPlugin(x),e})({})
  }();"object"==typeof exports&&"undefined"!=typeof module&&(module.exports=hljs);hljs.registerLanguage("routeros",(()=>{"use strict";return e=>{
  const r="foreach do while for if from to step else on-error and or not in",n="true false yes no nothing nil null",i={
  className:"variable",variants:[{begin:/\$[\w\d#@][\w\d_]*/},{begin:/\$\{(.*?)\}/
  }]},s={className:"string",begin:/"/,end:/"/,contains:[e.BACKSLASH_ESCAPE,i,{
  className:"variable",begin:/\$\(/,end:/\)/,contains:[e.BACKSLASH_ESCAPE]}]},t={
  className:"string",begin:/'/,end:/'/};return{name:"Microtik RouterOS script",
  aliases:["routeros","mikrotik"],case_insensitive:!0,keywords:{
  $pattern:/:?[\w-]+/,literal:n,
  keyword:r+" :"+r.split(" ").join(" :")+" :"+"global local beep delay put len typeof pick log time set find environment terminal error execute parse resolve toarray tobool toid toip toip6 tonum tostr totime".split(" ").join(" :")
  },contains:[{variants:[{begin:/\/\*/,end:/\*\//},{begin:/\/\//,end:/$/},{
  begin:/<\//,end:/>/}],illegal:/./},e.COMMENT("^#","$"),s,t,i,{
  begin:/[\w-]+=([^\s{}[\]()]+)/,relevance:0,returnBegin:!0,contains:[{
  className:"attribute",begin:/[^=]+/},{begin:/=/,endsWithParent:!0,relevance:0,
  contains:[s,t,i,{className:"literal",begin:"\\b("+n.split(" ").join("|")+")\\b"
  },{begin:/("[^"]*"|[^\s{}[\]]+)/}]}]},{className:"number",begin:/\*[0-9a-fA-F]+/
  },{
  begin:"\\b(add|remove|enable|disable|set|get|print|export|edit|find|run|debug|error|info|warning)([\\s[(\\]|])",
  returnBegin:!0,contains:[{className:"builtin-name",begin:/\w+/}]},{
  className:"built_in",variants:[{
  begin:"(\\.\\./|/|\\s)((traffic-flow|traffic-generator|firewall|scheduler|aaa|accounting|address-list|address|align|area|bandwidth-server|bfd|bgp|bridge|client|clock|community|config|connection|console|customer|default|dhcp-client|dhcp-server|discovery|dns|e-mail|ethernet|filter|firmware|gps|graphing|group|hardware|health|hotspot|identity|igmp-proxy|incoming|instance|interface|ip|ipsec|ipv6|irq|l2tp-server|lcd|ldp|logging|mac-server|mac-winbox|mangle|manual|mirror|mme|mpls|nat|nd|neighbor|network|note|ntp|ospf|ospf-v3|ovpn-server|page|peer|pim|ping|policy|pool|port|ppp|pppoe-client|pptp-server|prefix|profile|proposal|proxy|queue|radius|resource|rip|ripng|route|routing|screen|script|security-profiles|server|service|service-port|settings|shares|smb|sms|sniffer|snmp|snooper|socks|sstp-server|system|tool|tracking|type|upgrade|upnp|user-manager|users|user|vlan|secret|vrrp|watchdog|web-access|wireless|pptp|pppoe|lan|wan|layer7-protocol|lease|simple|raw);?\\s)+"
  },{begin:/\.\./,relevance:0}]}]}}})());
