<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Basic Traffic Priorities Rev 3.1 - MikroTik Script RouterOS</title>
<meta content='Basic Traffic Priorities Rev 3.1 - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script> 
</head> 
<body>
<div id="hidelink"></div>
<h1>Basic Traffic Priorities Rev 3.1 - MikroTik Script RouterOS</h1>
<pre>
<code class="routeros">#########################################################################################################
#  Rick Frey's Basic Traffic Priorities Rev 3.1                                                         #
#########################################################################################################
# Author: Rick Frey                                                                                     #
# email: <EMAIL>                                                                 #
# Username in MikroTik Forum is rickfrey                                                                #
#########################################################################################################
#                                         License                                                       #
# This script has been created for use by the general public and may be used freely. This script may    #
# not be sold!                                                                                          #
########################################################################################################
# Features                                                                                              #                                                                             #
# -Basic Traffic Priotization for VOIP applications                                                     #
# -WMM support (When WMM is enabled on the wireless interfaces)                                         #
# -HT AMPDU Priotization (When enabled on the Wireless interfaces)                                      #
#########################################################################################################

############################################################################################################################
#### These Mangle Rules set basic QOS Traffic Priorities for traffic passing through the router and Change the DSCP value ##
#### for certian services.##################################################################################################
############################################################################################################################

/ip firewall mangle

############################################################################################################################
#### Methods used to access the router are given the higest priority. These priorites only affect the router that these ####
#### rules reside on.                                                                                                   ####
############################################################################################################################

add chain=output comment="Section Break" disabled=yes
add action=change-dscp chain=input comment="DSCP - 7 - API Port 8728 (Local Management)" dst-port=8728 new-dscp=7 protocol=tcp
add action=change-dscp chain=input comment="DSCP - 7 - Secure Web Access Port 443 (Local Management)" dst-port=443 new-dscp=7 protocol=tcp
add action=change-dscp chain=input comment="DSCP - 7 - Web Access Port 80 (Local Management)" dst-port=80 new-dscp=7 protocol=tcp
add action=change-dscp chain=input comment="DSCP - 7 - Winbox Port 8291 (Local Management)" dst-port=8291 new-dscp=7 protocol=tcp
add action=change-dscp chain=input comment="DSCP - 7 - Telnet Port 23 (Local Management)" dst-port=23 new-dscp=7 protocol=tcp
add action=change-dscp chain=input comment="DSCP - 7 - SSH Port 22 (Local Management)" dst-port=22 new-dscp=7 protocol=tcp
add action=change-dscp chain=input comment="DSCP - 7 - FTP Port 21 (Local Management)" dst-port=21 new-dscp=7 protocol=tcp
add chain=output comment="Section Break" disabled=yes


############################################################################################################################
#### Methods used to access other devices on the network are given the higest priority. These priorites only affect the ####
#### access to other devices. Add Network Admins to the "Network Admins" address list.                                  ####
############################################################################################################################

add action=change-dscp chain=forward comment="DSCP - 7 - API Port 8728 (Remote Managemenet)" dst-port=8728 new-dscp=7 protocol=tcp src-address-list="Network Admins"
add action=change-dscp chain=forward comment="DSCP - 7 - Secure Web Access Port 443 (Remote Managemenet)" dst-port=443 new-dscp=7 protocol=tcp src-address-list="Network Admins"
add action=change-dscp chain=forward comment="DSCP - 7 - Web Access Port 80 (Remote Managemenet)" dst-port=80 new-dscp=7 protocol=tcp src-address-list="Network Admins"
add action=change-dscp chain=forward comment="DSCP - 7 - Winbox Port 8291 (Remote Managemenet)" dst-port=8291 new-dscp=7 protocol=tcp src-address-list="Network Admins"
add action=change-dscp chain=forward comment="DSCP - 7 - Telnet Port 23 (Remote Managemenet)" dst-port=23 new-dscp=7 protocol=tcp src-address-list="Network Admins"
add action=change-dscp chain=forward comment="DSCP - 7 - SSH Port 22 (Remote Managemenet)" dst-port=22 new-dscp=7 protocol=tcp src-address-list="Network Admins"
add action=change-dscp chain=forward comment="DSCP - 7 - FTP Port 21 (Remote Managemenet)" dst-port=21 new-dscp=7 protocol=tcp src-address-list="Network Admins"
add chain=output comment="Section Break" disabled=yes

/ip firewall address-list
add address=*********** list="Network Admins" disabled=yes comment=Example

############################################################################################################################
#### This section sets priorities for administrative tunneling methods. This will exclude tunneling methods for hosts.  ####
#### Add the IP address of the tunnel end point to the "Network Tunnels" address list.                                  ####
############################################################################################################################

/ip firewall mangle
add action=change-dscp chain=prerouting comment="DSCP - 6 - PPTP Port 1723 (Local Management)" new-dscp=6 port=1723 protocol=tcp src-address-list="Network Tunnels"
add action=change-dscp chain=prerouting comment="DSCP - 6 - GRE Protocol (Local Management)" new-dscp=6 protocol=gre src-address-list="Network Tunnels"
add action=change-dscp chain=prerouting comment="DSCP - 6 - L2TP UDP Port 500 (Local Management)" new-dscp=6 port=500 protocol=udp src-address-list="Network Tunnels"
add action=change-dscp chain=prerouting comment="DSCP - 6 - L2TP UDP Port 1701 (Local Management)" new-dscp=6 port=1701 protocol=udp src-address-list="Network Tunnels"
add action=change-dscp chain=prerouting comment="DSCP - 6 - L2TP UDP Port 4500 (Local Management)" new-dscp=6 port=4500 protocol=udp src-address-list="Network Tunnels"
add action=change-dscp chain=prerouting comment="DSCP - 6 - OVPN TCP Port 1194 (Local Management)" new-dscp=6 port=1194 protocol=tcp src-address-list="Network Tunnels"
add action=change-dscp chain=prerouting comment="DSCP - 5 - SSTP TCP Port 443 (Local Management)" new-dscp=5 port=443 protocol=tcp src-address-list="Network Tunnels"
add chain=output comment="Section Break" disabled=yes

/ip firewall address-list
add address=*********** list="Network Tunnels" disabled=yes comment=Example
############################################################################################################################
#### This section sets priorities for tunneling methods used by the hosts on your LAN.                                  ####
############################################################################################################################

/ip firewall mangle
add action=change-dscp chain=forward comment="DSCP - 6 - PPTP Port 1723 (LAN Traffic)" new-dscp=5 port=1723 protocol=tcp
add action=change-dscp chain=forward comment="DSCP - 6 - GRE Protocol (LAN Traffic)" new-dscp=5 protocol=gre
add action=change-dscp chain=forward comment="DSCP - 6 - L2TP UDP Port 500 (LAN Traffic)" new-dscp=5 port=500 protocol=udp
add action=change-dscp chain=forward comment="DSCP - 6 - L2TP UDP Port 1701 (LAN Traffic)" new-dscp=5 port=1701 protocol=udp
add action=change-dscp chain=forward comment="DSCP - 6 - L2TP UDP Port 4500 (LAN Traffic)" new-dscp=5 port=4500 protocol=udp
add action=change-dscp chain=forward comment="DSCP - 6 - OVPN TCP Port 1194 (LAN Traffic)" new-dscp=5 port=1194 protocol=tcp
add action=change-dscp chain=forward comment="DSCP - 5 - SSTP TCP Port 443 (LAN Traffic)" new-dscp=5 port=443 protocol=tcp
add chain=output comment="Section Break" disabled=yes


############################################################################################################################
#### This section sets priorities for VOIP Traffic                                                                      ####
############################################################################################################################

add action=change-dscp chain=postrouting comment="DSCP - 7 - Skype, HTTPS" disabled=no dst-port=443 new-dscp=7 passthrough=yes protocol=tcp
add action=change-dscp chain=postrouting comment="DSCP - 7 - VOIP" disabled=no new-dscp=7 passthrough=yes port=1167,1719,1720,8010 protocol=udp
add action=change-dscp chain=postrouting comment="DSCP - 7 - VOIP" disabled=no new-dscp=7 passthrough=yes port=1719,1720,8008,8009 protocol=tcp
add action=change-dscp chain=postrouting comment="DSCP - 7 - SIP" disabled=no new-dscp=7 passthrough=yes port=5060 protocol=tcp
add action=change-dscp chain=postrouting comment="DSCP - 7 - SIP" disabled=no new-dscp=7 passthrough=yes port=5060 protocol=udp
add action=change-dscp chain=postrouting comment="DSCP - 7 - SIP 5004" disabled=no new-dscp=7 passthrough=yes port=5004 protocol=udp
add action=change-dscp chain=postrouting comment="Priority - 7 - Ventrilo VOIP" disabled=no new-priority=7 passthrough=yes port=3784 protocol=tcp
add action=change-dscp chain=postrouting comment="Priority - 7 - Ventrilo VOIP" disabled=no new-priority=7 passthrough=yes port=3784,3785 protocol=udp
add action=change-dscp chain=postrouting comment="Priority - 7 - Windows Live Messenger Voice" disabled=no new-priority=7 passthrough=yes port=6901 protocol=tcp
add action=change-dscp chain=postrouting comment="Priority - 7 - Windows Live Messenger Voice" disabled=no new-priority=7 passthrough=yes port=6901 protocol=udp
add chain=output comment="Section Break" disabled=yes


############################################################################################################################
#### This section sets priorities for normal LAN Traffic                                                                ####
############################################################################################################################

add action=set-priority chain=prerouting comment="Priority - 6 - SSH" disabled=no new-priority=6 passthrough=yes port=22 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 6 - Telnet" disabled=no new-priority=6 passthrough=yes port=23 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 6 - ICMP" disabled=no new-priority=6 passthrough=yes protocol=icmp
add action=set-priority chain=prerouting comment="Priority - 6 - TCP DNS Requests" disabled=no new-priority=6 passthrough=yes port=53 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 6 - UDP DNS & mDNS Requests" disabled=no new-priority=6 passthrough=yes port=53,5353 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 6 - PPTP VPNs" disabled=no new-priority=6 passthrough=yes port=1723 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 6 - PPTP VPNs" disabled=no new-priority=6 passthrough=yes port=1723 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 6 - SSH" disabled=no new-priority=6 passthrough=yes port=22 protocol=udp

add action=set-priority chain=prerouting comment="Priority - 5 - HTTP Requests" connection-bytes=0-5000000 disabled=no dst-port=80 new-priority=5 passthrough=yes protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 5 - ICQ" disabled=no new-priority=5 passthrough=yes port=5190 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 5 - Yahoo IM" disabled=no new-priority=5 passthrough=yes port=5050 protocol=tcp

add action=set-priority chain=prerouting comment="Priority - 4 - AOL, IRC" disabled=no new-priority=4 passthrough=yes port=531,5190,6660-6669,6679,6697 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 4 - AOL, IRC" disabled=no new-priority=4 passthrough=yes port=531 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 4 - Time" disabled=no new-priority=4 passthrough=yes port=37 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 4 - Time" disabled=no new-priority=4 passthrough=yes port=37,123 protocol=udp

add action=set-priority chain=prerouting comment="Priority - 0 - SFTP" disabled=no dst-port=22 new-priority=0 packet-size=1400-1500 passthrough=yes protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - FTP" disabled=no dst-port=20,21 new-priority=0 packet-size=1400-1500 passthrough=yes protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - HTTP Downloads" connection-bytes=5000000-0 disabled=no new-priority=0 passthrough=yes port=80 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Mail Services" disabled=no port=110,995,143,993,25,57,109,465,587 new-priority=0 passthrough=yes protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - SNMP" disabled=no new-priority=0 passthrough=yes port=161,162 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - SNMP" disabled=no new-priority=0 passthrough=yes port=162 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - IMAP, IMAPS" disabled=no new-priority=0 passthrough=yes port=220,993 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - IMAP" disabled=no new-priority=0 passthrough=yes port=220 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Doom FPS" disabled=no new-priority=0 passthrough=yes port=666 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - America's Army MMO" disabled=no new-priority=0 passthrough=yes port=1716 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Civilization MMO" disabled=no new-priority=0 passthrough=yes port=2056 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Halo: Combat Evolved MMO" disabled=no new-priority=0 passthrough=yes port=2302 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Dark Ages" disabled=no port=2610 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Xbox Live" disabled=no new-priority=0 passthrough=yes port=3074 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Xbox Live" disabled=no new-priority=0 passthrough=yes port=3074 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Blizzard Games Online" disabled=no new-priority=0 passthrough=yes port=3723,6112 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Blizzard Games Online" disabled=no new-priority=0 passthrough=yes port=3723 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - WoW MMO" disabled=no new-priority=0 passthrough=yes port=3724 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - WoW MMO" disabled=no new-priority=0 passthrough=yes port=3724 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Club Penguin Disney Online" disabled=no new-priority=0 passthrough=yes port=3724,6112,6113,9875 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Diablo II" disabled=no new-priority=0 passthrough=yes port=4000 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Diablo II" disabled=no new-priority=0 passthrough=yes port=4000 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Microsoft Ants MMO" disabled=no new-priority=0 passthrough=yes port=4001 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Google Desktop" disabled=no new-priority=0 passthrough=yes port=4664 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - BZFlag" disabled=no new-priority=0 passthrough=yes port=5154 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - BZFlag" disabled=no new-priority=0 passthrough=yes port=5154 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Freeciv MMO" disabled=no new-priority=0 passthrough=yes port=5556 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Freeciv MMO" disabled=no new-priority=0 passthrough=yes port=5556 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Windows Live Messenger File Transfer" disabled=no new-priority=0 passthrough=yes port=6891-6900 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Enemy Territory: Quake Wars" disabled=no new-priority=0 passthrough=yes port=7133 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Teamspeak" disabled=no new-priority=0 passthrough=yes port=8767-8768 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Teamspeak" disabled=no new-priority=0 passthrough=yes port=9987 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Earthland Relams 2" disabled=no new-priority=0 passthrough=yes port=8888-8889 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Sony Playstation" disabled=no new-priority=0 passthrough=yes port=9293 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Battlefield 1942 MMO" disabled=no new-priority=0 passthrough=yes port=14567 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Battlefield Vietnam" disabled=no new-priority=0 passthrough=yes port=15567 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Battlefield 2" disabled=no new-priority=0 passthrough=yes port=16567 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Quake" disabled=no new-priority=0 passthrough=yes port=26000 protocol=tcp
add action=set-priority chain=prerouting comment="Priority - 0 - Quake" disabled=no new-priority=0 passthrough=yes port=26000,27901,27960 protocol=udp
add action=set-priority chain=prerouting comment="Priority - 0 - Call of Duty" disabled=no new-priority=0 passthrough=yes port=28960 protocol=udp
add chain=output comment="Section Break" disabled=yes


############################################################################################################################
#### This section sets priorities for Layer 7 Traffic Matching. This can be extremely CPU intensive!!!                  ####
############################################################################################################################

#########################################
#### P2p                             ####
#########################################

/ip firewall layer7-protocol
add name=edonkey regexp="^[\C5\D4\E3-\E5].\?.\?.\?.\?([\01\02\05\14\15\16\18\
    \19\1A\1B\1C !234568@ABCFGHIJKLMNOPQRSTUVWX[`\81\82\90\91\93\96\97\98\99\
    \9A\9B\9C\9E\A0\A1\A2\A3\A4]|Y................\?[ -~]|\96....\$)"
add name=goboogy regexp="<peerplat>|^get /getfilebyhash\\.cgi\\\?|^get /queue_\
    register\\.cgi\\\?|^get /getupdowninfo\\.cgi\\\?"
add name=soribada regexp="^GETMP3\r\
    \nFilename|^\01.\?.\?.\?(Q:\\+|Q2:)|^\10[\14-\16]\10[\15-\17].\?.\?.\?.\?\
    \$"
add name=gnutella regexp="^(gnd[\01\02]\?.\?.\?\01|gnutella connect/[012]\\.[0\
    -9]\r\
    \n|get /uri-res/n2r\\\?urn:sha1:|get /.*user-agent: (gtk-gnutella|bearshar\
    e|mactella|gnucleus|gnotella|limewire|imesh)|get /.*content-type: applicat\
    ion/x-gnutella-packets|giv [0-9]*:[0-9a-f]*/|queue [0-9a-f]* [1-9][0-9]\?[\
    0-9]\?\\.[1-9][0-9]\?[0-9]\?\\.[1-9][0-9]\?[0-9]\?\\.[1-9][0-9]\?[0-9]\?:[\
    1-9][0-9]\?[0-9]\?[0-9]\?|gnutella.*content-type: application/x-gnutella|.\
    ..................\?lime)"
add name=poco regexp="^\80\94\
    \n\01....\1F\9E"
add name=applejuice regexp="^ajprot\r\
    \n"
add name=mute regexp="^(Public|AES)Key: [0-9a-f]*\
    \nEnd(Public|AES)Key\
    \n\$"
add name=bittorrent regexp="^(\13bittorrent protocol|azver\01\$|get /scrape\\\
    \?info_hash=)|d1:ad2:id20:|\08'7P\\)[RP]"
add name=directconnect regexp="^(\\\$mynick |\\\$lock |\\\$key )"
add name=hotline regexp="^....................TRTPHOTL\01\02"
add name=kugoo regexp="^1..\8E"
add name=ares regexp="^\03[]Z].\?.\?\05\$"
add name=fasttrack regexp="^get (/.download/[ -~]*|/.supernode[ -~]|/.status[ \
    -~]|/.network[ -~]*|/.files|/.hash=[0-9a-f]*/[ -~]*) http/1.1|user-agent: \
    kazaa|x-kazaa(-username|-network|-ip|-supernodeip|-xferid|-xferuid|tag)|^g\
    ive [0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]\?[0-9]\?[0-9]\?"
add name=100bao regexp="^\01\01\05\
    \n"
add name=gnucleuslan regexp=\
    "gnuclear connect/[\t-\r -~]*user-agent: gnucleus [\t-\r -~]*lan:"
add name=tesla regexp="\03\9A\89\"111\\.00 Beta |\E2<i\1E\1C\E9"
add name=openft regexp="x-openftalias: [-)(0-9a-z ~.]"
add name=napster regexp="^(.[\02\06][!-~]+ [!-~]+ [0-9][0-9]\?[0-9]\?[0-9]\?[0\
    -9]\? \"[\t-\r -~]+\" ([0-9]|10)|1(send|get)[!-~]+ \"[\t-\r -~]+\")"
add name=soulseek regexp="^(\05..\?|.\01.[ -~]+\01F..\?.\?.\?.\?.\?.\?.\?)\$"
add name=xunlei regexp="^[()]...\?.\?.\?(reg|get|query)"
add name=thecircle regexp=\
    "^t\03ni.\?[\01-\06]\?t[\01-\05]s[\
    \n\0B](glob|who are you\$|query data)"
add name=imesh regexp="^(post[\t-\r -~]*<PasswordHash>........................\
    ........</PasswordHash><ClientVer>|4\80\?\r\?\FC\FF\04|get[\t-\r -~]*Host:\
    \_imsh\\.download-prod\\.musicnet\\.com|\02(\01|\02)\83.\?.\?.\?.\?.\?.\?.\
    \?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?\02(\01|\
    \02)\83)"

/ip firewall mangle
add action=set-priority chain=forward comment="Priority - 0 - File Sharing" p2p=all-p2p
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - 100bao" layer7-protocol=100bao
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Applejuice" layer7-protocol=applejuice
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Ares" layer7-protocol=ares
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Bittorrent" layer7-protocol=bittorrent
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Direct Connect" layer7-protocol=directconnect
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - eDonkey" layer7-protocol=edonkey
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Fasttrack" layer7-protocol=fasttrack
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - gnucleuslan" layer7-protocol=gnucleuslan
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - gnutella" layer7-protocol=gnutella
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Goboogy" layer7-protocol=goboogy
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Hotline" layer7-protocol=hotline
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - iMesh" layer7-protocol=imesh
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Kugoo" layer7-protocol=kugoo
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Mute" layer7-protocol=mute
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Napster" layer7-protocol=napster
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - OpenFT" layer7-protocol=openft
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Poco" layer7-protocol=poco
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Soribada" layer7-protocol=soribada
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Soulseek" layer7-protocol=soulseek
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Tesla" layer7-protocol=tesla
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - The Circle" layer7-protocol=thecircle
add action=set-priority chain=forward comment="Priority - 0 - File Sharing - Xunlei" layer7-protocol=xunlei
add chain=output comment="Section Break" disabled=yes

#########################################
#### VOIP                            ####
#########################################

/ip firewall layer7-protocol
add name=sip regexp=\
    "^(invite|register|cancel) sip[\t-\r -~]*sip/[0-2]\\.[0-9]"
add name=h323 regexp=\
    "^\03..\?\08...\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?.\?\05"
add name=skypeout regexp="^(\01.\?.\?.\?.\?.\?.\?.\?.\?\01|\02.\?.\?.\?.\?.\?.\
    \?.\?.\?\02|\03.\?.\?.\?.\?.\?.\?.\?.\?\03|\04.\?.\?.\?.\?.\?.\?.\?.\?\04|\
    \05.\?.\?.\?.\?.\?.\?.\?.\?\05|\06.\?.\?.\?.\?.\?.\?.\?.\?\06|\07.\?.\?.\?\
    .\?.\?.\?.\?.\?\07|\08.\?.\?.\?.\?.\?.\?.\?.\?\08|\t.\?.\?.\?.\?.\?.\?.\?.\
    \?\t|\
    \n.\?.\?.\?.\?.\?.\?.\?.\?\
    \n|\0B.\?.\?.\?.\?.\?.\?.\?.\?\0B|\0C.\?.\?.\?.\?.\?.\?.\?.\?\0C|\r.\?.\?.\
    \?.\?.\?.\?.\?.\?\r|\0E.\?.\?.\?.\?.\?.\?.\?.\?\0E|\0F.\?.\?.\?.\?.\?.\?.\
    \?.\?\0F|\10.\?.\?.\?.\?.\?.\?.\?.\?\10|\11.\?.\?.\?.\?.\?.\?.\?.\?\11|\12\
    .\?.\?.\?.\?.\?.\?.\?.\?\12|\13.\?.\?.\?.\?.\?.\?.\?.\?\13|\14.\?.\?.\?.\?\
    .\?.\?.\?.\?\14|\15.\?.\?.\?.\?.\?.\?.\?.\?\15|\16.\?.\?.\?.\?.\?.\?.\?.\?\
    \16|\17.\?.\?.\?.\?.\?.\?.\?.\?\17|\18.\?.\?.\?.\?.\?.\?.\?.\?\18|\19.\?.\
    \?.\?.\?.\?.\?.\?.\?\19|\1A.\?.\?.\?.\?.\?.\?.\?.\?\1A|\1B.\?.\?.\?.\?.\?.\
    \?.\?.\?\1B|\1C.\?.\?.\?.\?.\?.\?.\?.\?\1C|\1D.\?.\?.\?.\?.\?.\?.\?.\?\1D|\
    \1E.\?.\?.\?.\?.\?.\?.\?.\?\1E|\1F.\?.\?.\?.\?.\?.\?.\?.\?\1F| .\?.\?.\?.\
    \?.\?.\?.\?.\? |!.\?.\?.\?.\?.\?.\?.\?.\?!|\".\?.\?.\?.\?.\?.\?.\?.\?\"|#.\
    \?.\?.\?.\?.\?.\?.\?.\?#|\\\$.\?.\?.\?.\?.\?.\?.\?.\?\\\$|%.\?.\?.\?.\?.\?\
    .\?.\?.\?%|&.\?.\?.\?.\?.\?.\?.\?.\?&|'.\?.\?.\?.\?.\?.\?.\?.\?'|\\(.\?.\?\
    .\?.\?.\?.\?.\?.\?\\(|\\).\?.\?.\?.\?.\?.\?.\?.\?\\)|\\*.\?.\?.\?.\?.\?.\?\
    .\?.\?\\*|\\+.\?.\?.\?.\?.\?.\?.\?.\?\\+|,.\?.\?.\?.\?.\?.\?.\?.\?,|-.\?.\
    \?.\?.\?.\?.\?.\?.\?-|\\..\?.\?.\?.\?.\?.\?.\?.\?\\.|/.\?.\?.\?.\?.\?.\?.\
    \?.\?/|0.\?.\?.\?.\?.\?.\?.\?.\?0|1.\?.\?.\?.\?.\?.\?.\?.\?1|2.\?.\?.\?.\?\
    .\?.\?.\?.\?2|3.\?.\?.\?.\?.\?.\?.\?.\?3|4.\?.\?.\?.\?.\?.\?.\?.\?4|5.\?.\
    \?.\?.\?.\?.\?.\?.\?5|6.\?.\?.\?.\?.\?.\?.\?.\?6|7.\?.\?.\?.\?.\?.\?.\?.\?\
    7|8.\?.\?.\?.\?.\?.\?.\?.\?8|9.\?.\?.\?.\?.\?.\?.\?.\?9|:.\?.\?.\?.\?.\?.\
    \?.\?.\?:|;.\?.\?.\?.\?.\?.\?.\?.\?;|<.\?.\?.\?.\?.\?.\?.\?.\?<|=.\?.\?.\?\
    .\?.\?.\?.\?.\?=|>.\?.\?.\?.\?.\?.\?.\?.\?>|\\\?.\?.\?.\?.\?.\?.\?.\?.\?\\\
    \?|@.\?.\?.\?.\?.\?.\?.\?.\?@|A.\?.\?.\?.\?.\?.\?.\?.\?A|B.\?.\?.\?.\?.\?.\
    \?.\?.\?B|C.\?.\?.\?.\?.\?.\?.\?.\?C|D.\?.\?.\?.\?.\?.\?.\?.\?D|E.\?.\?.\?\
    .\?.\?.\?.\?.\?E|F.\?.\?.\?.\?.\?.\?.\?.\?F|G.\?.\?.\?.\?.\?.\?.\?.\?G|H.\
    \?.\?.\?.\?.\?.\?.\?.\?H|I.\?.\?.\?.\?.\?.\?.\?.\?I|J.\?.\?.\?.\?.\?.\?.\?\
    .\?J|K.\?.\?.\?.\?.\?.\?.\?.\?K|L.\?.\?.\?.\?.\?.\?.\?.\?L|M.\?.\?.\?.\?.\
    \?.\?.\?.\?M|N.\?.\?.\?.\?.\?.\?.\?.\?N|O.\?.\?.\?.\?.\?.\?.\?.\?O|P.\?.\?\
    .\?.\?.\?.\?.\?.\?P|Q.\?.\?.\?.\?.\?.\?.\?.\?Q|R.\?.\?.\?.\?.\?.\?.\?.\?R|\
    S.\?.\?.\?.\?.\?.\?.\?.\?S|T.\?.\?.\?.\?.\?.\?.\?.\?T|U.\?.\?.\?.\?.\?.\?.\
    \?.\?U|V.\?.\?.\?.\?.\?.\?.\?.\?V|W.\?.\?.\?.\?.\?.\?.\?.\?W|X.\?.\?.\?.\?\
    .\?.\?.\?.\?X|Y.\?.\?.\?.\?.\?.\?.\?.\?Y|Z.\?.\?.\?.\?.\?.\?.\?.\?Z|\\[.\?\
    .\?.\?.\?.\?.\?.\?.\?\\[|\\].\?.\?.\?.\?.\?.\?.\?.\?\\]|\\].\?.\?.\?.\?.\?\
    .\?.\?.\?\\]|\\^.\?.\?.\?.\?.\?.\?.\?.\?\\^|_.\?.\?.\?.\?.\?.\?.\?.\?_|`.\
    \?.\?.\?.\?.\?.\?.\?.\?`|a.\?.\?.\?.\?.\?.\?.\?.\?a|b.\?.\?.\?.\?.\?.\?.\?\
    .\?b|c.\?.\?.\?.\?.\?.\?.\?.\?c|d.\?.\?.\?.\?.\?.\?.\?.\?d|e.\?.\?.\?.\?.\
    \?.\?.\?.\?e|f.\?.\?.\?.\?.\?.\?.\?.\?f|g.\?.\?.\?.\?.\?.\?.\?.\?g|h.\?.\?\
    .\?.\?.\?.\?.\?.\?h|i.\?.\?.\?.\?.\?.\?.\?.\?i|j.\?.\?.\?.\?.\?.\?.\?.\?j|\
    k.\?.\?.\?.\?.\?.\?.\?.\?k|l.\?.\?.\?.\?.\?.\?.\?.\?l|m.\?.\?.\?.\?.\?.\?.\
    \?.\?m|n.\?.\?.\?.\?.\?.\?.\?.\?n|o.\?.\?.\?.\?.\?.\?.\?.\?o|p.\?.\?.\?.\?\
    .\?.\?.\?.\?p|q.\?.\?.\?.\?.\?.\?.\?.\?q|r.\?.\?.\?.\?.\?.\?.\?.\?r|s.\?.\
    \?.\?.\?.\?.\?.\?.\?s|t.\?.\?.\?.\?.\?.\?.\?.\?t|u.\?.\?.\?.\?.\?.\?.\?.\?\
    u|v.\?.\?.\?.\?.\?.\?.\?.\?v|w.\?.\?.\?.\?.\?.\?.\?.\?w|x.\?.\?.\?.\?.\?.\
    \?.\?.\?x|y.\?.\?.\?.\?.\?.\?.\?.\?y|z.\?.\?.\?.\?.\?.\?.\?.\?z|\\{.\?.\?.\
    \?.\?.\?.\?.\?.\?\\{|\\|.\?.\?.\?.\?.\?.\?.\?.\?\\||\\}.\?.\?.\?.\?.\?.\?.\
    \?.\?\\}|~.\?.\?.\?.\?.\?.\?.\?.\?~|\7F.\?.\?.\?.\?.\?.\?.\?.\?\7F|\80.\?.\
    \?.\?.\?.\?.\?.\?.\?\80|\81.\?.\?.\?.\?.\?.\?.\?.\?\81|\82.\?.\?.\?.\?.\?.\
    \?.\?.\?\82|\83.\?.\?.\?.\?.\?.\?.\?.\?\83|\84.\?.\?.\?.\?.\?.\?.\?.\?\84|\
    \85.\?.\?.\?.\?.\?.\?.\?.\?\85|\86.\?.\?.\?.\?.\?.\?.\?.\?\86|\87.\?.\?.\?\
    .\?.\?.\?.\?.\?\87|\88.\?.\?.\?.\?.\?.\?.\?.\?\88|\89.\?.\?.\?.\?.\?.\?.\?\
    .\?\89|\8A.\?.\?.\?.\?.\?.\?.\?.\?\8A|\8B.\?.\?.\?.\?.\?.\?.\?.\?\8B|\8C.\
    \?.\?.\?.\?.\?.\?.\?.\?\8C|\8D.\?.\?.\?.\?.\?.\?.\?.\?\8D|\8E.\?.\?.\?.\?.\
    \?.\?.\?.\?\8E|\8F.\?.\?.\?.\?.\?.\?.\?.\?\8F|\90.\?.\?.\?.\?.\?.\?.\?.\?\
    \90|\91.\?.\?.\?.\?.\?.\?.\?.\?\91|\92.\?.\?.\?.\?.\?.\?.\?.\?\92|\93.\?.\
    \?.\?.\?.\?.\?.\?.\?\93|\94.\?.\?.\?.\?.\?.\?.\?.\?\94|\95.\?.\?.\?.\?.\?.\
    \?.\?.\?\95|\96.\?.\?.\?.\?.\?.\?.\?.\?\96|\97.\?.\?.\?.\?.\?.\?.\?.\?\97|\
    \98.\?.\?.\?.\?.\?.\?.\?.\?\98|\99.\?.\?.\?.\?.\?.\?.\?.\?\99|\9A.\?.\?.\?\
    .\?.\?.\?.\?.\?\9A|\9B.\?.\?.\?.\?.\?.\?.\?.\?\9B|\9C.\?.\?.\?.\?.\?.\?.\?\
    .\?\9C|\9D.\?.\?.\?.\?.\?.\?.\?.\?\9D|\9E.\?.\?.\?.\?.\?.\?.\?.\?\9E|\9F.\
    \?.\?.\?.\?.\?.\?.\?.\?\9F|\A0.\?.\?.\?.\?.\?.\?.\?.\?\A0|\A1.\?.\?.\?.\?.\
    \?.\?.\?.\?\A1|\A2.\?.\?.\?.\?.\?.\?.\?.\?\A2|\A3.\?.\?.\?.\?.\?.\?.\?.\?\
    \A3|\A4.\?.\?.\?.\?.\?.\?.\?.\?\A4|\A5.\?.\?.\?.\?.\?.\?.\?.\?\A5|\A6.\?.\
    \?.\?.\?.\?.\?.\?.\?\A6|\A7.\?.\?.\?.\?.\?.\?.\?.\?\A7|\A8.\?.\?.\?.\?.\?.\
    \?.\?.\?\A8|\A9.\?.\?.\?.\?.\?.\?.\?.\?\A9|\AA.\?.\?.\?.\?.\?.\?.\?.\?\AA|\
    \AB.\?.\?.\?.\?.\?.\?.\?.\?\AB|\AC.\?.\?.\?.\?.\?.\?.\?.\?\AC|\AD.\?.\?.\?\
    .\?.\?.\?.\?.\?\AD|\AE.\?.\?.\?.\?.\?.\?.\?.\?\AE|\AF.\?.\?.\?.\?.\?.\?.\?\
    .\?\AF|\B0.\?.\?.\?.\?.\?.\?.\?.\?\B0|\B1.\?.\?.\?.\?.\?.\?.\?.\?\B1|\B2.\
    \?.\?.\?.\?.\?.\?.\?.\?\B2|\B3.\?.\?.\?.\?.\?.\?.\?.\?\B3|\B4.\?.\?.\?.\?.\
    \?.\?.\?.\?\B4|\B5.\?.\?.\?.\?.\?.\?.\?.\?\B5|\B6.\?.\?.\?.\?.\?.\?.\?.\?\
    \B6|\B7.\?.\?.\?.\?.\?.\?.\?.\?\B7|\B8.\?.\?.\?.\?.\?.\?.\?.\?\B8|\B9.\?.\
    \?.\?.\?.\?.\?.\?.\?\B9|\BA.\?.\?.\?.\?.\?.\?.\?.\?\BA|\BB.\?.\?.\?.\?.\?.\
    \?.\?.\?\BB|\BC.\?.\?.\?.\?.\?.\?.\?.\?\BC|\BD.\?.\?.\?.\?.\?.\?.\?.\?\BD|\
    \BE.\?.\?.\?.\?.\?.\?.\?.\?\BE|\BF.\?.\?.\?.\?.\?.\?.\?.\?\BF|\C0.\?.\?.\?\
    .\?.\?.\?.\?.\?\C0|\C1.\?.\?.\?.\?.\?.\?.\?.\?\C1|\C2.\?.\?.\?.\?.\?.\?.\?\
    .\?\C2|\C3.\?.\?.\?.\?.\?.\?.\?.\?\C3|\C4.\?.\?.\?.\?.\?.\?.\?.\?\C4|\C5.\
    \?.\?.\?.\?.\?.\?.\?.\?\C5|\C6.\?.\?.\?.\?.\?.\?.\?.\?\C6|\C7.\?.\?.\?.\?.\
    \?.\?.\?.\?\C7|\C8.\?.\?.\?.\?.\?.\?.\?.\?\C8|\C9.\?.\?.\?.\?.\?.\?.\?.\?\
    \C9|\CA.\?.\?.\?.\?.\?.\?.\?.\?\CA|\CB.\?.\?.\?.\?.\?.\?.\?.\?\CB|\CC.\?.\
    \?.\?.\?.\?.\?.\?.\?\CC|\CD.\?.\?.\?.\?.\?.\?.\?.\?\CD|\CE.\?.\?.\?.\?.\?.\
    \?.\?.\?\CE|\CF.\?.\?.\?.\?.\?.\?.\?.\?\CF|\D0.\?.\?.\?.\?.\?.\?.\?.\?\D0|\
    \D1.\?.\?.\?.\?.\?.\?.\?.\?\D1|\D2.\?.\?.\?.\?.\?.\?.\?.\?\D2|\D3.\?.\?.\?\
    .\?.\?.\?.\?.\?\D3|\D4.\?.\?.\?.\?.\?.\?.\?.\?\D4|\D5.\?.\?.\?.\?.\?.\?.\?\
    .\?\D5|\D6.\?.\?.\?.\?.\?.\?.\?.\?\D6|\D7.\?.\?.\?.\?.\?.\?.\?.\?\D7|\D8.\
    \?.\?.\?.\?.\?.\?.\?.\?\D8|\D9.\?.\?.\?.\?.\?.\?.\?.\?\D9|\DA.\?.\?.\?.\?.\
    \?.\?.\?.\?\DA|\DB.\?.\?.\?.\?.\?.\?.\?.\?\DB|\DC.\?.\?.\?.\?.\?.\?.\?.\?\
    \DC|\DD.\?.\?.\?.\?.\?.\?.\?.\?\DD|\DE.\?.\?.\?.\?.\?.\?.\?.\?\DE|\DF.\?.\
    \?.\?.\?.\?.\?.\?.\?\DF|\E0.\?.\?.\?.\?.\?.\?.\?.\?\E0|\E1.\?.\?.\?.\?.\?.\
    \?.\?.\?\E1|\E2.\?.\?.\?.\?.\?.\?.\?.\?\E2|\E3.\?.\?.\?.\?.\?.\?.\?.\?\E3|\
    \E4.\?.\?.\?.\?.\?.\?.\?.\?\E4|\E5.\?.\?.\?.\?.\?.\?.\?.\?\E5|\E6.\?.\?.\?\
    .\?.\?.\?.\?.\?\E6|\E7.\?.\?.\?.\?.\?.\?.\?.\?\E7|\E8.\?.\?.\?.\?.\?.\?.\?\
    .\?\E8|\E9.\?.\?.\?.\?.\?.\?.\?.\?\E9|\EA.\?.\?.\?.\?.\?.\?.\?.\?\EA|\EB.\
    \?.\?.\?.\?.\?.\?.\?.\?\EB|\EC.\?.\?.\?.\?.\?.\?.\?.\?\EC|\ED.\?.\?.\?.\?.\
    \?.\?.\?.\?\ED|\EE.\?.\?.\?.\?.\?.\?.\?.\?\EE|\EF.\?.\?.\?.\?.\?.\?.\?.\?\
    \EF|\F0.\?.\?.\?.\?.\?.\?.\?.\?\F0|\F1.\?.\?.\?.\?.\?.\?.\?.\?\F1|\F2.\?.\
    \?.\?.\?.\?.\?.\?.\?\F2|\F3.\?.\?.\?.\?.\?.\?.\?.\?\F3|\F4.\?.\?.\?.\?.\?.\
    \?.\?.\?\F4|\F5.\?.\?.\?.\?.\?.\?.\?.\?\F5|\F6.\?.\?.\?.\?.\?.\?.\?.\?\F6|\
    \F7.\?.\?.\?.\?.\?.\?.\?.\?\F7|\F8.\?.\?.\?.\?.\?.\?.\?.\?\F8|\F9.\?.\?.\?\
    .\?.\?.\?.\?.\?\F9|\FA.\?.\?.\?.\?.\?.\?.\?.\?\FA|\FB.\?.\?.\?.\?.\?.\?.\?\
    .\?\FB|\FC.\?.\?.\?.\?.\?.\?.\?.\?\FC|\FD.\?.\?.\?.\?.\?.\?.\?.\?\FD|\FE.\
    \?.\?.\?.\?.\?.\?.\?.\?\FE|\FF.\?.\?.\?.\?.\?.\?.\?.\?\FF)"
add name=skypetoskype regexp="^..\02............."
add name=teamspeak regexp="^\F4\BE\03.*teamspeak"
add name=ventrilo regexp="^..\?v\\\$\CF"
add name=stun regexp="^[\01\02]................\?\$"

/ip firewall mangle
add action=set-priority chain=forward comment="Priority - 7 - VOIP - h323" layer7-protocol=h323 new-priority=7
add action=set-priority chain=forward comment="Priority - 7 - VOIP - SIP" layer7-protocol=sip new-priority=7
add action=set-priority chain=forward comment="Priority - 7 - VOIP - Skypeout" layer7-protocol=skypeout new-priority=7
add action=set-priority chain=forward comment="Priority - 7 - VOIP - skypetoskype" layer7-protocol=skypetoskype new-priority=7
add action=set-priority chain=forward comment="Priority - 7 - VOIP - STUN" layer7-protocol=stun new-priority=7
add action=set-priority chain=forward comment="Priority - 7 - VOIP - Teamspeak" layer7-protocol=teamspeak new-priority=7
add action=set-priority chain=forward comment="Priority - 7 - VOIP - Ventrilo" layer7-protocol=ventrilo new-priority=7
</code>
Credit: https://rickfreyconsulting.com/basic-traffic-priorities-rev-3-1/
</pre>
<br>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
