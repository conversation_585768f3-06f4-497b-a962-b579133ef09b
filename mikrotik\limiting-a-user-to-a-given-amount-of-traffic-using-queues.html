<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Limiting A User To A Given Amount Of Traffic Using Queues - MikroTik Script RouterOS</title>
<meta content='Limiting A User To A Given Amount Of Traffic Using Queues - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Limiting A User To A Given Amount Of Traffic Using Queues - MikroTik Script RouterOS</h1>
<pre>I have users that never spend 100 mb daily but need the good speed, And i have users that would download the whole internet in a day if they could. This way 'easy' users will have good speed for browsing , chatting with camera, and some small downloads. And the 'bandwidth hungry' users will suffer slower speeds after they cross their 100MB during the day. But, thats not that bad , users can download during the night hours 1am-10am when I disable this script and allow higher speed, because the network is not at peak usage.

For the others information here is the complete script that is tested and works.

<code class="routeros">:local traf;
/queue simple
  :for i from=1 to= 254 do =  {
    :if ([/queue simple find target-addresses=("192.168.1." . $i)] != "") do={
      :set traf [get [find target-addresses=("192.168.1." . $i)] total-bytes]
      :if ($traf  > 104857600) do = {
        set [find target-addresses=("192.168.1." . $i)] max-limit= 32000/64000
      }
    }
  }</code>
This script will check all the simple queues shearching their target adresses and will search a whole ***********/24 subnet. It will read their total-bytes (recieved transmited) and will check them if they crosed a limit of 100 MB ( 104857600 bytes) If they did , they will be limited to lower speed (64k/64k)

You can change the subnet, the limit, and the lower speed acording to your needs.

It shoud be added a scheduler that will run this script every 5 minutes or so. Another scheduler to reset counters daily " /que simple reset-counters".

Limitations

If the MT box is reset the traffic counters will be reset and the speed will return to normal (e.g. power outage) -- someone confirm this?
If a similiar script were to be applied to a pppoe style connection, a simple disconnection by the user would reset the traffic counters.

Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>


