<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Sync Address List with DNS Cache - MikroTik Script RouterOS</title>
<meta content='Sync Address List with DNS Cache - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Sync Address List with DNS Cache - MikroTik Script RouterOS</h1>
<pre>This script will update an address list from a given DNS cache search. This address list can then be used for example in Firewall settings.

<code class="routeros"># Sync an address list with DNS cache
# Version: 1.0.0
# This script currently only works with type A and non-static DNS records.
# If no matching DNS records are found:
# the address list is removed.
# If matching DNS records are found:
# if an address list entry is NOT found in search result, it is removed.
# if an address list entry IS found in search result, is not modified.
# if a search result is NOT found in address list, it is added.
#
# Search string format:
# "(:|=|~)<search string>"
# :   match string anywhere
# =  match string exactly (case sensitive)
# ~  match string using regular expression (requires ROS >= v3.23)
#
# Example:  To search all records containing 'google.com':
# :local search ":google.com"
#
# To search all records ending with 'microsoft.com':
# :local search "~microsoft\\.com\$"
:local search ":google.com"
# The prefix of address list ("" = none)
:local listprefix "DNSCache_"
# Internal processing...
:local IPs ""
:local Names ""
:local sop [:pick [:tostr $search] 0 1]
:set search [:pick [:tostr $search] 1 [:len [:tostr $search]]]
/ip dns cache all {
   :local findex; :local property; :local value; :local name; :local type; :local data
   :local sfound 0
   :foreach rule in=[print detail as-value where static=no] do={
      :set name ""; :set type ""; :set data ""

      :foreach item in=$rule do={
         :set findex [:find [:tostr $item] "="]
         :set property [:pick [:tostr $item] 0 $findex]
         :set value [:pick [:tostr $item] ($findex + 1) [:len [:tostr $item]]]

         :if ($property = "name") do={ :set name $value }
         :if ($property = "type") do={ :set type $value }
         :if ($property = "data") do={ :set data $value }
      }
# Search DNS cache name using specified operator
      :set sfound 0
      :if ($sop = ":") do={
         :if ([:find [:tostr $name] [:tostr $search]] != "" && $type = "A") do={ :set sfound 1 }
      }
      :if ($sop = "=") do={
         :if ([:tostr $name] = [:tostr $search] && $type = "A") do={ :set sfound 1 }
      }
      :if ($sop = "~") do={
         :if ([:tostr $name] ~ [:tostr $search] && $type = "A") do={ :set sfound 1 }
      }
      :if ($sfound = 1) do={
#         :put ("Found " . $name . " -> " . $data)
         :set IPs ($IPs . $data . ",")
         :set Names ($Names . $name . ",")
      }
   }
# /ip dns cache all
}
:put ("DNS cache search found " . [:len [:toarray $IPs]] . " match(es) for '" . $search . "'")
# Search through IPs and add to address list
/ip firewall address-list {
   :local findex; :local listaddr; :local IPsFound ""

   :put ("Searching address list '" . ($listprefix . [:tostr $search]) . "'...")
   :foreach l in=[find list=($listprefix . [:tostr $search])] do={
      :set listaddr [get $l address]
      :if ([:len [:find [:toarray $IPs] [:toip $listaddr]]] = 0) do={
         :put ("   " . $listaddr . " not found in search, removing...")
         remove $l
      } else={
#         :put ($listaddr . " found address in IPs")
         :set IPsFound ($IPsFound . $listaddr . ",")
      }
   }
# Add remaining records to address list
   :set findex 0
   :foreach ip in=[:toarray $IPs] do={
      :if ([:len [:find [:toarray $IPsFound] [:toip $ip]]] = 0) do={
         :put ("   Adding address " . $ip)
         add list=($listprefix . $search) address=[:toip $ip] comment=([:pick [:toarray $Names] $findex]) disabled=no
      }
      :set findex ($findex + 1)
   }
# /ip firewall address-list
}</code>
Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

