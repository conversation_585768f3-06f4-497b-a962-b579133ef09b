<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Auto upgrade script V3.x - Mikrotik Scrip RouterOS</title>
<meta content='Auto upgrade script V3.x - Mikrotik RouterOS Script Database' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>  
</head>  
<body>
<div id="hidelink"></div>
<h1>Auto upgrade script V3.x - Mikrotik Script RouterOS</h1>
<pre>One little script to upgrade RrouterBoards automatically.
-) fetch file from main router containing latest ROS version number;
-) get currently running version number;
-) extract minor and major version numbers and compare them;
-) if version fetched from main router is newer then download combined package directly from mikrotik.com
-) reboot.
At first you should create file containing newest version number on main router.

<code class="routeros">/file print file="latestVer";</code>
<code class="routeros">/file set latestVer.txt content="3.24";</code>
Now we are ready to run scripts on other routers:

<code class="routeros">#########################################################################
# download and upgrade
#########################################################################

# newest version
:local lMajor;
:local lMinor;

# current version
:local vMajor;
:local vMinor;

:local myVer [/system resource get version];

:for i from=0 to=([:len $myVer] - 1) do={  
	:if ( [:pick $myVer $i] = ".") do={ 
		:set vMajor [:tonum [:pick $myVer 0 $i]]; 
		:set vMinor [:tonum [:pick $myVer ($i + 1) [:len $myVer]]] ;
	}
}

:if ($vMajor < 3) do={
	:log warning "RouterOS version too old ($vMajor.$vMinor),  update script not compatible";
} else={ 

#      detect platform (architecture-name is not available in older 3.x versions)
       :local platform [/system resource get architecture-name];


#      fetch latest version
       /tool fetch address="**********" src-path="latestVer.txt" user="admin" password="" mode=ftp;
       :local lVer [/file get latestVer.txt content];


	:for i from=0 to=([:len $lVer] - 1) do={  
		:if ( [:pick $lVer $i] = ".") do={ 
			:set lMajor [:tonum [:pick $lVer 0 $i]]; 
			:set lMinor [:tonum [:pick $lVer ($i + 1) [:len $lVer]]] ;
		}
	}

	:if (($vMajor = $lMajor) && ($vMinor < $lMinor)) do={
		:local pckgName "routeros-$platform-$lMajor.$lMinor.npk";
		/tool fetch address=[:resolve "www.mikrotik.com"] host="www.mikrotik.com" mode=http src-path="download/$pckgName";
		/system reboot;
	} else={
                :log info "Upgrade_script: already latest version";
        }
}</code>
Credit: https://wiki.mikrotik.com/wiki/Auto_upgrade_script_V3.x
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
