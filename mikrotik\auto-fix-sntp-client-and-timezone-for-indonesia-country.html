<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Auto Fix SNTP and Clock for Indonesia Timezone - MikroTik Script RouterOS</title>
<meta content='Auto Fix SNTP and Clock for Indonesia Timezone - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Auto Fix SNTP and Clock for Indonesia Timezone - MikroTik Script RouterOS</h1>
<pre>Time is very important for computers to do the right thing. If your clock is wrong by a minute authenticator apps won't work. Wrong by an hour and Windows Networking has problems. Wrong by days and you risk SSL certificates expiring earlier (or later) than they should. VPNs may stop working. Datestamps on logs will be misleading. Generally, wrong time is a Bad Thing, and what is worse is the time the hotspot is out of sync with the Hotpspot Billing. Because Correct time is very important.

Manual Setup:

<code class="routeros">#==============================================
# Auto Repair TimeZone and clock 
#==============================================
# Waktu Indonesia Barat (WIB)  = +07:00
# Waktu Indonesia Tengah (WITA) = +08:00
# Waktu Indonesia Timur (WIT)  = +09:00 
#==============================================
# Ganti "+07:00" sesuaikan dengan daerah masing-masing
#==============================================
/system clock manual set time-zone="+07:00";
/system clock set time-zone-autodetec=no;
/system clock set time-zone-name="manual";
/ip cloud set update-time=no;
:local ntpServer "asia.pool.ntp.org";
:local primary [resolve $ntpServer];
:local secondary [resolve $ntpServer];
/system ntp client set primary-ntp $primary;
/system ntp client set secondary-ntp $secondary;
/system ntp client set enabled=yes;</code>
Complete Script With Scheduler

<code class="routeros">/system scheduler
add interval=1d name=SETTING-TIMEZONE on-event="#=============================\
=================\r\
# Auto Repair TimeZone and clock \r\
#==============================================\r\
# Waktu Indonesia Barat (WIB)  = +07:00\r\
# Waktu Indonesia Tengah (WITA) = +08:00\r\
# Waktu Indonesia Timur (WIT)  = +09:00 \r\
#==============================================\r\
# Ganti \"+07:00\" sesuaikan dengan daerah masing-masing\r\
#==============================================\r\
/system clock manual set time-zone=\"+07:00\";\r\
/system clock set time-zone-autodetec=no;\r\
/system clock set time-zone-name=\"manual\";\r\
/ip cloud set update-time=no;\r\
:local ntpServer \"asia.pool.ntp.org\";\r\
:local primary [resolve \$ntpServer];\r\
:local secondary [resolve \$ntpServer];\r\
/system ntp client set primary-ntp \$primary;\r\
/system ntp client set secondary-ntp \$secondary;\r\
/system ntp client set enabled=yes;" policy=\
ftp,reboot,read,write,policy,test,password,sniff,sensitive,romon start-time=startup</code>
Credit: www.o-om.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>