<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Only 1 Hotspot Id Can Login Together - MikroTik Script RouterOS</title>
<meta content='Only 1 Hotspot Id Can Login Together - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Only 1 Hotspot Id Can Login Together - MikroTik Script RouterOS</h1>
<pre>Make Sure that only 1 hotspot ID can log in together, if there are 2 IDs that are logged in, it will automatically turn off the first ID (note: not for multi shared users). This script is also great if we want to switch logins to other devices without having to log out and wait for the session timeout on the previous device.
Before it is installed, at least we don't understand the concept of the work of the script, don't just take it out and paste it, instead the hotspot error :)

Please install it directly on the IP -> hotspot -> User Profile -> select the profile name -> Script -> On Login

Hotspot Profile On Login

<code class="routeros"># Local Variables Section
:local uname $user;
:local usercount 0;
:local usertime "00:00:00";
# Variable for previously logged in users
:local kickable;
# Variable for max user allowed sessions, if 2 means only one session is allowed at a time
:local maxuser 2;
# Loads all active users on the hotspot
:foreach i in=[/ip hotspot active find user=$uname] do= {
# UPTIME loads for all users and for later matches
:local curup [/ip hotspot active get $i uptime];
# If the same user was previously matched using UPTIME [above then 0] then assign global variables to break
:if ( $curup > $usertime ) do={
:set usertime $curup;
:set kickable $i;
}
:set usercount ($usercount+1);
}
# IF Function Function for logged in users,
:if ($usercount = $maxuser) do={
:log info "Login user: $uname ($usercount/$maxuser) - Oldest $usertime will be logout!";
# Kick the previous login user (if the ID is the same)
/ip hotspot active remove numbers=$kickable;
# If not, do nothing, just log in, you can modify this function too
} else {
:log info "Login user: $uname ($usercount/$maxuser)";</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

