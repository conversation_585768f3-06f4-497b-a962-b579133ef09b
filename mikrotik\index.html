<!DOCTYPE html>
<html lang="en">
<head>
<title>MikroTik Script RouterOS - mikrotiktool.Github.io</title>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta content='Complete MikroTik RouterOS Script DataBase Unoffical, Enjoy the most complete script to make it easier for you to learn Mikrotik RouterOS scripts!' name='description'/>
<meta content='RouterOS, Mikrotik Script, load balancing, pcc, LB, script generator, mikrotik, router, winbox, termimal, rsc, script, hotspot, wireless, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<meta property="og:title" content="MikroTik RouterOS Script - mikrotiktool">
<meta property="og:description" content="MikroTik RouterOS Script DataBase Unoffical - mikrotiktool">
<meta property="og:image" content="https://user-images.githubusercontent.com/42666125/111909594-8074e400-8a90-11eb-8d98-5dfe4b21c657.png">
<meta property="og:image:alt" content="Mikrotik RouterOS Script">
<meta property="og:type" content="website">
<meta property="og:url" content="https://mikrotiktool.github.io/mikrotik">	
<link rel="stylesheet" href="stytes.css">
<link rel="stylesheet" href="index-enhanced.css">
padding:3px 0 3px 0;
margin:0 0 0 22px;
color:#999
}
.logo {
border-bottom:0px solid #ccc;
margin-bottom:5px;
margin-top:10px;
color: #111;
font-size:30px;
font-weight:bold; 
}	
.logo a{
color:#111 !important;
text-decoration:none !important;
font-size:30px;
text-decoration:none;
}
.logo-left {
float:left;
}
.logo-right {
float:right;
}
#wrap{
max-width:1180px;
min-width:200px;
margin:0 auto;
}
.header{
padding:10px;
padding-top:10px;
padding-bottom:5px;
display:block;
background: #8E2DE2;  /* fallback for old browsers */
background: -webkit-linear-gradient(to left, #4A00E0, #8E2DE2);  /* Chrome 10-25, Safari 5.1-6 */
background: linear-gradient(to left, #4A00E0, #8E2DE2); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
color:#fff;
border-top:1px solid #ccc;
border-left:1px solid #ccc;
border-right:1px solid #ccc;
}
.header a{
color:#8E2DE2 !important;
text-decoration:none !important;
}
.header-left {
float:left;
width:450px;
}
.header-right {
float:right;
margin-top:-2px;
}
.content{
float:right;
width:64%;
padding: 0px;
border:1px solid #ccc;
background:#fff;
height: 750px;
}
.sidebar{
overflow:hidden;
float:left;
width:36%;
padding: 10px;
background-color:#eee;
border:1px solid #ccc; 
height: 750px;
}
.footer{
font-size:14px;
border-top:4px solid #ccc;
padding:10px;
background:#fff;
color:#111;
clear:both;
width:auto;
text-align:center;
}
.footer a{
color:#8E2DE2 !important;
}
h1 {
color:#fff;
font-size:20px;
margin-top:-2px;
}
h2 {
color:#8E2DE2;
font-size:20px;
}
.active1, .linkPress:hover, p{
color:#8E2DE2 !important;
}
a:link {
color: #333;
text-decoration:none
}
a:visited {
color: #333;
text-decoration:none
}
a:hover {
color: #333;
}
a:active {
color: #333;
}
button {
color: #fff;
background-color: #8E2DE2;
border-color: #8E2DE2;
border-radius: 2px;
border:none;
margin-top:0px;
padding:5px;
width:70px;
font-size:16px;
font-weight:bold;
}
input[type=text], select, textarea {
width: 100%;
padding: 5px;
border: 1px solid #ccc;
border-radius: 0px;
resize: vertical;
font-size:16px !important;
}
label {
padding: 5px 5px 5px 0;
display: inline-block;
}
input[type=submit] {
background-color: #4CAF50;
color: white;
padding: 13px 20px;
border: none;
border-radius: 1px;
cursor: pointer;
float: right;
}
input[type=submit]:hover {
background-color: #45a049;
}
/* Clear floats after the columns */
.row:after {
content: "";
display: table;
clear: both;
}
.list-game {
border-radius:0;
height:655px;
background:white;
overflow: scroll;
overflow-x: hidden;
width: 100%;
margin-top:3px;
padding: 0px;
border: 1px solid #ccc;
}
.list-game label {
padding: 0;
display: inline-block;
} 
.list-mangle {
height: 737px;
background:#fff;
width: 100%;
padding-left: 8px;
padding-right: 10px;
margin:0;
border: 0px solid #ccc;
}
.mytable { overflow-x:hidden;
height:218px;
margin:0;
border: 0px solid #ccc;
}
table {
border-collapse:collapse;
width: 100%;
}
tr{
width:100%;
border: 1px solid #f0f0f0;
background: #f5f5f5;
}
tr:nth-child(even) {
background: #fff;
width:100%;
}
@media screen and (max-width: 1024px){
.bookmark-font {font-size:25px; text-align:center}
body {margin:0px;background:#fff;}
.footer{border:0}
.logo{
margin-left:10px;
margin-right:10px;
}
#wrap{
padding:0;
margin:0;
width:100%;
height:100%;
}
.sidebar {
border:0px solid #ccc;
width:100%;
height:100%;
overflow: hidden;
background:#fff;
}
.list-game {
width:100%;
height:100%;
overflow: hidden;
}
.list-mangle {
height:100%;
}
.content, .welcome, .list-mangle {
display:none;
}
}
@media screen and (max-width: 734px){
button {
margin-top:5px;
}  
.header{
height:auto;    
}   
.header-left, .header-right {
float:none;
display:block;
width:auto;
}
}
@media screen and (max-width: 550px){
.logo-left a{
font-size:25px;
}	
.logo-left, .logo-right {
float:none;
display:block;
width:auto;
   text-align: center 
}
.header-left, .header-right {
  text-align: center
} 
}
iframe {
width:100%;
height:520px;
border:0 
}
.welcome {
width:100%;
height:520px;
padding:10px;
}
.welcome a{
color:#8E2DE2 !important;
}
.backtotop {
background:#ccc;
text-align:center;
width: 100%;
border-radius:0 0 4px 4px;
height:27px;
color: #444;
}
.backtotop a {
font-size:11px; 
padding:0;
color:#444;
font-weight: bold;
width: 100%;
text-transform: uppercase;
background:#ccc;
}
.backtotop a:hover {
color:#8E2DE2;
}
#myBtn {
  display: none;
  position: fixed;
  bottom: 50px;
  right: 30px;
  z-index: 99;
  font-size: 16px;
  border: none;
  outline: none;
  background-color: #8E2DE2;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 3px;
}
#myBtn:hover {
  background-color: #555;
}
#CbiJvKRogXqD {
display: none;
background: #000;
text-align: center;
font-weight: bold;
color: #fff;
font-size: 4.0vw;
position: fixed;
width: 100%;
height: 100%;
top:0;
left:0;
opacity: 0.7;
z-index:99;
padding:150px;
}
</style>
</head>
<body>
<button onclick="topFunction()" id="myBtn" title="Go to top">Top</button>
<div id="CbiJvKRogXqD">
PLEASE DISABLE YOUR AD BLOCKER FOR SUPPORT THIS SITE! THANKS :)
</div>
<div id="wrap">	
<div class="logo">	
<span class="logo-left"><a href="https://mikrotiktool.github.io"><span style="color:#8E2DE2 !important">mikrotiktool</span>.Github.io</a> </span>
<span class="logo-right"><img style="padding-top:12px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false" alt="Hits"/></span>
<div style='clear: both;'></div>
</div>
<div class="header">
<span class="header-left">
<span style="float:left; margin-right:6px; margin-top:-2px">
<a href="https://mikrotiktool.github.io/mikrotik/"><img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="Mikrotik Script RouterOS"></a>
</span>
<h1>MIKROTIK SCRIPT ROUTEROS DATABASE</h1>
</span>
<span class="header-right"><b>Contribution your script</b> <button style="margin-left:5px;" onclick="location.href='https://docs.google.com/forms/d/e/1FAIpQLSfbuzYtFCI57y7nJHgnL1w0aiCY5g45Uon90u3XQm6pAi56BQ/viewform';">upload</button></span>
<div style='clear: both;'></div>
</div>	
<div class="sidebar">
<input type="text" id="myInput" onkeyup="myFunctionSearch()" placeholder="search script.." style="width:100%;margin-bottom:7px;font-size: 16px"> 
<div class="list-game">
<div class="bookmark-font">
<span style="padding-left:6px;padding-top:5px;font-weight:bold; text-align:center">
<a id="top"></a>
<a href="#A"><span style="color:#8E2DE2">A</span></a>
<a href="#B"><span style="color:#000">B</span></a>
<a href="#C"><span style="color:#8E2DE2">C</span></a>
<a href="#D"><span style="color:#000">D</span></a>
<a href="#E"><span style="color:#8E2DE2">E</span></a>
<a href="#F"><span style="color:#000">F</span></a>
<a href="#G"><span style="color:#8E2DE2">G</span></a>
<a href="#H"><span style="color:#000">H</span></a>
<a href="#I"><span style="color:#8E2DE2">I</span></a>
<a href="#J"><span style="color:#000">J</span></a>
<a href="#K"><span style="color:#8E2DE2">K</span></a>
<a href="#L"><span style="color:#000">L</span></a>
<a href="#M"><span style="color:#8E2DE2">M</span></a>
<a href="#N"><span style="color:#000">N</span></a>
<a href="#O"><span style="color:#8E2DE2">O</span></a>
<a href="#P"><span style="color:#000">P</span></a>
<a href="#Q"><span style="color:#8E2DE2">Q</span></a>
<a href="#R"><span style="color:#000">R</span></a>
<a href="#S"><span style="color:#8E2DE2">S</span></a>
<a href="#T"><span style="color:#000">T</span></a>
<a href="#U"><span style="color:#8E2DE2">U</span></a>
<a href="#V"><span style="color:#000">V</span></a>
<a href="#W"><span style="color:#8E2DE2">W</span></a>
<a href="#X"><span style="color:#000">X</span></a>
<a href="#Y"><span style="color:#8E2DE2">Y</span></a>
<a href="#Z"><span style="color:#000">Z</span></a>
</span>
</div>
<table id="myTable">
<!-- A -->
<tr><td><span id="A" style="margin-left:5px; font-weight:bold">A</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/anti-hack-bootloader.html" onclick="getcontent(this); return truefalseFunction(x);">Anti Hack Protected BootLoader</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/auto-fix-sntp-client-and-timezone-for-indonesia-country.html" onclick="getcontent(this); return truefalseFunction(x);">Auto Fix SNTP and Clock for Indonesia Timezone</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Auto-Simple-Queue-From-DHCP-Server.html" onclick="getcontent(this); return truefalseFunction(x);">Auto Simple Queue From DHCP Server</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Auto-Connect-to-Internet-With-DHCP-Client.html" onclick="getcontent(this); return truefalseFunction(x);">Auto Connect to Internet With DHCP Client</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Auto-Repair-Passthrough-In-Mangle.html" onclick="getcontent(this); return truefalseFunction(x);">Auto Repair Passthrough In Mangle</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/auto-backup-filename-backup-and-send-to-gmail.html" onclick="getcontent(this); return truefalseFunction(x);">Auto Backup Filename.backup and Send To Gmail</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/auto-backup-filename-rsc-and-send-to-gmail.html" onclick="getcontent(this); return truefalseFunction(x);">Auto Backup Filename.rsc and Send To Gmail</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Automated-Usage-Script-without-usermanager.html" onclick="getcontent(this); return truefalseFunction(x);">Automated Usage Script Without User Manager</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Allow-use-ofntporg-pool-service-for-NTP.html" onclick="getcontent(this); return truefalseFunction(x);">Allow Use Of ntp.org Pool Service For NTP</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Auto-upgrade-script-V3x.html" onclick="getcontent(this); return truefalseFunction(x);">Auto upgrade script V3.x</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Audible-signal-test.html" onclick="getcontent(this); return truefalseFunction(x);">Audible Signal Test</a></p></td></tr>	
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/A-Bit-of-Sounds.html" onclick="getcontent(this); return truefalseFunction(x);">A Bit of Sounds</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/add-a-data-limit-to-trial-hotspot-users.html" onclick="getcontent(this); return truefalseFunction(x);">Add a Data Limit To Trial Hotspot Users</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Auto-Update-RouterOS-Package-From-Script.html" onclick="getcontent(this); return truefalseFunction(x);">Auto Update RouterOS Package From Script</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/ARP-Table-To-Dynamic-Simple-Queue.html" onclick="getcontent(this); return truefalseFunction(x);">ARP Table To Dynamic Simple Queue</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Add-Static-DHCP-Leases-to-ARP-List.html" onclick="getcontent(this); return truefalseFunction(x);">Add Static DHCP Leases to ARP List</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Alignment-Script-that-reads-back-RSSI-with-Beeps.html" onclick="getcontent(this); return truefalseFunction(x);">Alignment Script that "reads back" RSSI with Beeps</a></p></td></tr>
<!-- B -->
<tr><td><span id="B" style="margin-left:5px; font-weight:bold">B</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Block-Sites-User-Keywords-Use-The-Dns-Cache.html" onclick="getcontent(this); return truefalseFunction(x);">Block Sites With Keywords Use Dns Cache</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Blog-Website-With-Keywords-Use-DNS-Static.html" onclick="getcontent(this); return truefalseFunction(x);">Block Website With Keywords Use DNS Static</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Bypass-Full-Speed-Local-Transfer-File.html" onclick="getcontent(this); return truefalseFunction(x);">Bypass Full Speed Locat Transfer File</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/How-To-Backup.html" onclick="getcontent(this); return truefalseFunction(x);">Backup Mikrotik File .Backup</a></p></td></tr>	
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/How-to-Backup-Use-Export-rsc.html" onclick="getcontent(this); return truefalseFunction(x);">Backup Export File .rsc</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/How-To-Backup-rsc-hide-sensitive.html" onclick="getcontent(this); return truefalseFunction(x);">Backup Export File .rsc Hide-Sensitive</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/How-To-Backup-rsc-compact.html" onclick="getcontent(this); return truefalseFunction(x);">Backup Export File .rsc Compact</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/How-To-Backup-rsc-terse.html" onclick="getcontent(this); return truefalseFunction(x);">Backup Export File .rsc Terse</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/How-To-Backup-rsc-verbose.html" onclick="getcontent(this); return truefalseFunction(x);">Backup Export File .rsc Verbose</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/How-To-Backup-rsc-combination.html" onclick="getcontent(this); return truefalseFunction(x);">Backup Export File .rsc With Combination</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/How-to-Backup-Use-import-rsc.html" onclick="getcontent(this); return truefalseFunction(x);">Backup Import File .rsc</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Bypass-Local-Connection.html" onclick="getcontent(this); return truefalseFunction(x);">Bypass Local Connection / Local IP</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/bypass-user-hotspot-with-macaddress.html" onclick="getcontent(this); return truefalseFunction(x);">Bypass Hotspot User With Mac-Address</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Block-access-to-specific-websites.html" onclick="getcontent(this); return truefalseFunction(x);">Blocking Access To Specific Websites</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Bittorrent-layer-7.html" onclick="getcontent(this); return truefalseFunction(x);">Bittorrent, Torrentwws, Torrentwww Layer-7</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Block-Windows-Update.html" onclick="getcontent(this); return truefalseFunction(x);">Block Windows Update</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-youtube-layer7-content-tls.html" onclick="getcontent(this); return truefalseFunction(x);">Block Youtube with "Layer 7" or "Content" Or "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-whatsapp-layer7-content-tls.html" onclick="getcontent(this); return truefalseFunction(x);">Block Whatsapp with "Layer 7" or "Content" Or "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-facebook-layer7-content-tls.html" onclick="getcontent(this); return truefalseFunction(x);">Block Facebook with "Layer 7" or "Content" Or "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-istagram-layer7-content-tls.html" onclick="getcontent(this); return truefalseFunction(x);">Block Instagram with "Layer 7" or "Content" Or "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-twitter-layer7-content-tls.html" onclick="getcontent(this); return truefalseFunction(x);">Block Twitter with "Layer 7" or "Content" Or "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-telegram-layer7-content-tls.html" onclick="getcontent(this); return truefalseFunction(x);">Block Telegram with "Layer 7" or "Content" Or "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-signal-layer7-content-tls.html" onclick="getcontent(this); return truefalseFunction(x);">Block Signal with "Layer 7" or "Content" Or "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-bigo-layer7-content-tls.html" onclick="getcontent(this); return truefalseFunction(x);">Block Bigo with "Layer 7" or "Content" Or "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-netflix-layer7-content-tls.html" onclick="getcontent(this); return truefalseFunction(x);">Block Netflix with "Layer 7" or "Content" Or "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-iflix-layer7-content-tls.html" onclick="getcontent(this); return truefalseFunction(x);">Block iflix with "Layer 7" or "Content" Or "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-tracert.html" onclick="getcontent(this); return truefalseFunction(x);">Block Tracert / Traceroute</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Block-Tiktok.html" onclick="getcontent(this); return truefalseFunction(x);">Block TikTok Firewall Rules "TLS"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-tiktok-content-firewall-rules.html" onclick="getcontent(this); return truefalseFunction(x);">Block TikTok Firewall Rules "Content"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-tiktok-layer7-firewall-rules.html" onclick="getcontent(this); return truefalseFunction(x);">Block TikTok Firewall Rules "Layer-7"</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Block-MAC-Address-Device.html" onclick="getcontent(this); return truefalseFunction(x);">Block MAC Address Device</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Blocking-Modem.html" onclick="getcontent(this); return truefalseFunction(x);">Blocking Modem Access</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Backup-graphing-data.html" onclick="getcontent(this); return truefalseFunction(x);">Backup Graphing Data</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Basic-universal-firewall-script.html" onclick="getcontent(this); return truefalseFunction(x);">Basic universal firewall script</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-download-based-download-sizes.html" onclick="getcontent(this); return truefalseFunction(x);">Block Download Based Download Sizes</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/basic-traffic-priorities-rev-31.html" onclick="getcontent(this);return false;">Basic Traffic Priorities Rev 3.1</a></p></td></tr>
<!-- C -->
<tr><td><span id="C" style="margin-left:5px; font-weight:bold">C</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/how-to-change-mac-address.html" onclick="getcontent(this); return truefalseFunction(x);">Change MAC Address From Terminal</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/change-all-queue-type-in-queue-simple.html" onclick="getcontent(this); return truefalseFunction(x);">Change all Queue type in Queue Simple</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/change-all-queue-type-in-queue-tree.html" onclick="getcontent(this); return truefalseFunction(x);">Change all Queue type in Queue Tree</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Checking-for-the-update-New-Version.html" onclick="getcontent(this); return truefalseFunction(x);">Checking for the Update New Version</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Clear-Log-and-Session-User-Manager.html" onclick="getcontent(this); return truefalseFunction(x);">Clear Log and Session User Manager</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Custom-Limit-Bandwidth-Hotspot-With-Queue-Tree.html" onclick="getcontent(this); return truefalseFunction(x);">Custom Limit Bandwidth Hotspot With Queue Tree</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Custom-Limit-Bandwidth-Hotspot-With-Queue-Simple.html" onclick="getcontent(this); return truefalseFunction(x);">Custom Limit Bandwidth Hotspot With Queue Simple</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Complete-Bandwidth-Monitoring.html" onclick="getcontent(this); return truefalseFunction(x);">Complete Bandwidth Monitoring</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Create-Multiple-Queue-Tree-At-Once.html" onclick="getcontent(this); return truefalseFunction(x);">Create Multiple Queue Tree At Once</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Create-Multiple-Simple-Queue-At-Once.html" onclick="getcontent(this); return truefalseFunction(x);">Create Multiple Simple Queue At Once</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Create-Hotspot-Multi-Login-With-Bandwidth-Limit.html" onclick="getcontent(this); return truefalseFunction(x);">Create Hotspot Multi Login With Bandwidth Limit</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Check-if-RTO-and-send-to-Telegram.html" onclick="getcontent(this); return truefalseFunction(x);">Check if RTO and send to Telegram</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/address-list-dynamic-to-static.html" onclick="getcontent(this); return truefalseFunction(x);">Change All Address List Dynamic To Static</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/change-system-note.html" onclick="getcontent(this); return truefalseFunction(x);">Change System Note</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Speedtest-Mangle-layer7.html" onclick="getcontent(this); return truefalseFunction(x);">Complete Mangle Speedtest with Layer-7</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Get-Day.html" onclick="getcontent(this); return truefalseFunction(x);">Calculates Day Of The Week For a Givien Date</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/change-Interface-ether-Default-1.html" onclick="getcontent(this); return truefalseFunction(x);">Change Name Interface or Ether To Default Auto</a></p></td></tr>  
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/change-Interface-ether-Default-2.html" onclick="getcontent(this); return truefalseFunction(x);">Change Name Interface or Ether To Default Manual</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/check-ip-change.html" onclick="getcontent(this); return truefalseFunction(x);">Check if IP on interface have changed</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Check-bandwidth-and-add-limitations.html" onclick="getcontent(this); return truefalseFunction(x);">Check Bandwidth and Add Limitations</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Calculate-with-decimal-numbers.html" onclick="getcontent(this); return truefalseFunction(x);">Calculate With Decimal Numbers</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/clear-log-Mikrotik.html" onclick="getcontent(this); return truefalseFunction(x);">Clear LOG info from Terminal</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Create-a-file.html" onclick="getcontent(this); return truefalseFunction(x);">Create A File</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Calea-perl-trafr.html" onclick="getcontent(this); return truefalseFunction(x);">Calea Perl Trafr</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Converting_network_and_gateway_from_routing_table_to_hexadecimal_string.html" onclick="getcontent(this); return truefalseFunction(x);">Converting Gateway From Routing To hexadecimal</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Change-Mikrotik-identity-or-Winbox-Caption.html" onclick="getcontent(this); return truefalseFunction(x);">Change Mikrotik identity or Winbox Caption</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Change-All-Queue-Type-in-Interface-Queue.html" onclick="getcontent(this); return truefalseFunction(x);">Change All Queue Type in Interface Queue</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/check-routeros-update-and-notification.html" onclick="getcontent(this); return truefalseFunction(x);">Check RouterOS Update and Notification to eMail or Telegram</a></p></td></tr>
<!-- D -->
<tr><td><span id="D" style="margin-left:5px; font-weight:bold">D</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/detect-ip-address-on-vpn-tunnel-if-has-been-changed.html" onclick="getcontent(this); return truefalseFunction(x);">Detect IP Address on VPN Tunnel if has been changed</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/block-ads-use-dns-static.html" onclick="getcontent(this); return truefalseFunction(x);">DNS Static adblock For Block Ads</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Dynamic-DNS-on-private-DNS-server.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS on Private DNS Server</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-changeip.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS Update Script for ChangeIP.com</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-changeip-behind-nat.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS Update Script for ChangeIP behind NAT</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-dnsomatic.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS Update Script for DNSoMatic.com</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-dnsomatic-behind-nat.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS Update Script for DNSoMatic.com behind NAT</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-dyndns.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS Update Script for dynDNS</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-dyndns-behind-nat.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS Update Script for dynDNS behind NAT</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-everydns.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS Update Script for EveryDNS</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-hurricane-electric-dns.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS Update Script for Hurricane Electric DNS</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-namecheap.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS Update Script for Namecheap</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-no-ip-dns.html" onclick="getcontent(this); return truefalseFunction(x);">Dynamic DNS Update Script for No-IP DNS</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Delete-Arp-Trafic-For-Arp-Table.html" onclick="getcontent(this); return truefalseFunction(x);">Delete Arp Trafic For Arp Table</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Default-Filter-Rules.html" onclick="getcontent(this); return truefalseFunction(x);">Default Filter Rules</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/DNSCRYPT-with-OpenDNS.html" onclick="getcontent(this); return truefalseFunction(x);">DNSCRYPT with OpenDNS port 443 or 5353</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/dns-chace-flush.html" onclick="getcontent(this); return truefalseFunction(x);">DNS Cache Flush</a></p></td></tr>  
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Detect-new-log-entry.html" onclick="getcontent(this); return truefalseFunction(x);">Detect New Log Entry</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Dial_PPPoE_until_a_Certain_IP_Range_is_Obtained.html" onclick="getcontent(this); return truefalseFunction(x);">Dial PPPoE until a Certain IP Range is Obtained</a></p></td></tr>
<tr><td><p><a href="https://mikrotiktool.github.io/mikrotik/discover-unknown-dhcp-server-on-the-network.html">Discover Unknown DHCP Server On The Network (Telegram)</a></p></td></tr>
<!-- E -->
<tr><td><span id="E" style="margin-left:5px; font-weight:bold">E</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Extract-the-day-of-the-month.html" onclick="getcontent(this); return truefalseFunction(x);">Extract The Day Of The Month</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Automatic-Expired-Hotspot-Page-Without-A-Proxy.html" onclick="getcontent(this); return truefalseFunction(x);">Expired Hotspot Page Without A Proxy</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Enable-Disable-New-Guest-User-Account-Daily-Hotspot.html" onclick="getcontent(this); return truefalseFunction(x);">Enable or Disable New Guest User Account Daily Hotspot</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/expire-users-a-after-number-of-days.html" onclick="getcontent(this); return truefalseFunction(x);">Expire Users Hotspot A After Number Of Days</a></p></td></tr>
<!-- F -->
<tr><td><span id="F" style="margin-left:5px; font-weight:bold">F</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/enable-fasttrack.html" onclick="getcontent(this); return truefalseFunction(x);">Fasttrack Enable</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/filter-a-command-output.html" onclick="getcontent(this); return truefalseFunction(x);">Filter A Command Output</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/script-to-find-the-day-of-the-week.html" onclick="getcontent(this); return truefalseFunction(x);">Find The Day Of The Week</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/show-usermanager-password.html" onclick="getcontent(this); return truefalseFunction(x);">Forget or Show User Manager Password</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Failover-with-Netwatch.html" onclick="getcontent(this); return truefalseFunction(x);">Failover with Netwatch</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Failover-with-Scripting.html" onclick="getcontent(this); return truefalseFunction(x);">Failover With Scripting</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Force-Disconnect-Wireless-Stations-with-Low-CCQ.html" onclick="getcontent(this); return truefalseFunction(x);">Force Disconnect Wireless Stations with Low CCQ</a></p></td></tr>
<!-- G -->
<tr><td><span id="G" style="margin-left:5px; font-weight:bold">G</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/gps-text-file-converter-to-google-earth-or-maps.html" onclick="getcontent(this); return truefalseFunction(x);">GPS Text File Converter To Google Earth Or Maps</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/google-domain-list-or-all-google-host.html" onclick="getcontent(this); return truefalseFunction(x);">Google Domain List Or All Google Host</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/get-active-vpn-connections-via-e-mail.html" onclick="getcontent(this); return truefalseFunction(x);">Get Active Vpn Connections Via E-Mail</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Google-Safe-Search-use-DNS-Static.html" onclick="getcontent(this); return truefalseFunction(x);">Google Safe Search use DNS Static</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/How-to-get-todays-date-info.html" onclick="getcontent(this); return truefalseFunction(x);">Get Today's Date Info</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Generate-backup-and-sendit-by-email.html" onclick="getcontent(this); return truefalseFunction(x);">Generate Backup And Send it By E-Mail</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/get-ip-address-list-netflix-with-raw.html" onclick="getcontent(this); return truefalseFunction(x);">Get IP Address List Netflix With RAW</a></p></td></tr>	
<!-- H -->
<tr><td><span id="H" style="margin-left:5px; font-weight:bold">H</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/how-to-disable-google-ssl-using-static-dns.html" onclick="getcontent(this); return truefalseFunction(x);">How To Disable Google Ssl Using Static Dns</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Speedtest-domain-List.html" onclick="getcontent(this); return truefalseFunction(x);">Host or Domain list SpeedTest</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Hotspot-Auto-User-logout-user-When-internet-down.html" onclick="getcontent(this); return truefalseFunction(x);">Hotspot Auto logout user When Inet down</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/change-path-database-user-manager-to-disk-or-usb.html" onclick="getcontent(this); return truefalseFunction(x);">How To Change Path DataBase User Manager to Disk or USB</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/hurricane-electric-ipv6-tunnel-ipv4-endpoint-updater.html" onclick="getcontent(this); return truefalseFunction(x);">Hurricane Electric IPv6 Tunnel - IPv4 Endpoint updater</a></p></td></tr> 
<!-- I -->
<tr><td><span id="I" style="margin-left:5px; font-weight:bold">I</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Pool-Statistics.html" onclick="getcontent(this); return truefalseFunction(x);">IP Pool Statistics</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-list-netflix.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address List for Netflix</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-list-tiktok.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address List for Tiktok</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-list-GGC.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address List for GGC Google Global Cache</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-list-ix-nice-rsc.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address List Nice.rsc IIX or IXP</a></p></td></tr>	
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-List-AWS-EC2-Amazon.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address List AWS EC2 Amazon</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-list-Speedtest.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address list SpeedTest</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-list-Telergram.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address list Telegram</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-list-Facebook.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address list Facebook</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-List-twitter.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address list Twitter</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-List-WhatsApp.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address list WhatsApp</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Address-list-zoom-meeting.html" onclick="getcontent(this); return truefalseFunction(x);">IP Address List Zoom Meeting</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/IP-Private-or-IP-Local-Standard-RFC1918.html" onclick="getcontent(this); return truefalseFunction(x);">IP Private or IP Local Standard RFC 1918</a></p></td></tr>	
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Include-custom-function-in-another-script.html" onclick="getcontent(this); return truefalseFunction(x);">Include custom function in another script</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/initiating-the-update-process-new-version.html" onclick="getcontent(this); return truefalseFunction(x);">Initiating The Update Process New Version</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Improved-Netwatch.html" onclick="getcontent(this); return truefalseFunction(x);">Improved Netwatch</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Improved-Netwatch-2.html" onclick="getcontent(this); return truefalseFunction(x);">Improved Netwatch 2</a></p></td></tr>
<!-- J -->
<tr><td><span id="J" style="margin-left:5px; font-weight:bold">J</span></td></tr>
<!-- K -->
<tr><td><span id="K" style="margin-left:5px; font-weight:bold">K</span></td></tr>
<!-- L -->
<tr><td><span id="L" style="margin-left:5px; font-weight:bold">L</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Layer-7-For-All-Streaming-Video.html" onclick="getcontent(this); return truefalseFunction(x);">Layer-7 Regex For All Streaming Video</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Layer-7-For-All-Social-Media-Socmed.html" onclick="getcontent(this); return truefalseFunction(x);">Layer-7 Regex For All Social Media (Socmed)</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Layer-7-For-All-Media-Extention.html" onclick="getcontent(this); return truefalseFunction(x);">Layer-7 Regex For All Media Extention</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Layer-7-For-All-File-Extention.html" onclick="getcontent(this); return truefalseFunction(x);">Layer-7 Regex For All File Extention</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Layer-7-Regex-For-Telkom-Ads.html" onclick="getcontent(this); return truefalseFunction(x);">Layer-7 Regex For Telkom Ads</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Limit-sharing-Hotspot-with-Change-TTL.html" onclick="getcontent(this); return truefalseFunction(x);">Limit sharing Hotspot with 'Change TTL'</a></p></td></tr>  
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-PCC-2-Line-ISP.html" onclick="getcontent(this); return truefalseFunction(x);">LB PCC 2 Line ISP Failover</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-PCC-3-Line-ISP.html" onclick="getcontent(this); return truefalseFunction(x);">LB PCC 3 Line ISP Failover</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-PCC-4-Line-ISP.html" onclick="getcontent(this); return truefalseFunction(x);">LB PCC 4 Line ISP Failover</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-PCC-5-Line-ISP.html" onclick="getcontent(this); return truefalseFunction(x);">LB PCC 5 Line ISP Failover</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-PCC-6-Line-ISP.html" onclick="getcontent(this); return truefalseFunction(x);">LB PCC 6 Line ISP Failover</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-ECMP-2-Line-ISP.html" onclick="getcontent(this); return truefalseFunction(x);">LB ECMP 2 Line ISP Failover</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-ECMP-3-Line-ISP.html" onclick="getcontent(this); return truefalseFunction(x);">LB ECMP 3 Line ISP Failover</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-ECMP-4-Line-ISP.html" onclick="getcontent(this); return truefalseFunction(x);">LB ECMP 4 Line ISP Failover</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-ECMP-5-Line-ISP.html" onclick="getcontent(this); return truefalseFunction(x);">LB ECMP 5 Line ISP Failover</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-ECMP-6-Line-ISP.html" onclick="getcontent(this); return truefalseFunction(x);">LB ECMP 6 Line ISP Failover</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LB-ECMP-Failover-Script.html" onclick="getcontent(this); return truefalseFunction(x);">LB ECMP Failover Script</a></p></td></tr>	
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Log-Parser-Event-Trigger-Script.html" onclick="getcontent(this); return truefalseFunction(x);">Log Parser - Event Trigger Script</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/limiting-a-user-to-a-given-amount-of-traffic-using-queues.html" onclick="getcontent(this); return truefalseFunction(x);">Limiting A User To A Given Amount Of Traffic Using Queues</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Limiting-a-User-To-a-Given-Amount-Of-Traffic-With-User-Levels.html" onclick="getcontent(this); return truefalseFunction(x);">Limiting a User To a Given Amount Of Traffic With User Levels</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Limit-Different-Bandwidth-In-Day-and-Night.html" onclick="getcontent(this); return truefalseFunction(x);">Limit Different Bandwidth In Day and Night</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/LED-Antenna-Alignment.html" onclick="getcontent(this); return truefalseFunction(x);">LED Antenna Alignment</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/logging-average-ccq-and-wireless-clients-stats.html" onclick="getcontent(this); return truefalseFunction(x);">Logging Average CCQ and Wireless Clients Stats</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/logging-snr-and-thruput-values.html" onclick="getcontent(this); return truefalseFunction(x);">Logging SNR and Thruput Values</a></p></td></tr>
<!-- M -->
<tr><td><span id="M" style="margin-left:5px; font-weight:bold">M</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Monitoring-Script.html" onclick="getcontent(this); return truefalseFunction(x);">Monitoring Script</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Mangle-and-Port-UltraViewer.html" onclick="getcontent(this); return truefalseFunction(x);">Mangle and Port UltraViewer (Remote Dekstop)</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Mangle-and-Port-TeamViewer.html" onclick="getcontent(this); return truefalseFunction(x);">Mangle and Port TeamViewer (Remote Dekstop)</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Mangle-and-Port-AnyDesk.html" onclick="getcontent(this); return truefalseFunction(x);">Mangle and Port AnyDesk (Remote Dekstop)</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Mangle-and-Port-RDC.html" onclick="getcontent(this); return truefalseFunction(x);">Mangle and Port Remote Desktop Connection (RDC)</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Mangle-and-Port-chrome-remote-desktop.html" onclick="getcontent(this); return truefalseFunction(x);">Mangle and Port Google Chrome Remote Desktop</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Mangle-and-Port-employeespro.html" onclick="getcontent(this); return truefalseFunction(x);">Mangle and Port Net Monitoring for employees pro</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Mangle-and-Port-VNC.html" onclick="getcontent(this); return truefalseFunction(x);">Mangle and Port VNC (Virtual Network Computing</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/make-sure-all-clients-use-router-dns.html" onclick="getcontent(this); return truefalseFunction(x);">Make Sure All Clients Use Router Dns</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Make-an-Automated-Configuration-and-Uninstall.html" onclick="getcontent(this); return truefalseFunction(x);">Make an Automated Configuration and Uninstall</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Monitor-Logs-Send-Email-Alert-Or-Run-Script.html" onclick="getcontent(this); return truefalseFunction(x);">Monitor Logs, Send Email Alert Or Run Script</a></p></td></tr>
<!-- N -->
<tr><td><span id="N" style="margin-left:5px; font-weight:bold">N</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/No-Mark-Without-Mangle-in-Queue-Tree.html" onclick="getcontent(this); return truefalseFunction(x);">No-Mark Without Mangle in Queue Tree</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/notification-when-public-ip-address-router-changes-to-email-or-telegram.html" onclick="getcontent(this); return truefalseFunction(x);">Notification When Public IP Address Router Changes</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/notification-of-device-connection-to-the-network.html" onclick="getcontent(this); return truefalseFunction(x);">Notification Of Device Connection To The Network</a></p></td></tr> 
<!-- 0 -->
<tr><td><span id="O" style="margin-left:5px; font-weight:bold">O</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/only-1-hotspot-id-can-login-together.html" onclick="getcontent(this); return truefalseFunction(x);">Only 1 Hotspot Id Can Login Together</a></p></td></tr> 
<!-- P -->
<tr><td><span id="P" style="margin-left:5px; font-weight:bold">P</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/PPPoE-Secret-Random-Username-and-Password.html" onclick="getcontent(this); return truefalseFunction(x);">PPPoE Secret Random Username and Password</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Port-Forward-for-All-Remote-Desktop.html" onclick="getcontent(this); return truefalseFunction(x);">Port Forward for All Remote Desktop</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/password-ont-modem-telkom-indihome.html" onclick="getcontent(this); return truefalseFunction(x);">Password and username ONT Modem Telkom Indihome</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Protect-Modem-And-Webfig-Pages-Using-Internal-Proxy.html" onclick="getcontent(this); return truefalseFunction(x);">Protect Modem And Webfig Pages Using Internal Proxy</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Pseudo-Random-Number-Generator.html" onclick="getcontent(this); return truefalseFunction(x);">Pseudo Random Number Generator</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Ping-ICMP-To-Routing-ISP-Games.html" onclick="getcontent(this); return truefalseFunction(x);">Ping ICMP To Routing ISP Games</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/port-game-online-pc-games.html" onclick="getcontent(this); return truefalseFunction(x);">Port Game Online for PC Games</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/port-game-online-mobile-games.html" onclick="getcontent(this); return truefalseFunction(x);">Port Game Online for Mobile Games</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/port-game-online-web-games.html" onclick="getcontent(this); return truefalseFunction(x);">Port Game Online for Web Games</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Protect-Router-from-Port-Scanner.html" onclick="getcontent(this); return truefalseFunction(x);">Protect Router from Port Scanner</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/protect-connection-with-anti-netcut.html" onclick="getcontent(this); return truefalseFunction(x);">Protect Connection With ANTI NETCUT</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Protect-web-open-porxy.html" onclick="getcontent(this); return truefalseFunction(x);">Protect Open Web proxy</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Protect-Open-Recursive-DNS.html" onclick="getcontent(this); return truefalseFunction(x);">Protect Open Recursive DNS</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/protect-default-service-port.html" onclick="getcontent(this); return truefalseFunction(x);">Protect Default Port Service From WAN</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/protect-mikrotik-from-ddos.html" onclick="getcontent(this); return truefalseFunction(x);">Protect Router From DDOS Attacks</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/protect-mikrotik-from-exploit.html" onclick="getcontent(this); return truefalseFunction(x);">Protect Router From Exploit (VULNERABILITY)</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/port-knocking-icmp.html" onclick="getcontent(this); return truefalseFunction(x);">Port Knocking Maker or Generator Icmp + Packet Size</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Parse-file-to-add-ppp-secrets.html" onclick="getcontent(this); return truefalseFunction(x);">Parse File To Add PPP Secrets</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Print-function.html" onclick="getcontent(this); return truefalseFunction(x);">Print Function</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Parent-Simple-Queue-Always-On-Top.html" onclick="getcontent(this); return truefalseFunction(x);">Parent Simple Queue Always On Top</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/paypal-with-hotspot-and-walled-garden-bypass.html" onclick="getcontent(this); return truefalseFunction(x);">Paypal With Hotspot And Walled Garden Bypass</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/PPP-Keepalive-ping.html" onclick="getcontent(this); return truefalseFunction(x);">PPP Keepalive Ping</a></p></td></tr>
<!-- Q -->
<tr><td><span id="Q" style="margin-left:5px; font-weight:bold">Q</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Remove-Default-Configuration.html" onclick="getcontent(this); return truefalseFunction(x);">Quick Set Remove Config to Default Configuration</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Queue-Tree-And-E-Mailing-Stats.html" onclick="getcontent(this); return truefalseFunction(x);">Queue Tree And E-Mailing Stats</a></p></td></tr>
<!-- R -->
<tr><td><span id="R" style="margin-left:5px; font-weight:bold">R</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/restore-mac-address-to-default-interface.html" onclick="getcontent(this); return truefalseFunction(x);">Restore MAC-Address to Default Interface</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Remove-Default-Simple-Queue-from-Hotspot.html" onclick="getcontent(this); return truefalseFunction(x);">Remove Default Simple Queue From Hotspot</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Routing-FailOver-with-2-ISP-gateway.html" onclick="getcontent(this); return truefalseFunction(x);">Routing FailOver with 2 ISP (gateway)</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Routing-FailOver-with-3-ISP-gateway.html" onclick="getcontent(this); return truefalseFunction(x);">Routing FailOver with 3 ISP (gateway)</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Reset-Mikrotik-via-Script-from-Terminal.html" onclick="getcontent(this); return truefalseFunction(x);">Reset Mikrotik via Script from Terminal</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Remove-Files-On-Mikrotik.html" onclick="getcontent(this); return truefalseFunction(x);">Remove or Delete Files On Mikrotik</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/remote-ip-public-static.html" onclick="getcontent(this); return truefalseFunction(x);">Remote IP Public Static For Secondary Gateway</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Read-and-write-large-files.html" onclick="getcontent(this); return truefalseFunction(x);">Read and Write Large Files</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Resolve-host-name.html" onclick="getcontent(this); return truefalseFunction(x);">Resolve host-name</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/reset-counter.html" onclick="getcontent(this); return truefalseFunction(x);">Reset All Counter</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/fail-over-recursive-gateway.html" onclick="getcontent(this); return truefalseFunction(x);">Recursive Gateway Fail over</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/rebuild-database-User-Manager.html" onclick="getcontent(this); return truefalseFunction(x);">Rebuild DataBase On User Manager</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Reset-DataBase-User-Manager-to-default.html" onclick="getcontent(this); return truefalseFunction(x);">Reset DataBase User Manager</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Routing-Via-A-Dhcp-Allocated-Gateway.html" onclick="getcontent(this); return truefalseFunction(x);">Routing Via A Dhcp Allocated Gateway</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Reboot-mikrotik-from-terminal.html" onclick="getcontent(this); return truefalseFunction(x);">Reboot or Restart Mikrotik From Terminal</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/remove-firewall-address-lists.html" onclick="getcontent(this); return truefalseFunction(x);">Remove Firewall IP Address Lists From Script</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Removing-Busy-Leases-From-Dhcp-Pool.html" onclick="getcontent(this); return truefalseFunction(x);">Removing Busy Leases From Dhcp Pool</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Reboot-Boards-due-to-low-Memory-with-notification.html" onclick="getcontent(this); return truefalseFunction(x);">Reboot Boards due to low Memory with notification</a></p></td></tr>
<!-- S -->
<tr><td><span id="S" style="margin-left:5px; font-weight:bold">S</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/sending-your-self-an-e-mail-with-dsl-interface-ip-address.html" onclick="getcontent(this); return truefalseFunction(x);">Sending Your Self An E-Mail With Dsl Interface Ip Address</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/sending-text-out-over-a-serial-port.html" onclick="getcontent(this); return truefalseFunction(x);">Sending Text Out Over A Serial Port</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Script-to-Create-Directory.html" onclick="getcontent(this); return truefalseFunction(x);">Script to Create Directory</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Scheduled-Check-For-Loaded-Interfaces.html" onclick="getcontent(this); return truefalseFunction(x);">Scheduled Check For Loaded Interfaces</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Scheduled-WAN-Disconnect.html" onclick="getcontent(this); return truefalseFunction(x);">Scheduled WAN Disconnect</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/setup-and-troubleshooting-routeros-email-function.html" onclick="getcontent(this); return truefalseFunction(x);">Setup And Troubleshooting Routeros Email Function</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/super-mario-bross-theme-sound-beep.html" onclick="getcontent(this); return truefalseFunction(x);">Super Mario Bross Theme Sound Beep</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Setting-NTP-Network-Time-Protocol.html" onclick="getcontent(this); return truefalseFunction(x);">SNTP Client or NTP Network Time Protocol</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Solution-Hotspot-login-Page-LB-PCC.html" onclick="getcontent(this); return truefalseFunction(x);">Solution For Hotspot Login Page with LB PCC</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Signalorg-Mangle-And-Queue.html" onclick="getcontent(this); return truefalseFunction(x);">Signal.org Mangle And Queue</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Speedtest-Regexp-Layer7.html" onclick="getcontent(this); return truefalseFunction(x);">Speedtest Regexp Layer-7</a></p></td></tr> 
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/strip-ip-address-netmask.html" onclick="getcontent(this); return truefalseFunction(x);">Strip IP Address without netmask</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/A-script-to-set-up-WAN-or-LAN-or-WLAN-to-get-you-started.html" onclick="getcontent(this); return truefalseFunction(x);">Set Up WAN/LAN/WLAN To Get You Started</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Setting-Static-DNS-Record-For-Each-DHCP-Lease.html" onclick="getcontent(this); return truefalseFunction(x);">Setting Static DNS Record For Each DHCP Lease</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Send-Email-About-Reboot.html" onclick="getcontent(this); return truefalseFunction(x);">Send Email About Reboot</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Securing-L2TP-Server-for-IPSec.html" onclick="getcontent(this); return truefalseFunction(x);">Securing L2TP Server for IPSec</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/sync-address-list-from-dns-lookup-results.html" onclick="getcontent(this); return truefalseFunction(x);">Sync Address List from DNS Lookup Results</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/SXT-5HnD-Alignment-Script.html" onclick="getcontent(this); return truefalseFunction(x);">SXT 5HnD Alignment Script</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Sync-Address-List-with-DNS-Cache.html" onclick="getcontent(this); return truefalseFunction(x);">Sync Address List with DNS Cache</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Sending-Mails-When-Battery-Low.html" onclick="getcontent(this); return truefalseFunction(x);">Sending Mails When Battery Low (UPS Script)</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/sending-power-on-notification-to-telegram.html" onclick="getcontent(this); return truefalseFunction(x);">Sending Power On Notification To Telegram</a></p></td></tr>
<!-- T -->
<tr><td><span id="T" style="margin-left:5px; font-weight:bold">T</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Mikrotik-Terminal-Command-Line-and-Keyboard-Command.html" onclick="getcontent(this); return truefalseFunction(x);">Terminal Command Line and Keyboard Command</a></p></td></tr> 
<!-- U -->
<tr><td><span id="U" style="margin-left:5px; font-weight:bold">U</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Updating-The-Firmware-From-Terminal.html" onclick="getcontent(this); return truefalseFunction(x);">Updating The Firmware From Terminal</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/use-mikrotik-as-fail2ban-firewall.html" onclick="getcontent(this); return truefalseFunction(x);">Use Mikrotik as Fail2ban firewall</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Use-string-as-function.html" onclick="getcontent(this); return truefalseFunction(x);">Use String as Function</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/update-static-dns-entries-every-10-minutes.html" onclick="getcontent(this); return truefalseFunction(x);">Update Static DNS Entries Every 10 Minutes</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/UPnP-Multi-WAN.html" onclick="getcontent(this); return truefalseFunction(x);">UPnP Multi-WAN</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Use-Functions-in-CMD-Script.html" onclick="getcontent(this); return truefalseFunction(x);">Use Functions in CMD Script</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Use-Host-Names-In-Firewall-Rules.html" onclick="getcontent(this); return truefalseFunction(x);">Use Host Names In Firewall Rules</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Using-scripting-to-overcome-the-inability-to-specify-number-ranges-on-the-command-line.html" onclick="getcontent(this); return truefalseFunction(x);">Using Scripting To Overcome The Inability To Specify Number Ranges On The Command Line</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Using-Fetch-and-Scripting-to-add-IP-Address-Lists.html" onclick="getcontent(this); return truefalseFunction(x);">Using Fetch and Scripting to add IP Address Lists</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/upnp-redirect-the-port-to-mikrotik-device.html" onclick="getcontent(this); return truefalseFunction(x);">UPnP: Redirect The Port To Mikrotik Device</a></p></td></tr>
<!-- V -->
<tr><td><span id="V" style="margin-left:5px; font-weight:bold">V</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Virus-and-Malware-Port-Blocking.html" onclick="getcontent(this); return truefalseFunction(x);">Virus and Malware Port Blocking</a></p></td></tr>
<!-- W -->
<tr><td><span id="W" style="margin-left:5px; font-weight:bold">W</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/wms-auto-login.html" onclick="getcontent(this); return truefalseFunction(x);">WMS or Wifi.id Auto Login</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Write-simple-queue-stats-in-multiple-files.html" onclick="getcontent(this); return truefalseFunction(x);">Write simple queue stats in multiple files</a></p></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/wake-on-lan-before-connection-to-remote-desktop.html" onclick="getcontent(this); return truefalseFunction(x);">Wake On Lan Before Connection To Remote Desktop</a></p></td></tr>
<!-- X -->
<tr><td><span id="X" style="margin-left:5px; font-weight:bold">X</span></td></tr>
<!-- Y -->
<tr><td><span id="Y" style="margin-left:5px; font-weight:bold">Y</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Youtube-Restricted-Mode-use-DNS-Static.html" onclick="getcontent(this); return truefalseFunction(x);">Youtube Restricted Mode use DNS Static</a></p></td></tr>
<!-- Z -->
<tr><td><span id="Z" style="margin-left:5px; font-weight:bold">Z</span></td></tr>
<tr><td><p><a class="linkPress" href="https://mikrotiktool.github.io/mikrotik/Complete-zoom-mangle-and-queue-.html" onclick="getcontent(this); return truefalseFunction(x);">Zoom Meeting Mangle and Queue</a></p></td></tr>
<!-- WELCOME -->
<tr><td><p><a class="linkPress active1" href="https://mikrotiktool.github.io/mikrotik/welcome.html" onclick="getcontent(this); return truefalseFunction(x);">Welcome</a></p></td></tr>
</table>
</div>
<div class='backtotop'><a href='#top'>Back to Top</a></div>
</div>
<div class="content">
<div class="list-mangle">
<div id="reloadads1" style="width:100%; height:90px; margin-top:10px; margin-left:5px; margin-bottom:10px">
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<!-- MIKROTIK SCRIPT DB3 -->
<ins class="adsbygoogle"
style="display:inline-block;width:728px;height:90px"
data-ad-client="ca-pub-3815059393374366"
data-ad-slot="4637807296"></ins>
<script>
(adsbygoogle = window.adsbygoogle || []).push({});
</script> 
</div>
<div class="welcome" id="welcome">
<span style="float:left; display:inline;margin-right:6px; padding-top:-4px !important">
<img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="Mikrotik Script RouterOS Database">
</span>
<h2>COMPLETE MIKROTIK SCRIPT ROUTEROS DATABASE</h2>
<pre>
<span style="line-height: 1.4;">
We try to collect all the scripts found on the internet and combine them in one DataBase. Scripting host provides a way to automate some router maintenance tasks by means of executing user-defined scripts bounded to some event occurrence. Scripts can be stored in the Script repository or can be written directly to the console. The events used to trigger script execution include, but are not limited to the System Scheduler, the Traffic Monitoring Tool, and the Netwatch Tool generated events.</span>
<span style="font-size:14px; font-weight:bold;">
MMM      MMM       KKK                          TTTTTTTTTTT      KKK
MMMM    MMMM       KKK                          TTTTTTTTTTT      KKK
MMM MMMM MMM  III  KKK  KKK  RRRRRR     OOOOOO      TTT     III  KKK  KKK
MMM  MM  MMM  III  KKKKK     RRR  RRR  OOO  OOO     TTT     III  KKKKK
MMM      MMM  III  KKK KKK   RRRRRR    OOO  OOO     TTT     III  KKK KKK
MMM      MMM  III  KKK  KKK  RRR  RRR   OOOOOO      TTT     III  KKK  KKK

Mikrotik RouterOS Script          mikrotiktool.github.io/mikrotik</span>
<span style="line-height: 1.4; text-align:justify !important;">
Enjoy the most complete script to make it easier for you to learn Mikrotik RouterOS scripts!

Send Your Mikrotik RouterOS Script For Contribution -> <a href="https://docs.google.com/forms/d/e/1FAIpQLSfbuzYtFCI57y7nJHgnL1w0aiCY5g45Uon90u3XQm6pAi56BQ/viewform"><b>Upload</b></a></span>
</pre>
</div>
<iframe id="change-iframe-url" src=""></iframe>
<div id="reloadads2" style="width:100%; height:90px; margin-top:10px; margin-left:5px;">
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<!-- MIKROTIK SCRIPT DB3 -->
<ins class="adsbygoogle"
style="display:inline-block;width:728px;height:90px"
data-ad-client="ca-pub-3815059393374366"
data-ad-slot="4637807296"></ins>
<script>
(adsbygoogle = window.adsbygoogle || []).push({});
</script> 
</div> 
</div>
</div>
<div class="footer">
© Copyright 2020-2021 <a href="https://mikrotiktool.github.io/mikrotik">Mikrotik Script RouterOS</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a> -  Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a>  
</div>
</div>
<script>
var _0x16a0=['indexOf','welcome','active1','setAttribute','reloadads1','click','(max-width:\x201024px)','scrollTop','display','matches','myTable','change-iframe-url','getAttribute','696989FcttDL','textContent','innerHTML','src','\x20active1','innerText','matchMedia','className','length','block','getElementsByClassName','addEventListener','3iVBdFk','405887ZZdEkn','hidden','1Hgacrg','href','1265053aZWOax','getElementById','316452UqFqVQ','411795QOexzs','linkPress','style','reloadads2','documentElement','1198806QFpvJw','none','body','1uBHBOu','378848kbnXit','getElementsByTagName','toUpperCase'];var _0x1f01=function(_0x406de6,_0x472444){_0x406de6=_0x406de6-0xe3;var _0x16a032=_0x16a0[_0x406de6];return _0x16a032;};var _0xb2676a=_0x1f01;(function(_0xaeba55,_0x459183){var _0x39382a=_0x1f01;while(!![]){try{var _0x235bb5=parseInt(_0x39382a(0x101))+-parseInt(_0x39382a(0xfb))*-parseInt(_0x39382a(0xfa))+parseInt(_0x39382a(0x107))+parseInt(_0x39382a(0x102))+-parseInt(_0x39382a(0x10b))+parseInt(_0x39382a(0x10a))*-parseInt(_0x39382a(0xff))+parseInt(_0x39382a(0xee))*-parseInt(_0x39382a(0xfd));if(_0x235bb5===_0x459183)break;else _0xaeba55['push'](_0xaeba55['shift']());}catch(_0x2c95cd){_0xaeba55['push'](_0xaeba55['shift']());}}}(_0x16a0,0xc43f0));function scrollFunction(){var _0x727571=_0x1f01;document[_0x727571(0x109)][_0x727571(0xe8)]>0x32||document[_0x727571(0x106)][_0x727571(0xe8)]>0x32?mybutton['style'][_0x727571(0xe9)]=_0x727571(0xf7):mybutton[_0x727571(0x104)]['display']='none';}function topFunction(){var _0x9b4b84=_0x1f01;document[_0x9b4b84(0x109)]['scrollTop']=0x0,document[_0x9b4b84(0x106)][_0x9b4b84(0xe8)]=0x0;}var mybutton=document[_0xb2676a(0x100)]('myBtn');window['onscroll']=function(){scrollFunction();};function truefalseFunction(_0x12174e){var _0x2b5d07=_0xb2676a;return _0x12174e[_0x2b5d07(0xea)]?!![]:![];}var x=window[_0xb2676a(0xf4)](_0xb2676a(0xe7));truefalseFunction(x),x['addListener'](truefalseFunction),document[_0xb2676a(0x100)](_0xb2676a(0xec))[_0xb2676a(0xfc)]=!![];function reloadads1(){var _0x325826=_0xb2676a;document[_0x325826(0x100)]('reloadads1')['innerHTML']=document['getElementById'](_0x325826(0xe5))[_0x325826(0xf0)];}function reloadads2(){var _0xd7209b=_0xb2676a;document[_0xd7209b(0x100)](_0xd7209b(0x105))[_0xd7209b(0xf0)]=document['getElementById'](_0xd7209b(0x105))[_0xd7209b(0xf0)];}function content(_0x25d5cf){var _0xd105d7=_0xb2676a,_0x190678=document[_0xd105d7(0x100)](_0xd105d7(0xec));_0x190678[_0xd105d7(0xe4)](_0xd105d7(0xf1),_0x25d5cf),document[_0xd105d7(0x100)]('change-iframe-url')[_0xd105d7(0xfc)]=![],document['getElementById'](_0xd105d7(0x10f))['hidden']=!![];}function getcontent(_0xb734e9){var _0x6b131c=_0xb2676a,_0x536f33=document[_0x6b131c(0x100)](_0x6b131c(0xec)),_0x61ad59=_0xb734e9[_0x6b131c(0xed)](_0x6b131c(0xfe));_0x536f33[_0x6b131c(0xe4)](_0x6b131c(0xf1),_0x61ad59),document['getElementById']('change-iframe-url')[_0x6b131c(0xfc)]=![],document['getElementById']('welcome')[_0x6b131c(0xfc)]=!![],reloadads1(),reloadads2();}function myFunctionSearch(){var _0x2f949e=_0xb2676a,_0x2a167b,_0x17830d,_0x1ca81b,_0x5168f6,_0x34534d,_0x37d9c4,_0x5112bf;_0x2a167b=document[_0x2f949e(0x100)]('myInput'),_0x17830d=_0x2a167b['value']['toUpperCase'](),_0x1ca81b=document[_0x2f949e(0x100)](_0x2f949e(0xeb)),_0x5168f6=_0x1ca81b['getElementsByTagName']('tr');for(_0x37d9c4=0x0;_0x37d9c4<_0x5168f6[_0x2f949e(0xf6)];_0x37d9c4++){_0x34534d=_0x5168f6[_0x37d9c4][_0x2f949e(0x10c)]('td')[0x0],_0x34534d&&(_0x5112bf=_0x34534d[_0x2f949e(0xef)]||_0x34534d[_0x2f949e(0xf3)],_0x5112bf[_0x2f949e(0x10d)]()[_0x2f949e(0x10e)](_0x17830d)>-0x1?_0x5168f6[_0x37d9c4]['style'][_0x2f949e(0xe9)]='':_0x5168f6[_0x37d9c4][_0x2f949e(0x104)]['display']=_0x2f949e(0x108));}}var header=document[_0xb2676a(0x100)]('myTable'),btns=header['getElementsByClassName'](_0xb2676a(0x103));for(var i=0x0;i<btns[_0xb2676a(0xf6)];i++){btns[i][_0xb2676a(0xf9)](_0xb2676a(0xe6),function(){var _0x58d0db=_0xb2676a,_0x3361e3=document[_0x58d0db(0xf8)](_0x58d0db(0xe3));_0x3361e3[0x0][_0x58d0db(0xf5)]=_0x3361e3[0x0][_0x58d0db(0xf5)]['replace'](_0x58d0db(0xf2),''),this[_0x58d0db(0xf5)]+='\x20active1';});}
</script>
</body>
</html>
