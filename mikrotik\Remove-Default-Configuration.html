<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Quick Set Remove Config to Default Configuration - MikroTik Script RouterOS</title>
<meta content='Quick Set Remove Config to Default Configuration - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Quick Set Remove Config to Default Configuration - MikroTik Script RouterOS</h1>
<pre>The following default configuration has been installed on your router:

Welcome to RouterOS!
   1) Set a strong router password in the System > Users menu
   2) Upgrade the software in the System > Packages menu
   3) Enable firewall on untrusted networks
-----------------------------------------------------------------------------
RouterMode:
 * WAN port is protected by firewall and enabled DHCP client
 * Ethernet interfaces (except WAN port/s) are part of LAN bridge
LAN Configuration:
    IP address ************/24 is set on bridge (LAN port)
    DHCP Server: enabled;
    DNS: enabled;
WAN (gateway) Configuration:
    gateway:  ether1 ;
    ip4 firewall:  enabled;
    NAT:   enabled;
    DHCP Client: enabled;

You can click on "Show Script" to see the exact commands that are used to add and remove this default configuration.To remove this default configuration click on "Remove Configuration" or click on "OK" to continue. 

NOTE: If you are connected using the above IP and you remove it, you will be disconnected.

<code class="routeros">#| Welcome to RouterOS!
#|    1) Set a strong router password in the System > Users menu
#|    2) Upgrade the software in the System > Packages menu
#|    3) Enable firewall on untrusted networks
#| -----------------------------------------------------------------------------
#| RouterMode:
#|  * WAN port is protected by firewall and enabled DHCP client
#|  * Ethernet interfaces (except WAN port/s) are part of LAN bridge
#| LAN Configuration:
#|     IP address ************/24 is set on bridge (LAN port)
#|     DHCP Server: enabled;
#|     DNS: enabled;
#| WAN (gateway) Configuration:
#|     gateway:  ether1 ;
#|     ip4 firewall:  enabled;
#|     NAT:   enabled;
#|     DHCP Client: enabled;

:global defconfMode;
:log info "Starting defconf script";
#-------------------------------------------------------------------------------
# Apply configuration.
# these commands are executed after installation or configuration reset
#-------------------------------------------------------------------------------
:if ($action = "apply") do={
  # wait for interfaces
  :local count 0;
  :while ([/interface ethernet find] = "") do={
    :if ($count = 30) do={
      :log warning "DefConf: Unable to find ethernet interfaces";
      /quit;
    }
    :delay 1s; :set count ($count +1); 
  };
 /interface list add name=WAN comment="defconf"
 /interface list add name=LAN comment="defconf"
 /interface bridge
   add name=bridge disabled=no auto-mac=yes protocol-mode=rstp comment=defconf;
 :local bMACIsSet 0;
 :foreach k in=[/interface find where !(slave=yes  || name="ether1" || name~"bridge")] do={
   :local tmpPortName [/interface get $k name];
   :if ($bMACIsSet = 0) do={
     :if ([/interface get $k type] = "ether") do={
       /interface bridge set "bridge" auto-mac=no admin-mac=[/interface get $tmpPortName mac-address];
       :set bMACIsSet 1;
     }
   }
   /interface bridge port
     add bridge=bridge interface=$tmpPortName comment=defconf;
 }
   /ip pool add name="default-dhcp" ranges=************0-**************;
   /ip dhcp-server
     add name=defconf address-pool="default-dhcp" interface=bridge lease-time=10m disabled=no;
   /ip dhcp-server network
     add address=************/24 gateway=************ comment="defconf";
  /ip address add address=************/24 interface=bridge comment="defconf";
 /ip dns {
     set allow-remote-requests=yes
     static add name=router.lan address=************ comment=defconf
 }

   /ip dhcp-client add interface=ether1 disabled=no comment="defconf";
 /interface list member add list=LAN interface=bridge comment="defconf"
 /interface list member add list=WAN interface=ether1 comment="defconf"
 /ip firewall nat add chain=srcnat out-interface-list=WAN ipsec-policy=out,none action=masquerade comment="defconf: masquerade"
 /ip firewall {
   filter add chain=input action=accept connection-state=established,related,untracked comment="defconf: accept established,related,untracked"
   filter add chain=input action=drop connection-state=invalid comment="defconf: drop invalid"
   filter add chain=input action=accept protocol=icmp comment="defconf: accept ICMP"
   filter add chain=input action=accept dst-address=127.0.0.1 comment="defconf: accept to local loopback (for CAPsMAN)"
   filter add chain=input action=drop in-interface-list=!LAN comment="defconf: drop all not coming from LAN"
   filter add chain=forward action=accept ipsec-policy=in,ipsec comment="defconf: accept in ipsec policy"
   filter add chain=forward action=accept ipsec-policy=out,ipsec comment="defconf: accept out ipsec policy"
   filter add chain=forward action=fasttrack-connection connection-state=established,related comment="defconf: fasttrack"
   filter add chain=forward action=accept connection-state=established,related,untracked comment="defconf: accept established,related, untracked"
   filter add chain=forward action=drop connection-state=invalid comment="defconf: drop invalid"
   filter add chain=forward action=drop connection-state=new connection-nat-state=!dstnat in-interface-list=WAN comment="defconf: drop all from WAN not DSTNATed"
 }
   /ip neighbor discovery-settings set discover-interface-list=LAN
   /tool mac-server set allowed-interface-list=LAN
   /tool mac-server mac-winbox set allowed-interface-list=LAN
}
#-------------------------------------------------------------------------------
# Revert configuration.
# these commands are executed if user requests to remove default configuration
#-------------------------------------------------------------------------------
:if ($action = "revert") do={
/user set admin password=""
 /system routerboard mode-button set enabled=no
 /system routerboard mode-button set on-event=""
 /system script remove [find comment~"defconf"]
 /ip firewall filter remove [find comment~"defconf"]
 /ip firewall nat remove [find comment~"defconf"]
 /interface list member remove [find comment~"defconf"]
 /interface detect-internet set detect-interface-list=none
 /interface detect-internet set lan-interface-list=none
 /interface detect-internet set wan-interface-list=none
 /interface detect-internet set internet-interface-list=none
 /interface list remove [find comment~"defconf"]
 /tool mac-server set allowed-interface-list=all
 /tool mac-server mac-winbox set allowed-interface-list=all
 /ip neighbor discovery-settings set discover-interface-list=!dynamic
   :local o [/ip dhcp-server network find comment="defconf"]
   :if ([:len $o] != 0) do={ /ip dhcp-server network remove $o }
   :local o [/ip dhcp-server find name="defconf" !disabled]
   :if ([:len $o] != 0) do={ /ip dhcp-server remove $o }
   /ip pool {
     :local o [find name="default-dhcp" ranges=************0-**************]
     :if ([:len $o] != 0) do={ remove $o }
   }
   :local o [/ip dhcp-client find comment="defconf"]
   :if ([:len $o] != 0) do={ /ip dhcp-client remove $o }
 /ip dns {
   set allow-remote-requests=no
   :local o [static find comment="defconf"]
   :if ([:len $o] != 0) do={ static remove $o }
 }
 /ip address {
   :local o [find comment="defconf"]
   :if ([:len $o] != 0) do={ remove $o }
 }
 :foreach iface in=[/interface ethernet find] do={
   /interface ethernet set $iface name=[get $iface default-name]
 }
 /interface bridge port remove [find comment="defconf"]
 /interface bridge remove [find comment="defconf"]
 /interface bonding remove [find comment="defconf"]
 /interface wireless cap set enabled=no interfaces="" caps-man-addresses=""
  /caps-man manager set enabled=no
  /caps-man manager interface remove [find comment="defconf"]
  /caps-man manager interface set [ find default=yes ] forbid=no
  /caps-man provisioning remove [find comment="defconf"]
  /caps-man configuration remove [find comment="defconf"]
}
:log info Defconf_script_finished;
:set defconfMode;</code>
Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>


