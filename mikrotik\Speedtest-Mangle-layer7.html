
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Complete Mangle Speedtest with Layer-7 - Mikrotik Script RouterOS</title>
<meta content='Complete Mangle Speedtest with Layer-7 - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>  
</head> 
<body>
<div id="hidelink"></div>
<h1>Complete Mangle Speedtest with Layer-7 - Mikrotik Script RouterOS</h1>
<pre>Complete Mangle Speedtest with Layer-7

<code class="routeros">/queue tree
add limit-at=100M max-limit=100M name=SPEEDTEST parent=global priority=1 queue=default
add limit-at=100M max-limit=100M name="1.SpeedTest UP" packet-mark=speedtest_pkt-up parent=SPEEDTEST priority=1 queue=default
add limit-at=100M max-limit=100M name="2.SpeedTest DOWN" packet-mark=speedtest_pkt-down parent=SPEEDTEST priority=1 queue=default

/ip firewall layer7-protocol
add name=speedtest regexp="^.+(speedtest).*\$"

/ip firewall mangle
add action=mark-connection chain=prerouting layer7-protocol=speedtest new-connection-mark=speedtest_conn
add action=mark-connection chain=prerouting dst-port=8080 new-connection-mark=speedtest_conn protocol=tcp
add action=mark-connection chain=postrouting new-connection-mark=speedtest_conn protocol=tcp src-port=8080
add action=mark-packet chain=prerouting connection-mark=speedtest_conn new-packet-mark=speedtest_pkt-up passthrough=no src-address=***********/24
add action=mark-packet chain=prerouting connection-mark=speedtest_conn dst-address=***********/24 new-packet-mark=speedtest_pkt-down passthrough=no
add action=mark-packet chain=postrouting connection-mark=speedtest_conn new-packet-mark=speedtest_pkt-up passthrough=no src-address=***********/24
add action=mark-packet chain=postrouting connection-mark=speedtest_conn dst-address=***********/24 new-packet-mark=speedtest_pkt-down passthrough=no</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
