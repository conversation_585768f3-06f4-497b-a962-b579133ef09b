<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Converting network and gateway from routing table to hexadecimal string - Mikrotik Script RouterOS</title>
<meta content='Converting network and gateway from routing table to hexadecimal string - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head> 
<body>
<div id="hidelink"></div>
<h1>Converting network and gateway from routing table to hexadecimal string - Mikrotik Script RouterOS</h1>
<pre>
Version of the script giving result as DHCP option 121 (RFC 3442)

<code class="routeros"># Second script converting network and gateway from routing table to hexadecimal string
# according to RFC 3442 - DHCP option 121
# result=0x<prefix><network><gateway>
# example: dst-address=***********/24 gateway=*********** => result=0x18c0a802c0a80101
#
# Network can have 0 to 4 octets:
# Width of subnet mask     Number of significant octets
#              0                     0
#           1- 8                     1
#           9-16                     2
#          17-24                     3
#          25-32                     4
#
# (c) Daniel Starnowski 2011
#
:foreach route in=[/ip route find] do={
:local dst [/ip route get $route dst-address];
:local gateway [/ip route get $route gateway];
:if ($gateway=[:toip $gateway]) do={
  :local total ($dst . "." . $gateway);
  :local result "0x";
  :local hextable [:toarray "0,1,2,3,4,5,6,7,8,9,a,b,c,d,e,f"];
  :local total2 "";
  :local decimal;
  :local division;
  :local i;
  :local j;
  :for i from=1 to=[:len $total] step=1 do={
   :set j [:pick $total ($i-1) $i];
   :if (($j=".") or ($j="/")) do={:set j ","};
   :set total2 ($total2 . $j);
   };
  :set total2 [:toarray $total2];

  :set decimal [:pick $total2 4]
  :set division ($decimal / 16);
  :set result ($result . [:pick $hextable $division]);
  :set result ($result . [:pick $hextable ($decimal - (16 * $division))]);
  :local omit ((32-[:pick $total2 4]) / 8);

  :for i from=($omit+1) to=8 step=1 do={
   :set j $i;
   :if ($j<5) do={:set j ($j-1-$omit);};
   :set decimal [:pick $total2 $j]
   :set division ($decimal / 16);
   :set result ($result . [:pick $hextable $division]);
   :set result ($result . [:pick $hextable ($decimal - (16 * $division))]);
   };
  :put ($result . " for " . $dst);
} else={:put ("Gateway is not a single IP address for ".$dst);}
}</code>

Version of the script with full 4 bytes of destination network

<code class="routeros"># Script converting network and gateway from routing table to hexadecimal string
# dst-address=***********/24 gateway=*********** => result=0x18c0a80200c0a80101
#
# (c) Daniel Starnowski 2011
#
:foreach route in=[/ip route find] do={
:local dst [/ip route get $route dst-address];
:local gateway [/ip route get $route gateway];
:if ($gateway=[:toip $gateway]) do={
  :local total ($dst . "." . $gateway);
  :local result "0x";
  :local hextable [:toarray "0,1,2,3,4,5,6,7,8,9,a,b,c,d,e,f"];
  :local total2 "";
  :local decimal;
  :local division;
  :local i;
  :local j;
  :for i from=1 to=[:len $total] step=1 do={
   :set j [:pick $total ($i-1) $i];
   :if (($j=".") or ($j="/")) do={:set j ","};
   :set total2 ($total2 . $j);
   };
  :set total2 [:toarray $total2];

  :for i from=0 to=8 step=1 do={
   :set j $i;
   :if ($j<5) do={if ($j=0) do={:set j 4;} else={:set j ($j-1);};};
   :set decimal [:pick $total2 $j ($j+1)]
   :set division ($decimal / 16);
   :set result ($result . [:pick $hextable $division]);
   :set result ($result . [:pick $hextable ($decimal - (16 * $division))]);
   };
  :put $result;
} else={:put ("Gateway is not a single IP address for ".$dst);}}</code>
Credit: https://wiki.mikrotik.com/wiki/Converting_network_and_gateway_from_routing_table_to_hexadecimal_string
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
