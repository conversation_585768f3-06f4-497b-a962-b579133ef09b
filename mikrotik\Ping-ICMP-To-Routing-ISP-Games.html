<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Ping ICMP To Routing ISP Games - MikroTik Script RouterOS</title>
<meta content='Ping ICMP To Routing ISP Games - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Ping ICMP To Routing ISP Games - MikroTik Script RouterOS</h1>
<pre>There are 3 ISPs, 2 ISPs in LB for browsing purposes and 1 more ISP specifically for game lines, then where do the replay ping (icmp) results actually have to be monitored or displayed real to clients from these 3 ISPs?

Let's just say that currently we have 3 ISPs that separate the path between gaming and browsing, the first 2 ISPs are specifically loaded with load balancing for browsing and the other is specifically for game lines, without special routing, when we or our clients ping from a PC or handphone, the router of course will immediately direct the ping to the main route, in this case the browsing path (main) is selected as distance 1, logically why do we check the main line ping while there are other priorities that prefer ping in this case to monitor the game connection path, Of course I would prefer to divert the ping routing to a game path that deserves to be monitored.

<code class="routeros">/ip route
add check-gateway=ping gateway=*********** routing-mark=icmp distance=1
/ip firewall mangle
add action=mark-routing chain=prerouting in-interface=LAN new-routing-mark=icmp passthrough=no protocol=icmp distance=1</code>

notes: please change the gateway = *********** according to the game line modem gateway ip and in-interface = LAN according to the ether local network respectively.

Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

