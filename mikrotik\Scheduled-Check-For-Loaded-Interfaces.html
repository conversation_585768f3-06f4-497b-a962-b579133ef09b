<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Scheduled Check For Loaded Interfaces - MikroTik Script RouterOS</title>
<meta content='Scheduled Check For Loaded Interfaces - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Scheduled Check For Loaded Interfaces - MikroTik Script RouterOS</h1>
<pre><b>Scheduled Check For Loaded Interfaces (auto adding queue to some IP or interface)</b>

If you have MikroTik as main router on your network (business and residential clients using the same downlink) and you want to create QoS for business users.

Simple: If your downlink is about 2Mbps and you want to grant the business clients min 1Mbit. But on the same MikroTik you have connected residential users.
This script can check out-interface (for $sent-bits-per-second/$received-bits-per-second) and if sent/received bits > 1Mbps, script can add queue for some interface or IP address(es).

<code class="routeros">/system script 
add name="tl_down" source="/interface monitor-traffic [/interface find name "interface"] once do { 
:if ($received-bits-per-second > 1048576) do { 
/queue simple add name=(username . "_" . [:pick [/system clock get date] 4 6] . "-" . [:pick [/system clock get date] 0 3]  \
. "-" . [:pick [/system clock get date] 7 11] . "-" . [:pick [/system clock get time] 0 9]) limit-at=262144/262144 \
max-limit=262144/262144 target-addresses="ip address" priority=1
:log warning "Traffic limit added for ip address/network/interface"
} 
}"
add name="tl_remove_down" source="/interface monitor-traffic [/interface find name "interface"] once do { 
:if ($received-bits-per-second < 1048576) do { 
/queue simple remove [/queue simple find target-addresses="ip address"]
:log warning "Traffic limit removed for ip address/network/interface"
} 
}"
/system scheduler
add name="name-unloaded" on-event=tl_remove_down start-date=jan/01/2007 start-time=00:00:00 interval=9m comment="" disabled=no
add name="name-loaded" on-event=tl_down start-date=jan/01/2007 start-time=00:00:00 interval=10m comment="" disabled=no</code>
Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
