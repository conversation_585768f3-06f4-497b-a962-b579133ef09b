<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>LB ECMP 6 Line ISP Failover - Mikrotik Script RouterOS</title>
<meta content='LB ECMP 6 Line ISP Failover - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>  
</head>
<body>
<div id="hidelink"></div>
<h1>LB ECMP 6 Line ISP Failover - Mikrotik Script RouterOS</h1>
<pre>ECMP or Equal Cost Multi Path is a load balancing method that uses the per address-pair connection load balancing method. ECMP allows a router to have more than one gateway to a destination network. Because the method is per address-pair connection, the load balancing system is that each different address on different connections will probably pass through different gateways.

<code class="routeros">################################################
# LOAD BALANCING ECMP SCRIPT GENERATOR
# Date/Time: 2/13/2021, 8:57:47 PM
#  
# Load Balancing ECMP (EQUAL COST MULTI PATH)
################################################

/ip firewall nat
add chain=srcnat out-interface="ether1" action=masquerade 
add chain=srcnat out-interface="ether2" action=masquerade 
add chain=srcnat out-interface="ether3" action=masquerade 
add chain=srcnat out-interface="ether4" action=masquerade 
add chain=srcnat out-interface="ether5" action=masquerade 
add chain=srcnat out-interface="ether6" action=masquerade 

/ip route
add check-gateway=ping distance=1 gateway="***********,***********,***********,***********,***********,***********" 
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether1" 
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether2" 
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether3" 
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether4" 
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether5" 
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether6" 

/ip firewall mangle
add action=mark-connection chain=input in-interface="ether1" new-connection-mark="cm-ether1" 
add action=mark-connection chain=input in-interface="ether2" new-connection-mark="cm-ether2" 
add action=mark-connection chain=input in-interface="ether3" new-connection-mark="cm-ether3" 
add action=mark-connection chain=input in-interface="ether4" new-connection-mark="cm-ether4" 
add action=mark-connection chain=input in-interface="ether5" new-connection-mark="cm-ether5" 
add action=mark-connection chain=input in-interface="ether6" new-connection-mark="cm-ether6" 
add action=mark-routing chain=output connection-mark="cm-ether1" new-routing-mark="to-ether1" 
add action=mark-routing chain=output connection-mark="cm-ether2" new-routing-mark="to-ether2" 
add action=mark-routing chain=output connection-mark="cm-ether3" new-routing-mark="to-ether3" 
add action=mark-routing chain=output connection-mark="cm-ether4" new-routing-mark="to-ether4" 
add action=mark-routing chain=output connection-mark="cm-ether5" new-routing-mark="to-ether5" 
add action=mark-routing chain=output connection-mark="cm-ether6" new-routing-mark="to-ether6"</code>
Credit https://www.o-om.com/2020/11/mikrotik-load-balancing-ecmp-script.html
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

