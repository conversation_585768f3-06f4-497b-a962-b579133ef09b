<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>LB ECMP Failover Script Failover - Mikrotik Script RouterOS</title>
<meta content='LB ECMP Failover Script Failover - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>  
</head>
<body>
<div id="hidelink"></div>

<h1>LB ECMP Failover Script Failover - Mikrotik Script RouterOS</h1><pre>
How to do automatic ECMP failover
This script demonstrates one method of doing automatic failover using the Netwatch function and using scripting to enable or disable gateways. This is probably not the most efficient way, but it works. I would welcome any input on how it can be improved.

The situation:

You have 2 lines going out to the internet - ********* and *********. You have setup a mangle to mark HTTP traffic (optional) and want to route http along the 2 lines using load balancing.

You setup the mangle:

<code class="routeros">/ip firewall mangle add chain=prerouting protocol=tcp dst-port=80 action=mark-routing \
new-routing-mark=ecmp-http-route passthrough=yes comment=" Route HTTP \
traffic to ECMP" disabled=no</code>

You set up ECMP (Equal Cost Multipath Routing) by using something like

<code class="routeros">/ip route add dst-address=0.0.0.0/0 gateway=*********,********* routing-mark=ecmp-http-route comment="ECMP route for HTTP"</code>

Now you have ECMP for HTTP only. This is nice because MSN messenger, banking websites and other programs and problem sites will not be broken in the same way it might be if you used ECMP for all protocols.

What I then do is for example mark SMTP traffic and route this out through *********:

<code class="routeros">/ip firewall mangle add chain=prerouting protocol=tcp dst-port=25 action=mark-routing \
new-routing-mark=smtp-out passthrough=yes comment="SMTP Traffic" disabled=no 
/ip route add dst-address=0.0.0.0/0 gateway=********* routing-mark=smtp-out comment="SMTP Traffic out"</code>

and route all other traffic through *********

<code class="routeros">/ip route add dst-address=0.0.0.0/0 gateway=********* comment="Default Route to Internet"</code>

Then I need to setup 2 routes to specific addresses to force the router through specific gateways to "test" the links. These should not be popular addresses with your users! Otherwise when a gateway goes down they will have no access to those sites. The addresses I am using as an example are ******** to test *********, and ******** to test *********.

Next I use the Netwatch Function to switch all traffic to the working gateway should any of the gateways fail:

<code class="routeros">/tool netwatch 
add host=******** timeout=2s interval=30s up-script="/ip route set \
\[find comment=\"Default Route To Internet\"\] gateway=*********" \
down-script="/ip route set \[find comment=\"Default Route To Internet\"\] \
gateway=********* comment="" disabled=no 
add host=******** timeout=2s interval=30s up-script="/ip route set \
\[find comment=\"SMTP Traffic out\"\] gateway=********" down-script="/ip \
" \route set \[find comment=\"SMTP Traffic out\"\] gateway=*********
comment="" disabled=no </code>

The problem is that the ECMP http route will still be active, therefore http traffic wont work, so I have 2 scripts to check if both gateways are up or down and take action accordingly:

<code class="routeros">/system script 
add name="ecmp-startup" source=":if ([/ping ******** count=1]=1 && \
[/ping ******** count=1]=1 && [/ip route get [find \
comment=\"ECMP Route For HTTP\"] disabled]=true) do={ :log info \"Both gateways up\" \
/ip route set [find routing-mark=ecmp-http-route] \
disabled=no}" policy=ftp,reboot,read,write,policy,test,winbox,password 
add name="ecmp-shutdown" source=":if ([/ping ******** count=1]=1 && \
[/ping ******** count=1]=0) do={ :log info \"Gateway down\"\
/ip route set [find routing-mark=ecmp-http-route] \
disabled=yes}" policy=ftp,reboot,read,write,policy,test,winbox,password
Hi I found this error while trying to use this script, what worked for me was
ecmp start/shut script. Looks like  in the start and shut script (") are missing
from the find, well other the script works wonders for me. Thanks a lot savagedavid
ecmp starthp script
:if ([/ping ******** count=1]=1 && [/ping ******** count=1]=1 && [/ip route get \
[find routing-mark="ecmp-http-route"] disabled]=true) do={:log info "Both Gateways are up" \
/n/ip route set [find routing-mark="ecmp-http-route"] disable=no}
ecmp shutdown script
:if ([/ping ******** count=1]=0 || [/ping ******** count=1]=0) do={:log info \
"Gateway down" /ip route set [find routing-mark="ecmp-http-route"] disabled=yes} </code>

Notice that it first checks to see if the route is enable before trying to re-enable it. Otherwise it will reset the route and users will be dropped momentarily.

Then finally schedule the scripts to check every 30 seconds:

<code class="routeros">/system scheduler 
add name="gateway-check" on-event="/system script run ecmp-shutdown
script run ecmp-startup" start-date=jan/01/1970 start-time=00:00:00 \
interval=30s comment="" disabled=no</code>
</pre>
<br>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
