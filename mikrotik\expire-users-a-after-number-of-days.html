<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Expire Users Hotspot A After Number Of Days - MikroTik Script RouterOS</title>
<meta content='Expire Users Hotspot A After Number Of Days - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Expire Users Hotspot A After Number Of Days - MikroTik Script RouterOS</h1>
<pre>Two scripts here, one to set a comment on users at first login to the hotspot. The comment contains the date of the fist login. The second script should be run daily to disable user accounts once the account has been active for $offset days.

This allows you to give someone a login which will expire say a week after their first login as opposed to an uptime of a week.

Apply this to a hotspot user profile:

<code class="routeros">{
 :local date [ /system clock get date ]
 :if ( [ /ip hotspot user get $user comment ] = "" ) do={
  [ /ip hotspot user set $user comment=$date ]
 }
}</code>
Run this once per day:

<code class="routeros">{
 :local offset 7
 :global today
 
 {
  :local date [ /system clock get date ]
  :local montharray ( "jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec" )
  :local monthdays ( 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 )
  :local days [ :pick $date 4 6 ]
  :local monthtxt [ :pick $date 0 3 ]
  :local year [ :pick $date 7 11 ]
  :local months ([ :find $montharray $monthtxt]  )
  :for nodays from=0 to=$months do={
   :set days ( $days + [ :pick $monthdays $nodays ] )
  }
  :set days ($days + $year * 365) 
  :set today $days
 }
 
 :foreach i in [ /ip hotspot user find where disabled=no ] do={
  :if ([ :find [ /ip hotspot user get $i comment ] ] = 0 ) do={
   :local date [ /ip hotspot user get $i comment ]
   :local montharray ( "jan","feb","mar","apr","may","jun","jul","aug","sep","oct","nov","dec" )
   :local monthdays ( 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 )
   :local days [ :pick $date 4 6 ]
   :local monthtxt [ :pick $date 0 3 ]
   :local year [ :pick $date 7 11 ]
   :local months ( [ :find $montharray $monthtxt ] )
   :for nodays from=0 to=$months do={
    :set days ( $days + [ :pick $monthdays $nodays ] )
   }
   :set days ($days + $year * 365)
   :if ( ($days + $offset) < $today ) do={ 
    :local name [/ip hotspot user get $i name]
    :log info "HOTSPOT EXPIRE SCRIPT: Disabling Hotspot user $name first logged in $date"
    [ /ip hotspot user disable $i ]
    [ /ip hotspot active remove [find where user=$user] ]
   }
  }
 }
}</code>
Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
