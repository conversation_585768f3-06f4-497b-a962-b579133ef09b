<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Find The Day Of The Week - MikroTik Script RouterOS</title>
<meta content='Find The Day Of The Week - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Find The Day Of The Week - MikroTik Script RouterOS</h1>
<pre>This script will find the weekday (sun,mon,tue, etc.) for a given date.

Tested on 3.25, 3.26, and 3.27

--Doug 23:35, 27 July 2009 (EEST) Update: remove leading zeros from day and year (thanks to vash in forums)

<code class="routeros"># Calculates day of the week for a givien date
# Month: jan,feb ... nov,dec   (must be lower-case)
# Day: 1 - 31
# Year: 1900 - 2999
# mmm/dd/yyyy   same format as [/system clock get date]
# (ex. jul/22/2009)

:local date [/system clock get date]

# Math Calculation here
:local result ""
:local months [:toarray "jan,feb,mar,apr,may,jun,jul,aug,sep,oct,nov,dec"]
:local monthtbl [:toarray "0,3,3,6,1,4,6,2,5,0,3,5"]
:local daytbl [:toarray "sun,mon,tue,wed,thu,fri,sat"]

:local month [:pick $date 0 3]
:local day [:pick $date 4 6]
:local dayc [:pick $date 5 6]
:local century [:pick $date 7 9]
:local year [:pick $date 9 11]
:local yearc [:pick $date 10 11]

# if the first char is a 0 (zero) only read last char, else script fails
:if ([:pick $date 4 5] = 0) do={ :set day ($dayc)}
:if ([:pick $date 9 10] = 0) do=[:set year ($yearc)]

:local sum 0
:set sum ($sum + (2 * (3 - ($century - (($century / 4) * 4)))))
:set sum ($sum + ($year / 4))
:set sum ($sum + $year + $day)
:for mindex from=0 to=[:len $months] do={
  :if ([:pick $months $mindex] = $month) do={:set sum ($sum + [:pick $monthtbl $mindex]) }
}
:set sum ($sum - (($sum / 7) * 7))
:set result [:pick $daytbl $sum]

# END Math Calculation

:put ([:pick $date 0 3] . "/" . [:pick $date 4 6] . "/" . [:pick $date 7 9] . [:pick $date 9 11] . " is on a " . $result)</code>
Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
