<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Using Fetch and Scripting to add IP Address Lists - MikroTik Script RouterOS</title>
<meta content='Using Fetch and Scripting to add IP Address Lists - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Using Fetch and Scripting to add IP Address Lists - MikroTik Script RouterOS</h1>
<pre>This only works with files under 4096 characters in size due to the variable size limit in v3 hopefully they will re-introduce the LUA system in v4 shortly so we can make this work with any size list.

If not I will be investigating an alternate solution for breaking files up into readable chunks.

Regards, Andrew Cox

The Code

<code class="routeros">## Generic IP address list input
## Based on a script written by Sam Norris, ChangeIP.com 2008
## Edited by Andrew Cox, AccessPlus.com.au 2008
:if ( [/file get [/file find name=ipaddress.txt] size] > 0 ) do={
# Remove exisiting addresses from the current Address list
/ip firewall address-list remove [/ip firewall address-list find list=MY-IP-LIST]

:global content [/file get [/file find name=ipaddress.txt] contents] ;
:global contentLen [ :len $content ] ;

:global lineEnd 0;
:global line "";
:global lastEnd 0;

:do {
	 :set lineEnd [:find $content "" $lastEnd ] ;
	 :set line [:pick $content $lastEnd $lineEnd] ;
	 :set lastEnd ( $lineEnd + 1 ) ;
	 #If the line doesn't start with a hash then process and add to the list
	 :if ( [:pick $line 0 1] != "#" ) do={

	:local entry [:pick $line 0 $lineEnd ]
	:if ( [:len $entry ] > 0 ) do={
	   /ip firewall address-list add list=MY-IP-LIST address=$entry
	}
  }
} while ($lineEnd < $contentLen)
}</code>
How to use
This will grab IP entries for a simple list in the format

<code class="routeros">#This is a comment
#Blah blah blah
*******
*******/24
*******
*********/26</code>
Just substitute the address-list and filename you want to pull from (for anyone else who wants to use it with their own generated lists)

You can use this hand-in-hand with a fetch script to retrive the list from a remote site then process it.

Code for fetching

<code class="routeros">/tool fetch address=server.somewhere.tld host=server.somewhere.tld mode=http src-path=folder/anotherfolder/ipaddress.txt
:delay 10
#Replace with whatever name you have called the processing script
/system script run add-ip-addresses</code>
Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>