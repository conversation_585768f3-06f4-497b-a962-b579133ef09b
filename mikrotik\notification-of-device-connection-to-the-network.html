<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Notification Of Device Connection To The Network - MikroTik Script RouterOS</title>
<meta content='Notification Of Device Connection To The Network - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Notification Of Device Connection To The Network - MikroTik Script RouterOS</h1>
<pre>We set up a notification about the connection of a new device to a local network or WiFi using the DHCP server settings (allocation of an IP address or lease expiration). We send a message to Telegram or email.

But this is not the only application of this possibility. 🙂

If you set the IP address lease time (Lease Time) equal to 10 hours, you can find out what time the employees’ computers were connected to the network or who does not turn off the computer when leaving the workplace.

If you use a simple WiFi password, you may find that the neighbor’s child is also using your WiFi. 🙂

The script will help you find out about the existing problems with the stability of the connection or about network problems.

You can also configure other actions when activating this script.

Mikrotik script is triggered when an IP address is allocated to a new device or a device whose IP address has expired. Sends a Telegram message or email.

<code class="routeros">:if ($leaseBound =1) do={
    # Variables
    :local Time [/system clock get time];
    :local Date [/system clock get date];
    :local Comment [/ip dhcp-server lease get value-name=comment number=[/ip dhcp-server lease find address=$leaseActIP]]
    :local DeviceName [/system identity get name];

    # START Send Telegram Module
    :local MessageText "\F0\9F\9F\A2 <b>$DeviceName: New DHCP client</b> %0D%0A <b>Name:</b> $"lease-hostname" %0D%0A <b>Comment:</b> [$Comment] %0D%0A <b>Interface:</b> $leaseServerName %0D%0A <b>IP:</b> $leaseActIP %0D%0A <b>MAC:</b> $leaseActMAC";
    :local SendTelegramMessage [:parse [/system script  get MyTGBotSendMessage source]]; 
    $SendTelegramMessage MessageText=$MessageText;
    #END Send Telegram Module

    # START Send Email Module
    :local SendTo "<EMAIL>";
    :local Subject "\F0\9F\9F\A2 INFO: $DeviceName [$Date $Time] New DHCP client";
    :local MessageText "Name: $"lease-hostname", Comment: $Comment, Interface: $leaseServerName IP: $leaseActIP MAC: $leaseActMAC";
    :local FileName "";
    :local SendEmail [:parse [/system script get SendEmailFunction source]];
    $SendEmail SendTo=$SendTo TextMail=$MessageText Subject=$Subject FileName=$FileName;
    # END Send Email Module
}</code>
Credit: <a href="https://mhelp.pro/mikrotik-scripts-notification-of-device-connection-to-the-network/">https://mhelp.pro/mikrotik-scripts-notification-of-device-connection-to-the-network/</a>
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

