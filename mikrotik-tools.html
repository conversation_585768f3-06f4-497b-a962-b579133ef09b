<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MikroTik Tools - All in One</title>
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #07553B 0%, #CED46A 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .nav-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 20px;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .nav-tab {
            padding: 12px 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            flex: 1;
            min-width: 150px;
        }
        
        .nav-tab:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .nav-tab.active {
            background: #07553B;
            box-shadow: 0 4px 15px rgba(7, 85, 59, 0.4);
        }
        
        .content-area {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            min-height: 500px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .tool-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .tool-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #07553B;
            transition: transform 0.2s ease;
        }
        
        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .tool-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }
        
        .tool-card p {
            color: #666;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .tool-link {
            display: inline-block;
            padding: 8px 16px;
            background: #07553B;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.2s ease;
        }

        .tool-link:hover {
            background: #0a6b49;
        }
        
        .calculator-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.2s ease;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #07553B;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.2s ease;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .result-area {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            border-left: 4px solid #4CAF50;
        }
        
        textarea {
            width: 100%;
            height: 300px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            resize: vertical;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-tabs {
                flex-direction: column;
            }
            
            .nav-tab {
                min-width: auto;
            }
            
            .content-area {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 MikroTik Tools</h1>
            <p>Koleksi lengkap tools untuk MikroTik RouterOS - All in One</p>
        </div>
        
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('home')">🏠 Home</button>
            <button class="nav-tab" onclick="showTab('burst-calculator')">📊 Burst Calculator</button>
            <button class="nav-tab" onclick="showTab('pcc-generator')">⚖️ PCC Generator</button>
            <button class="nav-tab" onclick="showTab('queue-generator')">📋 Queue Generator</button>
            <button class="nav-tab" onclick="showTab('vpn-generator')">🔒 VPN Generator</button>
            <button class="nav-tab" onclick="showTab('script-database')">📚 Script Database</button>
        </div>
        
        <div class="content-area">
            <!-- Home Tab -->
            <div id="home" class="tab-content active">
                <h2>Selamat Datang di MikroTik Tools</h2>
                <p>Platform lengkap untuk semua kebutuhan MikroTik RouterOS Anda. Pilih tool yang Anda butuhkan dari menu navigasi di atas.</p>
                
                <div class="tool-grid">
                    <div class="tool-card">
                        <h3>📊 Burst Limit Calculator</h3>
                        <p>Hitung konfigurasi burst limit yang optimal untuk bandwidth management</p>
                        <a href="#" class="tool-link" onclick="showTab('burst-calculator')">Buka Tool</a>
                    </div>
                    
                    <div class="tool-card">
                        <h3>⚖️ Load Balancing PCC</h3>
                        <p>Generator script untuk load balancing menggunakan metode PCC</p>
                        <a href="#" class="tool-link" onclick="showTab('pcc-generator')">Buka Tool</a>
                    </div>
                    
                    <div class="tool-card">
                        <h3>📋 Queue Generator</h3>
                        <p>Buat script queue tree dan simple queue dengan mudah</p>
                        <a href="#" class="tool-link" onclick="showTab('queue-generator')">Buka Tool</a>
                    </div>
                    
                    <div class="tool-card">
                        <h3>🔒 VPN Generator</h3>
                        <p>Generator script untuk routing VPN dan tunnel configuration</p>
                        <a href="#" class="tool-link" onclick="showTab('vpn-generator')">Buka Tool</a>
                    </div>
                    
                    <div class="tool-card">
                        <h3>📚 Script Database</h3>
                        <p>Koleksi lengkap script MikroTik RouterOS untuk berbagai keperluan</p>
                        <a href="#" class="tool-link" onclick="showTab('script-database')">Buka Database</a>
                    </div>
                </div>
            </div>

            <!-- Burst Calculator Tab -->
            <div id="burst-calculator" class="tab-content">
                <h2>📊 MikroTik Burst Limit Calculator</h2>
                <p>Hitung konfigurasi burst limit yang optimal untuk bandwidth management Anda.</p>

                <div class="calculator-form">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h3>Upload Settings</h3>
                            <div class="form-group">
                                <label>Max Limit (K/M)</label>
                                <input type="text" id="up-max-limit" value="512K" placeholder="512K">
                            </div>
                            <div class="form-group">
                                <label>Burst Limit (K/M)</label>
                                <input type="text" id="up-burst-limit" value="1M" placeholder="1M">
                            </div>
                            <div class="form-group">
                                <label>Burst Duration (seconds)</label>
                                <input type="text" id="up-burst-duration" value="6" placeholder="6">
                            </div>
                        </div>

                        <div>
                            <h3>Download Settings</h3>
                            <div class="form-group">
                                <label>Max Limit (K/M)</label>
                                <input type="text" id="down-max-limit" value="1M" placeholder="1M">
                            </div>
                            <div class="form-group">
                                <label>Burst Limit (K/M)</label>
                                <input type="text" id="down-burst-limit" value="2M" placeholder="2M">
                            </div>
                            <div class="form-group">
                                <label>Burst Duration (seconds)</label>
                                <input type="text" id="down-burst-duration" value="6" placeholder="6">
                            </div>
                        </div>
                    </div>

                    <button class="btn" onclick="calculateBurst()">Hitung Burst Settings</button>

                    <div id="burst-result" class="result-area" style="display: none;">
                        <h3>Hasil Perhitungan:</h3>
                        <div id="burst-output"></div>
                    </div>
                </div>
            </div>

            <!-- PCC Generator Tab -->
            <div id="pcc-generator" class="tab-content">
                <h2>⚖️ Load Balancing PCC Generator</h2>
                <p>Generate script untuk load balancing menggunakan metode Per Connection Classifier (PCC).</p>

                <div class="calculator-form">
                    <div class="form-group">
                        <label>Jumlah ISP/Gateway</label>
                        <select id="pcc-gateway-count" onchange="updateGatewayInputs()">
                            <option value="2">2 Gateway</option>
                            <option value="3">3 Gateway</option>
                            <option value="4">4 Gateway</option>
                            <option value="5">5 Gateway</option>
                        </select>
                    </div>

                    <div id="pcc-gateways">
                        <div class="form-group">
                            <label>Gateway 1 IP</label>
                            <input type="text" id="gw1" value="***********" placeholder="***********">
                        </div>
                        <div class="form-group">
                            <label>Gateway 2 IP</label>
                            <input type="text" id="gw2" value="***********" placeholder="***********">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Interface LAN</label>
                        <input type="text" id="lan-interface" value="bridge-local" placeholder="bridge-local">
                    </div>

                    <button class="btn" onclick="generatePCC()">Generate PCC Script</button>

                    <div id="pcc-result" class="result-area" style="display: none;">
                        <h3>Script PCC Load Balancing:</h3>
                        <textarea id="pcc-output"></textarea>
                        <button class="btn" onclick="copyToClipboard('pcc-output')" style="margin-top: 10px;">Copy Script</button>
                    </div>
                </div>
            </div>

            <!-- Queue Generator Tab -->
            <div id="queue-generator" class="tab-content">
                <h2>📋 Queue Generator</h2>
                <p>Generate script untuk Simple Queue dan Queue Tree dengan bandwidth sharing.</p>

                <div class="calculator-form">
                    <div class="form-group">
                        <label>Tipe Queue</label>
                        <select id="queue-type">
                            <option value="simple">Simple Queue</option>
                            <option value="tree">Queue Tree</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Network Address</label>
                        <input type="text" id="network-addr" value="***********/24" placeholder="***********/24">
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="form-group">
                            <label>Upload Limit</label>
                            <input type="text" id="upload-limit" value="1M" placeholder="1M">
                        </div>
                        <div class="form-group">
                            <label>Download Limit</label>
                            <input type="text" id="download-limit" value="2M" placeholder="2M">
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Bandwidth Sharing</label>
                        <select id="bandwidth-sharing">
                            <option value="shared">Shared (UpTo)</option>
                            <option value="dedicated">Dedicated</option>
                        </select>
                    </div>

                    <button class="btn" onclick="generateQueue()">Generate Queue Script</button>

                    <div id="queue-result" class="result-area" style="display: none;">
                        <h3>Script Queue:</h3>
                        <textarea id="queue-output"></textarea>
                        <button class="btn" onclick="copyToClipboard('queue-output')" style="margin-top: 10px;">Copy Script</button>
                    </div>
                </div>
            </div>

            <!-- VPN Generator Tab -->
            <div id="vpn-generator" class="tab-content">
                <h2>🔒 VPN Generator</h2>
                <p>Generate script untuk VPN routing dan tunnel configuration.</p>

                <div class="calculator-form">
                    <div class="form-group">
                        <label>Tipe VPN</label>
                        <select id="vpn-type">
                            <option value="game-routing">Game Routing</option>
                            <option value="all-traffic">All Traffic Tunnel</option>
                            <option value="split-tunnel">Split Tunnel</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>VPN Gateway IP</label>
                        <input type="text" id="vpn-gateway" value="********" placeholder="********">
                    </div>

                    <div class="form-group">
                        <label>VPN Interface</label>
                        <input type="text" id="vpn-interface" value="pptp-out1" placeholder="pptp-out1">
                    </div>

                    <div id="game-ports" style="display: block;">
                        <div class="form-group">
                            <label>Game Ports (pisahkan dengan koma)</label>
                            <input type="text" id="game-port-list" value="80,443,8080" placeholder="80,443,8080">
                        </div>
                    </div>

                    <button class="btn" onclick="generateVPN()">Generate VPN Script</button>

                    <div id="vpn-result" class="result-area" style="display: none;">
                        <h3>Script VPN:</h3>
                        <textarea id="vpn-output"></textarea>
                        <button class="btn" onclick="copyToClipboard('vpn-output')" style="margin-top: 10px;">Copy Script</button>
                    </div>
                </div>
            </div>

            <!-- Script Database Tab -->
            <div id="script-database" class="tab-content">
                <h2>📚 MikroTik Script Database</h2>
                <p>Koleksi lengkap script MikroTik RouterOS untuk berbagai keperluan.</p>

                <div class="tool-grid">
                    <div class="tool-card">
                        <h3>🔒 Security Scripts</h3>
                        <p>Script untuk keamanan router dan network protection</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/Protect-Router-from-Port-Scanner.html" class="tool-link" target="_blank">Port Scanner Protection</a>
                            <a href="mikrotik/Basic-universal-firewall-script.html" class="tool-link" target="_blank">Universal Firewall</a>
                            <a href="mikrotik/Default-Filter-Rules.html" class="tool-link" target="_blank">Default Filter Rules</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>📊 Monitoring Scripts</h3>
                        <p>Script untuk monitoring bandwidth, traffic, dan system health</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/Complete-Bandwidth-Monitoring.html" class="tool-link" target="_blank">Bandwidth Monitor</a>
                            <a href="mikrotik/Monitoring-Script.html" class="tool-link" target="_blank">System Monitor</a>
                            <a href="mikrotik/Check-bandwidth-and-add-limitations.html" class="tool-link" target="_blank">Bandwidth Check</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>🌐 Load Balancing</h3>
                        <p>Script untuk load balancing ECMP, PCC, dan failover</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/LB-PCC-2-Line-ISP.html" class="tool-link" target="_blank">PCC 2 Line</a>
                            <a href="mikrotik/LB-ECMP-2-Line-ISP.html" class="tool-link" target="_blank">ECMP 2 Line</a>
                            <a href="mikrotik/Failover-with-Netwatch.html" class="tool-link" target="_blank">Failover Netwatch</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>🔥 Hotspot Scripts</h3>
                        <p>Script untuk manajemen hotspot dan user management</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/Create-Hotspot-Multi-Login-With-Bandwidth-Limit.html" class="tool-link" target="_blank">Multi Login</a>
                            <a href="mikrotik/Custom-Limit-Bandwidth-Hotspot-With-Queue-Simple.html" class="tool-link" target="_blank">Custom Bandwidth</a>
                            <a href="mikrotik/bypass-user-hotspot-with-macaddress.html" class="tool-link" target="_blank">MAC Bypass</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>📡 Wireless Scripts</h3>
                        <p>Script untuk konfigurasi wireless dan alignment</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/SXT-5HnD-Alignment-Script.html" class="tool-link" target="_blank">SXT Alignment</a>
                            <a href="mikrotik/LED-Antenna-Alignment.html" class="tool-link" target="_blank">LED Alignment</a>
                            <a href="mikrotik/Force-Disconnect-Wireless-Stations-with-Low-CCQ.html" class="tool-link" target="_blank">Low CCQ Disconnect</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>🛠️ System Scripts</h3>
                        <p>Script untuk backup, update, dan system maintenance</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/Auto-Update-RouterOS-Package-From-Script.html" class="tool-link" target="_blank">Auto Update</a>
                            <a href="mikrotik/How-To-Backup.html" class="tool-link" target="_blank">Backup Script</a>
                            <a href="mikrotik/Reboot-mikrotik-from-terminal.html" class="tool-link" target="_blank">Auto Reboot</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>🎮 Gaming Scripts</h3>
                        <p>Script untuk optimasi gaming dan port forwarding</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/port-game-online-pc-games.html" class="tool-link" target="_blank">PC Games Port</a>
                            <a href="mikrotik/port-game-online-mobile-games.html" class="tool-link" target="_blank">Mobile Games</a>
                            <a href="mikrotik/Ping-ICMP-To-Routing-ISP-Games.html" class="tool-link" target="_blank">Game Routing</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>🚫 Blocking Scripts</h3>
                        <p>Script untuk blocking website, ads, dan content filtering</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/block-ads-use-dns-static.html" class="tool-link" target="_blank">Block Ads</a>
                            <a href="mikrotik/Block-Tiktok.html" class="tool-link" target="_blank">Block TikTok</a>
                            <a href="mikrotik/block-youtube-layer7-content-tls.html" class="tool-link" target="_blank">Block YouTube</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>📧 Notification Scripts</h3>
                        <p>Script untuk notifikasi email dan telegram</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/notification-when-public-ip-address-router-changes-to-email-or-telegram.html" class="tool-link" target="_blank">IP Change Alert</a>
                            <a href="mikrotik/Send-Email-About-Reboot.html" class="tool-link" target="_blank">Reboot Alert</a>
                            <a href="mikrotik/sending-power-on-notification-to-telegram.html" class="tool-link" target="_blank">Power On Alert</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>💾 Backup Scripts</h3>
                        <p>Script untuk backup otomatis dan restore</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/auto-backup-filename-backup-and-send-to-gmail.html" class="tool-link" target="_blank">Auto Backup Gmail</a>
                            <a href="mikrotik/Generate-backup-and-sendit-by-email.html" class="tool-link" target="_blank">Backup Email</a>
                            <a href="mikrotik/How-to-Backup-Use-Export-rsc.html" class="tool-link" target="_blank">Export RSC</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>🌍 DNS Scripts</h3>
                        <p>Script untuk DNS management dan dynamic DNS</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <a href="mikrotik/dynamic-dns-update-script-for-dyndns.html" class="tool-link" target="_blank">DynDNS Update</a>
                            <a href="mikrotik/Google-Safe-Search-use-DNS-Static.html" class="tool-link" target="_blank">Safe Search DNS</a>
                            <a href="mikrotik/make-sure-all-clients-use-router-dns.html" class="tool-link" target="_blank">Force Router DNS</a>
                        </div>
                    </div>

                    <div class="tool-card">
                        <h3>📋 All Scripts</h3>
                        <p>Lihat semua script yang tersedia dalam database</p>
                        <a href="mikrotik/index.html" class="tool-link" target="_blank">Lihat Semua Script</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Navigation functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all nav tabs
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');
        }

        // Burst Calculator
        function calculateBurst() {
            const upMaxLimit = document.getElementById('up-max-limit').value;
            const upBurstLimit = document.getElementById('up-burst-limit').value;
            const upBurstDuration = document.getElementById('up-burst-duration').value;
            const downMaxLimit = document.getElementById('down-max-limit').value;
            const downBurstLimit = document.getElementById('down-burst-limit').value;
            const downBurstDuration = document.getElementById('down-burst-duration').value;

            // Convert to numbers for calculation
            const upMax = parseValue(upMaxLimit);
            const upBurst = parseValue(upBurstLimit);
            const downMax = parseValue(downMaxLimit);
            const downBurst = parseValue(downBurstLimit);

            // Calculate burst threshold (75% of max limit)
            const upThreshold = Math.ceil(upMax * 0.75);
            const downThreshold = Math.ceil(downMax * 0.75);

            // Calculate burst time
            const upBurstTime = Math.ceil(parseInt(upBurstDuration) * upBurst / (upMax * 0.75));
            const downBurstTime = Math.ceil(parseInt(downBurstDuration) * downBurst / (downMax * 0.75));

            // Calculate limit-at (12.5% of max limit)
            const upLimitAt = Math.ceil(upMax * 0.125);
            const downLimitAt = Math.ceil(downMax * 0.125);

            const result = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>Upload Configuration:</h4>
                        <p><strong>Max Limit:</strong> ${upMaxLimit}</p>
                        <p><strong>Burst Limit:</strong> ${upBurstLimit}</p>
                        <p><strong>Burst Threshold:</strong> ${upThreshold}K</p>
                        <p><strong>Burst Time:</strong> ${upBurstTime}s</p>
                        <p><strong>Limit At:</strong> ${upLimitAt}K</p>
                    </div>
                    <div>
                        <h4>Download Configuration:</h4>
                        <p><strong>Max Limit:</strong> ${downMaxLimit}</p>
                        <p><strong>Burst Limit:</strong> ${downBurstLimit}</p>
                        <p><strong>Burst Threshold:</strong> ${downThreshold}K</p>
                        <p><strong>Burst Time:</strong> ${downBurstTime}s</p>
                        <p><strong>Limit At:</strong> ${downLimitAt}K</p>
                    </div>
                </div>
                <div style="margin-top: 20px; padding: 15px; background: #f0f8ff; border-radius: 5px;">
                    <h4>Rate Limit String:</h4>
                    <code>${upMaxLimit}/${downMaxLimit} ${upBurstLimit}/${downBurstLimit} ${upThreshold}K/${downThreshold}K ${upBurstTime}/${downBurstTime} 8 ${upLimitAt}K/${downLimitAt}K</code>
                </div>
            `;

            document.getElementById('burst-output').innerHTML = result;
            document.getElementById('burst-result').style.display = 'block';
        }

        function parseValue(value) {
            const num = parseInt(value.replace(/[^0-9]/g, ''));
            const unit = value.match(/[a-zA-Z]/);
            if (unit && unit[0].toLowerCase() === 'm') {
                return num * 1000;
            }
            return num;
        }

        // Update gateway inputs based on count
        function updateGatewayInputs() {
            const count = parseInt(document.getElementById('pcc-gateway-count').value);
            const container = document.getElementById('pcc-gateways');
            container.innerHTML = '';

            for (let i = 1; i <= count; i++) {
                const div = document.createElement('div');
                div.className = 'form-group';
                div.innerHTML = `
                    <label>Gateway ${i} IP</label>
                    <input type="text" id="gw${i}" value="192.168.${i}.1" placeholder="192.168.${i}.1">
                `;
                container.appendChild(div);
            }
        }

        // PCC Generator
        function generatePCC() {
            const gatewayCount = parseInt(document.getElementById('pcc-gateway-count').value);
            const lanInterface = document.getElementById('lan-interface').value;

            let gateways = [];
            for (let i = 1; i <= gatewayCount; i++) {
                const gwElement = document.getElementById(`gw${i}`);
                if (gwElement) {
                    gateways.push(gwElement.value);
                }
            }

            let script = `# Load Balancing PCC Script for ${gatewayCount} Gateways\n\n`;

            // Mangle rules
            script += "# Mangle Rules\n";
            for (let i = 0; i < gatewayCount; i++) {
                script += `/ip firewall mangle add chain=prerouting in-interface=${lanInterface} connection-state=new nth=${gatewayCount},${i+1} action=mark-connection new-connection-mark=conn${i+1}\n`;
                script += `/ip firewall mangle add chain=prerouting connection-mark=conn${i+1} action=mark-routing new-routing-mark=route${i+1}\n`;
            }

            script += "\n# Routes\n";
            for (let i = 0; i < gatewayCount; i++) {
                script += `/ip route add dst-address=0.0.0.0/0 gateway=${gateways[i]} routing-mark=route${i+1} check-gateway=ping\n`;
            }

            script += "\n# Default Route\n";
            script += `/ip route add dst-address=0.0.0.0/0 gateway=${gateways[0]} distance=1 check-gateway=ping\n`;

            document.getElementById('pcc-output').value = script;
            document.getElementById('pcc-result').style.display = 'block';
        }

        // Queue Generator
        function generateQueue() {
            const queueType = document.getElementById('queue-type').value;
            const networkAddr = document.getElementById('network-addr').value;
            const uploadLimit = document.getElementById('upload-limit').value;
            const downloadLimit = document.getElementById('download-limit').value;
            const bandwidthSharing = document.getElementById('bandwidth-sharing').value;

            let script = `# ${queueType === 'simple' ? 'Simple Queue' : 'Queue Tree'} Script\n\n`;

            if (queueType === 'simple') {
                const network = networkAddr.split('/')[0];
                const subnet = networkAddr.split('/')[1];
                const baseIP = network.split('.').slice(0, 3).join('.');

                script += "# Simple Queue Rules\n";
                for (let i = 2; i <= 10; i++) { // Limit to 10 IPs for demo
                    const ip = `${baseIP}.${i}`;
                    const queueName = `queue-${ip.replace(/\./g, '-')}`;

                    if (bandwidthSharing === 'shared') {
                        script += `/queue simple add name="${queueName}" target=${ip}/32 max-limit=${uploadLimit}/${downloadLimit}\n`;
                    } else {
                        script += `/queue simple add name="${queueName}" target=${ip}/32 limit-at=${uploadLimit}/${downloadLimit} max-limit=${uploadLimit}/${downloadLimit}\n`;
                    }
                }
            } else {
                script += "# Queue Tree Rules\n";
                script += `/queue tree add name="Download" parent=global limit-at=0 max-limit=${downloadLimit}\n`;
                script += `/queue tree add name="Upload" parent=global limit-at=0 max-limit=${uploadLimit}\n`;

                const network = networkAddr.split('/')[0];
                const baseIP = network.split('.').slice(0, 3).join('.');

                for (let i = 2; i <= 10; i++) { // Limit to 10 IPs for demo
                    const ip = `${baseIP}.${i}`;
                    const queueName = `queue-${ip.replace(/\./g, '-')}`;

                    if (bandwidthSharing === 'shared') {
                        script += `/queue tree add name="${queueName}-down" parent="Download" packet-mark=down-${ip.replace(/\./g, '-')} limit-at=0 max-limit=${downloadLimit}\n`;
                        script += `/queue tree add name="${queueName}-up" parent="Upload" packet-mark=up-${ip.replace(/\./g, '-')} limit-at=0 max-limit=${uploadLimit}\n`;
                    } else {
                        script += `/queue tree add name="${queueName}-down" parent="Download" packet-mark=down-${ip.replace(/\./g, '-')} limit-at=${downloadLimit} max-limit=${downloadLimit}\n`;
                        script += `/queue tree add name="${queueName}-up" parent="Upload" packet-mark=up-${ip.replace(/\./g, '-')} limit-at=${uploadLimit} max-limit=${uploadLimit}\n`;
                    }
                }
            }

            document.getElementById('queue-output').value = script;
            document.getElementById('queue-result').style.display = 'block';
        }

        // VPN Generator
        function generateVPN() {
            const vpnType = document.getElementById('vpn-type').value;
            const vpnGateway = document.getElementById('vpn-gateway').value;
            const vpnInterface = document.getElementById('vpn-interface').value;
            const gamePorts = document.getElementById('game-port-list').value;

            let script = `# VPN ${vpnType} Script\n\n`;

            if (vpnType === 'game-routing') {
                script += "# Game Routing VPN Script\n";
                script += `# Mangle rules for game ports\n`;

                const ports = gamePorts.split(',').map(p => p.trim());
                ports.forEach(port => {
                    script += `/ip firewall mangle add chain=prerouting protocol=tcp dst-port=${port} action=mark-connection new-connection-mark=game-conn\n`;
                    script += `/ip firewall mangle add chain=prerouting protocol=udp dst-port=${port} action=mark-connection new-connection-mark=game-conn\n`;
                });

                script += `/ip firewall mangle add chain=prerouting connection-mark=game-conn action=mark-routing new-routing-mark=game-route\n`;
                script += `\n# Route for game traffic\n`;
                script += `/ip route add dst-address=0.0.0.0/0 gateway=${vpnGateway} routing-mark=game-route\n`;

            } else if (vpnType === 'all-traffic') {
                script += "# All Traffic VPN Tunnel\n";
                script += `/ip route add dst-address=0.0.0.0/0 gateway=${vpnGateway} distance=1\n`;
                script += `# NAT rule for VPN interface\n`;
                script += `/ip firewall nat add chain=srcnat out-interface=${vpnInterface} action=masquerade\n`;

            } else if (vpnType === 'split-tunnel') {
                script += "# Split Tunnel VPN Script\n";
                script += `# Route specific networks through VPN\n`;
                script += `/ip route add dst-address=*******/32 gateway=${vpnGateway}\n`;
                script += `/ip route add dst-address=*******/32 gateway=${vpnGateway}\n`;
                script += `# Add more specific routes as needed\n`;
            }

            document.getElementById('vpn-output').value = script;
            document.getElementById('vpn-result').style.display = 'block';
        }

        // Copy to clipboard function
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999); // For mobile devices

            try {
                document.execCommand('copy');
                alert('Script berhasil disalin ke clipboard!');
            } catch (err) {
                alert('Gagal menyalin script. Silakan copy manual.');
            }
        }

        // Initialize gateway inputs on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateGatewayInputs();
        });
    </script>
</body>
</html>
