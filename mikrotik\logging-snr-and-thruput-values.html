<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Logging SNR and Thruput Values - MikroTik Script RouterOS</title>
<meta content='Logging SNR and Thruput Values - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Logging SNR and Thruput Values - MikroTik Script RouterOS</h1>
<pre>Upgraded Version of the script below.

Works through to Version 5 and probably Version 6

This script logs the radio name, system identity, system uptime, board name, firmware version, clients connected, ccq, nstreme, frequency, ethernet information, network latencies, arp entries, neighbours etc. The clients script logs the details of radios connected to an AP.

It is extremely useful for debugging wireless problems, like intermittent noise, signal and ccq. Use the following script on a schedule. Configure the Mikrotik AP to log data to a syslog server (remote) and you have a powerful way of monitoring your equipment. This newer version does not care what your radio's are named.

New Version that works with v5/v6, first line should be the next device closest to your main router. It reports ping stats, you can use the results to create latency charts between network devices, useful for detecting network problems.

<code class="routeros">/system script add name=CombStats policy=ftp,reboot,read,write,policy,test,winbox,password,sniff,sensitive,api source="
   :local Dependent XXX.XXX.XXX.XXX;\r\
   :local i;\r\
   :local MM [:tonum [:pick [/sys clo get time] 3 5]];\r\
   #:log info (\"Starting Script\");\r\
   :local MF \"2\";\r\
   :local myvar1;\r\
   :local myvar2;\r\
   :local myvar3;\r\
   :local myvar4;\r\
   :local myvar5;\r\
   :local myvar6;\r\
   ############## Wireless Clients every Two Minutes ###############################################\r\
   \r\
   :if ((\$MM/\$MF)*\$MF = \$MM) do={\r\
   /int wire reg\r\
   :foreach i in=[find] do={\r\
   \t:local mc [get \$i mac-address];\r\
   \t:local ip [get \$i last-ip];\r\
   \t:local rx [get \$i rx-rate];\r\
   \t:local tx [get \$i tx-rate];\r\
   \t:local up [get \$i uptime];\r\
   \t:local la [get \$i last-activity];\r\
   \t:local ss [get \$i signal-strength];\r\
   \t:local sn [get \$i signal-to-noise];\r\
   \t:local tq [get \$i tx-ccq];\r\
                   :local by [get \$i bytes];\r\
                   :local pk [get \$i packets];\r\
   \t:local rq [get \$i rx-ccq];\r\
                   :local rv [get \$i routeros-version];\r\
                   :local pt [get \$i p-throughput];\r\
   \t:local ts [get \$i tx-signal-strength];\r\
   \r\
   \t:log info ([/system identity get name]. \",CLIENT,\".\$mc.\",\".\$ip.\",\".\$rx.\",\".\$tx.\",\".\$up.\",\".\$la.\",\
   \".\$ss.\",\".\$sn.\",\".\$tq.\",\".\$by.\",\".\$pk.\",\".\$rq.\",\".\$rv.\",\".\$pt . \",\" . \$ts);}\r\
   }\r\
   ############# Neighbours ##################\r\
   :set MF \"30\"; \t:if ((\$MM/\$MF)*\$MF = \$MM) do={\r\
   #### GET LIST OF NEIGHBOURS\r\
   /ip neigh\r\
   :foreach i in=[find] do={\r\
   \t:local nint  [get \$i interface];\r\
                   :local naddress [get \$i address];\r\
                   :local nmac [get \$i mac-address];\r\
   \t:local nidentity  [get \$i identity];\r\
                   :local nplatform [get \$i platform];\r\
                   :local nversion [get \$i version];\r\
                   :local nboard [get \$i board];\r\
                   :local nuptime [get \$i uptime];\r\
   \r\
   \r\
                   :log info ([/system identity get name].\",NEIGH,\".\$nint.\",\".\$naddress.\",\".\$nmac.\",\".\$nidenti\
   ty.\",\".\$nplatform.\",\".\$nversion.\",\".\$nboard.\",\".\$nuptime);\r\
       \t}\r\
   }\r\
   ############# BRIDGE ENTRIES ###########\r\
   :set MF \"20\"; \t:if ((\$MM/\$MF)*\$MF = \$MM) do={\r\
   \r\
   ##### Get Bridge Entries\r\
   /int bridge\r\
   :foreach i in=[find] do={\r\
   \t:local name  [get \$i name];\r\
                   :local nmac [get \$i mac-address];\r\
                   :log info ([/system identity get name].\",BRIDGE,\".\$name.\",\".\$nmac);\r\
       \t}\r\
   }\r\
   ############# WIRELESS CARDS ###########\r\
   :set MF \"20\"; \t:if ((\$MM/\$MF)*\$MF = \$MM) do={\r\
   #Get info on wireless adapters\r\
   /int wire\r\
   :foreach i in=[find] do={\r\
   \t:local mac  [get \$i mac-address];\r\
                   :local intname [get \$i name];\r\
                   :local inttype [get \$i interface-type];\r\
                   :log info ([/system identity get name].\",APMAC,\".\$mac.\",\".\$intname.\",\".\$inttype);\r\
       \t}\r\
   }\r\
   \r\
   ##############  Half-Hourly Voltage and ARP/IP Match #################################################\r\
   # keep this it is handy\r\
   \r\
   :set MF \"30\"; \t:if ((\$MM/\$MF)*\$MF = \$MM) do={\r\
   :local Volts [/sys he get voltage];\r\
   :if (\$Volts > 20 ) do={:log info ([/system identity get name].\",VOLT,\".\$Volts);}\r\
   /ip arp\r\
   :foreach i in=[find] do={\r\
   \t:log info ([/system identity get name].\",ARPW,\".[get \$i mac-address].\",\".[get \$i address])}\r\
   }\r\
   ############# Ping Statistics every 5 minutes  ##################################################\r\
   # Latency Test\r\
   \r\
   :set MF \"5\"; \t:if ((\$MM/\$MF)*\$MF = \$MM) do={\r\
   #:local Dependent;\r\
   #:foreach i in [/tool traffic-monitor find] do={:set Dependent [/tool traffic-monitor get \$i name];}\r\
   :local avgRtt;\r\
   :local DCavgRtt;\r\
   :local FJavgRtt;\r\
   :local avgTRtt;\r\
   :local maxTRtt;\r\
   :local maxRtt;\r\
   :local DCmaxRtt;\r\
   :local FJmaxRtt;\r\
   :local rxpkt;\r\
   :local txpkt;\r\
   :set maxTRtt \"0\";\r\
   :for i from=1 to=4 step=1 do={\r\
       /tool flood-ping \$Dependent count=10 size=1500 do={\r\
   \t:if (\$sent = 10) do={\r\
   \t\t :set rxpkt (rxpkt + \$\"received\");\r\
   \t\t :set txpkt (txpkt + \$\"sent\");\r\
   \t\t :set avgRtt (\$\"avg-rtt\");\r\
   \t\t :set maxRtt (\$\"max-rtt\");                    \r\
    \t\t}\r\
   \t}\r\
       :if (\$maxRtt>\$maxTRtt) do={:set  maxTRtt (\$maxRtt);}\r\
       :set  avgTRtt (\$avgRtt+\$avgTRtt);\r\
       :delay 4;\r\
      }\r\
   \r\
   :log info ( [/system identity get name].\",PING,\". \$Dependent . \",\" . ((\$rxpkt*100)/\$txpkt) . \",\" . \$avgTRtt/4\
   \_. \",\" . \$maxTRtt);\r\
   }\r\
   #############  Ethernet Status Minutely ##################################################\r\
   \r\
   /int ether\r\
   :foreach i in=[find] do={\r\
   \t:local name [get \$i name];\r\
                   :local mtu [get \$i mtu];\r\
   \t:local mac  [get \$i mac-address];\r\
                   :local auto  [get \$i auto-negotiation];\r\
   \t:local fdx  [get \$i full-duplex];\r\
   \t:local sped [get \$i speed];\r\
   \r\
   \t/int eth monitor [/int wire get \$i name] once do={\r\
   \t\t :local myvar1 \$\"auto-negotiation\";\r\
   \t\t :local myvar2 \$\"full-duplex\";\r\
   \t\t :local myvar3  \"\";\r\
   \t\t :local myvar4 \$\"rate\";\r\
   \t\t :local myvar5 \$\"status\";\r\
   \t\t :local myvar6  \"\";\r\
   \t\t :log info ([/system identity get name].\",ETHER,\".\$name.\",\".\$mac.\",\".\$auto.\",\".\$fdx.\",\".\$sped.\",\".\
   \$myvar1.\",\".\$myvar2.\",\".\$myvar4.\",\".\$myvar5);\r\
   \t\t}\r\
   \t}\r\
   \r\
   ##############  Wireless Status by the Minute ##############################################################\r\
   \r\
   # Loop through interfaces\r\
   :foreach i in=[/interface wireless find ] do={/interface wireless monitor \$i once do={\r\
   \r\
   # Clear Variables\r\
   :set myvar1 \"\";\r\
   :set myvar2 \"\";\r\
   :set myvar3 \"\";\r\
   :set myvar4 \"\";\r\
   :set myvar5 \"\";\r\
   :set myvar6 \"\";\r\
   \r\
   /int monitor-traffic interface=[/int wire get \$i name] once do={\r\
      :set myvar3 \$\"rx-bits-per-second\"\r\
      :set myvar4 \$\"tx-bits-per-second\"\r\
      :set myvar5 \$\"rx-packets-per-second\"\r\
      :set myvar6 \$\"tx-packets-per-second\"\r\
   }\r\
   \r\
   # If radio is in bridge mode then get uptime of link from registration\r\
   # Could change this so that if connected number of hosts is 2 or 1 then report\r\
   :if ([/int wire get \$i mode] = \"bridge\" ) do={\r\
   \r\
   # get some more data through this variable\r\
   \r\
   :foreach a in=[ /int wir reg find interface=[/int wire get \$i name]] do={\r\
   \r\
   /int wire reg\r\
         :set myvar1 [get \$a uptime];\r\
         :set myvar2 [get \$a last-activity];\r\
         }  \r\
   }\r\
   \r\
   :log info ( [/system identity get name] . \",WLAN,\" . [/int wire get \$i name] . \",\" . [/system resour get uptime] .\
   \_\",\" . [/system reso get board-name] .\",\" . [/system reso get version] . \",\" .\$\"registered-clients\" .  \",\".  \
   \$\"overall-tx-ccq\" . \".\" . \$\"nstreme\" . \",\" . \$\"rx-ccq\" . \",\" . \$\"noise-floor\" . \",\"  . \$\"frequency\
   \" . \",\" . \$\"band\" . \",\" . [/int wire get \$i ssid] .  \",,\"  .  \",\" . [/int wire get \$i hw-retries] .  \",\" \
   . [/int wire get \$i wds-mode]  . \",\" . \$\"tx-rate\" . \",\" . \$\"rx-rate\" . \",\" . \$\"signal-strength\" . \",\" .\
   \_\$\"tx-signal-strength\" . \",\" . [/system reso get cpu-load]   . \",\" . [/system reso get free-memory] . \"/\" . [/s\
   ys reso get total-memory]  . \",\" . [/system reso get free-hdd-space] . \"/\" . [/sys reso get total-hdd-space]   . \",\
   \" . [/sys reso get bad-blocks]   . \",\" . [/int wire get \$i adaptive-noise-immunity] . \",\" . \$myvar1 . \",\" . \$my\
   var2 . \",\" . \$myvar3  . \",\" . \$myvar4 . \",\" . \$myvar5 . \",\" . \$myvar6)\r\
   \r\
   } } \r\
   \r\
   \r\
   }\r\
   \r\
   :log info (\"Ending Script\");\r\
   \r\
   \r\
     "</code>
Tested and works with version 3.12 and up.

<code class="routeros">/system script
add name=stats policy=ftp,reboot,read,write,policy,test,winbox,sniff source="#AP STATS\r\
   \r\
   :foreach i in=[/interface wireless find ] do={/interface wireless monitor \$i once do { :log info ( \"int=\" . [/int wire get \$i name] . \",\" . [/system identity g\
   et name] . \",\" . [/system resour get uptime] . \",\" . [/system reso get board-name] .\",\" . [/system reso get version] . \",\" .\$\"registered-clients\" .  \",\". \
   \_\$\"overall-tx-ccq\" . \".\" . \$\"nstreme\" . \",\" . \$\"rx-ccq\" . \",\" . \$\"noise-floor\" . \",\"  . \$\"frequency\" . \",\" . \$\"band\" . \",\" . [/int wire \
   get \$i ssid] .  \",\" . [/int wire get \$i ack-timeout] .  \",\" . [/int wire get \$i hw-retries] .  \",\" . [/int wire get \$i wds-mode]  . \",\" . \$\"tx-rate\" . \
   \",\" . \$\"rx-rate\" . \",\" . \$\"signal-strength\" . \",\" . \$\"tx-signal-strength\")}}\r\
   \r\
   #CLIENTS CONNECTED\r\
   /int wire reg\r\
   :foreach i in=[ /int wir reg find] do={:log info ([/system identity get name] . \",\" . [get \$i  mac-address ]. \",\" . [get \$i last-ip] . \",\" . [get \$i rx-rate\
   ] . \",\" . [get \$i tx-rate] . \",\" . [get \$i uptime] . \",\" . [get \$i last-activity] . \",\" . [get \$i signal-strength] . \",\" . [get \$i signal-to-noise] . \"\
   ,\" . [get \$i tx-ccq] . \",\" . [get \$i bytes] . \",\" . [get \$i packets])}\r\
   \r\
   "</code>
2.x firmware version

<code class="routeros">:local interface "wlan1";
/interface wireless monitor $interface once do={
:local signal $"signal-strength";
:local snr $"signal-to-noise";
:local noise $"noise-floor";
:local thruput $"p-throughput";
:local freq $"frequency";
:log info "SNR: $snr Signal $signal Noise: $noise , thruput: $thruput , freq: $freq MHz";
};</code>
Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

