<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Bypass Full Speed Local Transfer File - MikroTik Script RouterOS</title>
<meta content='Bypass Full Speed Local Transfer File - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Bypass Full Speed Local Transfer File - MikroTik Script RouterOS</h1>
<pre>The case uses the RB750GR3 proxy
Port 1 for LAN 1, 1Gbps
Port 2 for LAN 2, 1Gbps
Gbps IP Local: ************ (possibly ftp or web server)

When copying files from LAN1 to LAN2, why is the maximum bandwidth only 150 mega bits / second or 15MB / s. Even though the proxy CPU resource is only 36% used, how do you get full speed and maximize the bandwidth?

The method is very easy: in a simple queue or queue tree, create a queue, for example FULL-SPEED, then use SFQ (Stochastic Fairness Queuing) in queue type.

<code class="routeros">/queue type 
add name=Full-Speed kind=sfq
/queue simple
add name="FULL-SPEED" queue=Full-Speed/Full-Speed target=************/24 total-queue=default</code>
This method will be even more optimal if the Fasttrack function is activated.

<code class="routeros">/ip firewall filter 
add chain=forward action=fasttrack-connection connection-state=established,related
add chain=forward action=accept connection-state=established,related</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>


