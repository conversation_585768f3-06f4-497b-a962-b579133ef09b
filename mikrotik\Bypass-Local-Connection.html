  
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Bypass Local Connection / Bypass Local IP - Mikrotik Script RouterOS</title>
<meta content='CBypass Local Connection / Bypass Local IP  - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script> 
</head>
<body>
<div id="hidelink"></div>
<h1>Bypass Local Connection / Bypass Local IP  - Mikrotik Script RouterOS</h1>
<pre>Simple How Bypass Local Connection / Bypass Local IP on RouterOS

<code class="routeros"># paste script addlist to terminal
/ip firewall address-list
add address=0.0.0.0/8 list=LOCAL-IP
add address=10.0.0.0/8 list=LOCAL-IP
add address=**********/10 list=LOCAL-IP
add address=*********/8 list=LOCAL-IP
add address=***********/16 list=LOCAL-IP
add address=**********/12 list=LOCAL-IP
add address=*********/24 list=LOCAL-IP
add address=*********/24 list=LOCAL-IP
add address=***********/16 list=LOCAL-IP
add address=**********/15 list=LOCAL-IP
add address=************/24 list=LOCAL-IP
add address=***********/24 list=LOCAL-IP
add address=*********/4 list=LOCAL-IP
add address=240.0.0.0/4 list=LOCAL-IP

/ip fi ma
add action=accept chain=prerouting dst-address-list=LOCAL-IP  src-address-list=LOCAL-IP
add action=accept chain=postrouting dst-address-list=LOCAL-IP  src-address-list=LOCAL-IP
add action=accept chain=forward dst-address-list=LOCAL-IP  src-address-list=LOCAL-IP
add action=accept chain=input dst-address-list=LOCAL-IP  src-address-list=LOCAL-IP
add action=accept chain=output dst-address-list=LOCAL-IP  src-address-list=LOCAL-IP</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

