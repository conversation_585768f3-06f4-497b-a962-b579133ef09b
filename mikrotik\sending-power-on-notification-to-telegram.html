<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Sending Power On Notification To Telegram - MikroTik Script RouterOS</title>
<meta content='Sending Power On Notification To Telegram - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Sending Power On Notification To Telegram - MikroTik Script RouterOS</h1>
<pre>MikroTik script sends a power on notification the device to Telegram. Allows you to learn about unexpected device reboots or equipment power problems, complementing the monitoring system.
When sending a message to Telegram, it will also write the event to the system log.

🔔 For the script to work, you must have a Telegram bot Token and a chat ID or a personal ID. Creating a Telegram bot and getting the required IDs is not the topic of this article.

<code class="routeros"># Func: Telegram send message
:local TGSendMessage do={
    :local tgUrl "https://api.telegram.org/bot$Token/sendMessage?chat_id=$ChatID&text=$Text&parse_mode=html";
    /tool fetch http-method=get url=$tgUrl keep-result=no;
}

# Constants
:global RebootStatus;
:local TelegramBotToken "*********************************************";
:local TelegramChatID "987654321";
:local DeviceName [/system identity get name];

:local TelegramMessageText "\F0\9F\9F\A2 <b>$DeviceName:</b> start after Power Off.";

# Program
:if ($RebootStatus != true)  do={
    $TGSendMessage Token=$TelegramBotToken ChatID=$TelegramChatID Text=$TelegramMessageText;
    :set RebootStatus true;
    :log info "Script CheckPowerOn send Telegram message."
}</code>
Credit: <a href="https://mhelp.pro/mikrotik-scripts-sending-power-on-notification-to-telegram/">https://mhelp.pro/mikrotik-scripts-sending-power-on-notification-to-telegram/</a>
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

