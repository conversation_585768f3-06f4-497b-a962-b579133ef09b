<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Alignment Script that "reads back" RSSI with Beeps - MikroTik Script RouterOS</title>
<meta content='Alignment Script that "reads back" RSSI with Beeps - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Alignment Script that "reads back" RSSI with Beeps - MikroTik Script RouterOS</h1>
<pre>How To Alignment Script that "reads back" RSSI with Beeps..

<code class="routeros">#
# Mikrotik RB411/433 "RSSI Readback" alignment script
# 
# Based off script by Mark Shumate Feb 2009
# Modified by Randy Cosby June 2009
#
# NOTE: This script may work on other platforms, I
# don't have anything but RB411/433 here in front of
# me. The Beeps will probably work on practically any
# wireless CPE type Routerboard, but maybe the script
# would have to be edited? 
#

# default delaytime
:local lnsdelaytime 230ms;

# name of wireless interface to monitor (default wlan1)
:local lnsintname "wlan1";
:local lnsbeepfreq 800;

# base freq (as in pitch) of tens digit beep 
:local tensbeepfreq 1200;
#  base freq (as in pitch) of ones digit beep 
:local onesbeepfreq 1100;



# The (veery approximate, heh) running time of the script
# is set here. I am too lazy right now to do this a
# better way...besides, who cares if the thing Beeps 
#  a couple extra minutes right?!? :)
:local lnsrunningtime 10m;

# Here, we set how long the script will beep. NOTE that
# startup/shutdown tones will still be played.
:local lnsbeeptime 10m;

# figure out beep cutoff time
:local lnsrunbeepdiff;
:set lnsrunbeepdiff ($lnsrunningtime - $lnsbeeptime);


# play starting tones
:delay 150ms;
:beep frequency=($lnsbeepfreq - 300) length=150ms;
:delay 150ms;
:beep frequency=($lnsbeepfreq - 200) length=150ms;
:delay 550ms;

# main monitoring cycle
:while ($lnsrunningtime > 0s) do={
 /interface wireless monitor "$lnsintname" once do={
     :put "Monitoring....";
:if ($"status" = "connected-to-ess") do={
#:if ($"status" = "searching-for-network") do={

     :local rssi ($"signal-strength");
#     :local rssi -60;
     :put $"signal-strength";
     :local tensBeeps (tonum( $rssi / 10 ) * -1 );
     :put $tensBeeps;
     :local onesBeeps (($rssi*-1) - ($tensBeeps * 10))
     :put $onesBeeps;
     :for i from=1 to=($tensBeeps) do={
     :beep frequency=(-($rssi*$rssi)/10+$tensbeepfreq) length=($lnsdelaytime / 2);
     :delay $lnsdelaytime;
     :set lnsrunningtime ($lnsrunningtime - $lnsdelaytime);

     }
     :delay 400ms;
     :set lnsrunningtime ($lnsrunningtime - 400ms);
     :if ($onesBeeps = 0) do={
             :beep frequency=(-($rssi * $rssi)/10 + $onesbeepfreq) length=($lnsdelaytime*2);
             :delay $lnsdelaytime;
             :set lnsrunningtime ($lnsrunningtime - ($lnsdelaytime));
            } else={
     :for i from=1 to=($onesBeeps) do={
     :beep frequency=(-($rssi * $rssi)/10 + $onesbeepfreq) length=($lnsdelaytime / 2);
     :delay $lnsdelaytime;    
     :set lnsrunningtime ($lnsrunningtime - $lnsdelaytime);

      }
     }
     :delay 1s;
            } else={
    :if ($"status" = "searching-for-network") do={
      
       :delay 200ms;
      :if ($lnsrunningtime > $lnsrunbeepdiff) do={
        :beep frequency=$lnsbeepfreq length=150ms;
      }
       :delay 200ms;
       :if ($lnsrunningtime > $lnsrunbeepdiff) do={
         :beep frequency=($lnsbeepfreq + 100) length=125ms;
       }
       :delay 150ms;
       :if ($lnsrunningtime > $lnsrunbeepdiff) do={
         :beep frequency=($lnsbeepfreq + 200) length=100ms;
       }
       :delay 300ms;
       :set lnsrunningtime ($lnsrunningtime - 750ms);
       } else={
       
       :if ($lnsrunningtime > $lnsrunbeepdiff) do={
         :beep frequency=($lnsbeepfreq - 150) length=150ms;
       }
       :delay 150ms;
       :if ($lnsrunningtime > $lnsrunbeepdiff) do={
         :beep frequency=($lnsbeepfreq - 350) length=100ms;
       }
       :delay 1550ms;
       
     }
   }
 }
#:put $lnsrunningtime;
#:set lnsrunningtime (totime($lnsrunningtime));
#:set lnsrunningtime ("$lnsrunningtime" - "$lnsdelaytime");
:put $lnsrunningtime;
}
# shut off LEDs, play shutdown tones
:beep frequency=($lnsbeepfreq - 200) length=150ms;
:delay 150ms;
:beep frequency=($lnsbeepfreq - 300) length=150ms;
:delay 150ms;</code>
Credit: wiki.mikrotik.Com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

