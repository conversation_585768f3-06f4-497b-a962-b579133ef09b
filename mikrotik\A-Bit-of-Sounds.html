<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>A Bit of Sounds - MikroTik RouterOS Script</title>
<meta content='A Bit of Sounds - MikroTik RouterOS Script Database' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/routeros.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>A Bit of Sounds - Mikrotik Script RouterOS</h1>
<pre>Part One: SQUAWK
The scripts below are based on SQUAWK program (C) 1987 Merlin R. Null.

<code class="routeros"># SQUAWK Chang
 :for j from=1 to=4 step=1 do={
   :for i from=2000 to=50 step=-400 do={
     :beep frequency=$i length=11ms;
     :delay 11ms;
   }
   :for i from=800 to=2000 step=400 do={
     :beep frequency=$i length=11ms;
     :delay 11ms;
   }
 }</code>
  <code class="routeros"># SQUAWK Coo
 :for i from=0 to=150 step=10 do={
   :beep frequency=(1295 - i) length=22ms;
   :delay 22ms;
   :beep frequency=(1095 + i) length=22ms;
   :delay 22ms;
 }</code>
  <code class="routeros"># SQUAWK Oh!
 :for i from=800 to=2000 step=100 do={
   :beep frequency=$i length=11ms;
   :delay 11ms;
 }
 :for i from=2000 to=50 step=-100 do={
   :beep frequency=$i length=11ms;
   :delay 11ms;
 }</code>
  <code class="routeros"># SQUAWK Phone 1
 :for i from=1 to=10 step=1 do={
   :beep frequency=1195 length=22ms;
   :delay 22ms;
   :beep frequency=2571 length=22ms;
   :delay 22ms;
 }</code>
 <code class="routeros"># SQUAWK Siren
 :for i from=1 to=3 step=1 do={
   :beep frequency=550 length=494ms;
   :delay 494ms;
   :beep frequency=400 length=494ms;
   :delay 494ms;
 }</code>
  <code class="routeros"># SQUAWK Space 1
 :for i from=1000 to=40 step=-20 do={
   :beep frequency=$i length=11ms;
   :delay 11ms;
 }</code>
 <code class="routeros"># SQUAWK Space 2
 :for i from=10000 to=500 step=-500 do={
   :beep frequency=$i length=11ms;
   :delay 11ms;
 }</code>
  <code class="routeros"># Phone Call

 :for i from=1 to=10 do={
   /system script run "SQUAWK Phone 1";
   :delay 400ms;
   /system script run "SQUAWK Phone 1";
   :delay 2000ms;
 }</code>
Part Two: Adams
The scripts below are based on the code from Lee Adams' book "Grafik, Animation, Simulation fur Personalcomputer" (ISBN 3890905722, Markt&Technik, 1989).

<code class="routeros"># Adams Fanfare
 :for t from=1200 to=350 step=-50 do={
   :beep frequency=$t length=33ms;
   :delay 33ms;
 }</code>
  <code class="routeros"># Adams Larm
 :for t1 from=1 to=10 step=1 do={
   :for t2 from=300 to=1800 step=40 do={
     :beep frequency=$t2 length=11ms;
     :delay 11ms;
   }
 }</code>
  <code class="routeros"># Adams Maschinengewehr
 :for t from=1 to=20 step=1 do={
   :beep frequency=40 length=33ms;
   :delay 33ms;
   :delay 55ms;
 }</code>
 <code class="routeros"># Adams Telefone
 :for t1 from=1 to=4 step=1 do={
   :for t2 from=1 to=25 step=1 do={
     :beep frequency=540 length=33ms;
     :delay 33ms;
     :beep frequency=650 length=27ms;
     :delay 27ms;
   }
   :delay 2000ms;
 }</code>
 <code class="routeros"># Adams Tonarkade
 :for t1 from=1 to=8 step=1 do={
     :for t2 from=600 to=750 step=8 do={
     :beep frequency=$t2 length=11ms;
     :delay 11ms;
   }
 }</code>
 <code class="routeros"># Adams Tonarkade 2
 :for t from=1250 to=600 step=-8 do={
   :beep frequency=$t length=11ms;
   :delay 11ms;
 }</code>
Part Three: Miklavcic
The scripts below are based on the code from http://snipplr.com/view/9220/simple-tic-tac-toe/

<code class="routeros"># Miklavcic Sad
 :beep frequency=784 length=500ms;
 :delay 500ms;
 
 :beep frequency=738 length=500ms;
 :delay 500ms;
 
 :beep frequency=684 length=500ms;
 :delay 500ms;
 
 :beep frequency=644 length=1000ms;
 :delay 1000ms;</code>
<code class="routeros"># Miklavcic Victory
 :beep frequency=523 length=200ms;
 :delay 1000ms;
 
 :beep frequency=523 length=200ms;
 :delay 1000ms;
 
 :beep frequency=523 length=200ms;
 :delay 1000ms;
 
 :beep frequency=659 length=700ms;
 :delay 700ms;
 
 :beep frequency=784 length=500ms;
 :delay 500ms;
 
 :beep frequency=523 length=200ms;
 :delay 1000ms;
 
 :beep frequency=523 length=200ms;
 :delay 1000ms;
 
 :beep frequency=523 length=200ms;
 :delay 1000ms;
 
 :beep frequency=659 length=700ms;
 :delay 700ms;
 
 :beep frequency=784 length=500ms;
 :delay 800ms;
 
 :beep frequency=784 length=400ms;
 :delay 400ms;
 
 :beep frequency=884 length=200ms;
 :delay 200ms;
 
 :beep frequency=784 length=200ms;
 :delay 200ms;
 
 :beep frequency=687 length=200ms;
 :delay 200ms;
 
 :beep frequency=659 length=200ms;
 :delay 200ms;
 
 :beep frequency=579 length=200ms;
 :delay 200ms;
 
 :beep frequency=519 length=400ms;
 :delay 400ms;</code>
 <code class="routeros"># Part Z: Assorted Sounds
# Sound 1

 :beep frequency=600 length=165ms;
 :delay 165ms;
 
 :beep frequency=50 length=83ms;
 :delay 83ms;
 
 :beep frequency=600 length=165ms;
 :delay 165ms;
 
 :beep frequency=50 length=83ms;
 :delay 83ms;
 
 :beep frequency=650 length=165ms;
 :delay 165ms;
 
 :beep frequency=600 length=165ms;
 :delay 165ms;
 
 :beep frequency=50 length=83ms;
 :delay 83ms;
 
 :beep frequency=600 length=165ms;
 :delay 165ms;
 
 :beep frequency=700 length=275ms;
 :delay 275ms;
 
 :beep frequency=800 length=275ms;
 :delay 275ms;
 
 :beep frequency=900 length=110ms;
 :delay 110ms;</code>
 Credit: https://wiki.mikrotik.com/wiki/A_Bit_of_Sounds
 </pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

