<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Block Sites User Keywords Use Dns Cache - MikroTik Script RouterOS</title>
<meta content='Block Sites User Keywords Use Dns Cache - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Block Sites User Keywords Use Dns Cache - MikroTik Script RouterOS</h1>
<pre>Make sure to only use Mikrotik DNS:

<code class="routeros">/ip firewall nat
add action=redirect chain=dstnat comment=DNS dst-port=53 protocol=tcp to-ports=53
add action=redirect chain=dstnat dst-port=53 protocol=udp to-ports=53</code>
There are 2 blocking options:

1.Block it by dropping it in Filter rules

<code class="routeros">/ip firewall filter
add chain=forward dst-address-list=BLOCK_SITE action=drop</code>

2. Block by redirecting IP and Port

<code class="routeros">/ip firewall nat
add action=dst-nat chain=dstnat comment="BLOCK WEBSITE" dst-address-list=
BLOCK_SITE dst-port=80,81,8181,443 protocol=tcp to-addresses=************ to-ports=80</code>

Notes:
1. Do not immediately install the scripts, learn first, especially for keywords, you should first change the keywords according to your needs.
2. By default the website that I block I point to internet postifi with ip ************ you can direct it to another IP.
3. Please enter it in the proxy schedule or via a free script and it's better if the running time is not too fast, I personally set it only in 10 minutes
4. This script eats up a little of resources so sorry for the plastic RB (the old one), you should set the running timing a little longer.

<code class="routeros">
# Find all entry on dns cache
:foreach iDNS in=[/ip dns cache all find where (name~"poker" || name~"porn" || name~"cheat" || name~"bokep" || name~"ngentot" || name~"qq.com" || name~"qq.net" || name~"qq.org" || name~"roulette" || name~"sbobet" || name~"casino" || name~"xvideos") && (type="A") ] do={
# find and filtering keyword and only find record for type A
# for keyword just add keyword || name~"KEYWORD") before && (type="A")
##########################################################################
:local tmpDNSsite [/ip dns cache get $iDNS name] ;
:local tmpDNSip [/ip dns cache get $iDNS address];
:local nameList "BLOCK_SITE";
# save to local cache by string
##########################################################################
delay delay-time=10ms
# wait for 10ms
##########################################################################
:if ( [/ip firewall address-list find where address=$tmpDNSip] = "") do={ 
# chek for no more duplicate site on cache
##########################################################################
:log warning ("Added site to block on dns: $tmpDNSsite : $tmpDNSip");
# show info on warning log
########################################################################## 
/ip firewall address-list add address=$tmpDNSip list=$nameList comment=$tmpDNSsite;
# add site to add list entry.
##########################################################################
}
}
# End Script 
##########################################################################</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>


