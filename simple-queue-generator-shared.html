<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Mikrotik Simple Queue Script Generator For Bandwidth Shared (UpTo) - mikrotiktool.Github.io</title>
<meta content='Simple Queue Script Generator For Mikrotik - mikrotiktool.Github.io' name='description'/>
<meta content='script generator, mikrotik, router, winbox, termimal, rsc, script, hotspot, wireless, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<meta property="og:title" content="Mikrotik Simple Queue Script Generator For Bandwidth Shared (UpTo) ">
<meta property="og:description" content="Mikrotik Simple Queue Script Generator For Bandwidth Shared (UpTo) ">
<meta property="og:image:alt" content="Mikrotik Simple Queue Script Generator For Bandwidth Shared (UpTo) ">
<meta property="og:type" content="website">
<meta property="og:url" content="https://mikrotiktool.github.io/simple-queue-generator-shared.html"> 
<script data-ad-client="ca-pub-3815059393374366" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M5TL99Q9M2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-M5TL99Q9M2');
</script>
<style>
*{-webkit-transition: all .3s ease-in;-moz-transition: all .3s ease-in;-ms-transition: all .3s ease-in;transition: all .3s ease-in;}
body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table{margin:0; padding:0}
body { font-size:16px; font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; margin:20px;}
html, body {
height: 100%;
width:100%;
margin-top: 0px;
padding:0;
}
* {
box-sizing: border-box;
}
.logo {
margin-top:30px; 
color: #111;
text-decoration:none;
font-size:27px;
font-weight:bold; 
}	
.logo a{
color:#111 !important;
text-decoration:none !important;
}
.Menu{
width:100%;
border-bottom:1px solid #ccc;
height:100%;
padding-bottom: 15px;
margin-bottom: 20px;
font-size:14px;
font-weight:bold;
}
#wrap{
width:1220px;
margin:0 auto;
padding:10px;
background:#fff;
}

.header{
height:auto; 
width:100%;
margin-top:0px;
margin-bottom:10px;
border-top:1px solid #ccc;
}
.main-wrap{
display: flex;
}   
.content{
flex:1;
float:right;
width:800px;
padding: 10px;
border-top:1px solid #bbb;
border-bottom:1px solid #bbb;
border-right:1px solid #bbb;
background:#ddd;
}
.sidebar{
float:left;
width:400px;
Padding-top:8px;
padding-left: 10px;
Padding-right:10px;
Padding-bottom:20px;
background-color:#ddd;
border:1px solid #bbb; 
height:auto;

}
.footer{
font-size:14px;
padding-top:15px;
text-align:center;
clear:both;
width:auto;
}

h1 {
color:#111;
font-size:22px;
margin-bottom:10px;
margin-top:10px;
}
a:link {
color: #a8328b;
}
a:visited {
color: #a8328b;
}
a:hover {
color: #a8328b;
}
a:active {
color: #a8328b;
}
.menu {
clear:both;
width:auto;
padding-bottom:13px;
}
.menu1 {
width:360px;
margin-bottom:1px;
font-weight:bold;
background-color:#4CAF50;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu1 a:link {
text-decoration:none;
color:white;
}
.menu1 a:visited{
text-decoration:none;
color:white;
}
.menu2 {
width:300px;
margin-bottom:1px;
font-weight:bold;
background-color:#bbb;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu2 a:link{
text-decoration:none;
color:white;
}
.menu2 a:visited{
text-decoration:none;
color:white;
}
button {
color: #fff;
background-color: #a8328b;
border-color: #a8328b;
border:none;
padding:8px;
width:178px;
font-size:16px;
font-weight:bold;
}
.row:after {
content: "";
display: table;
clear: both;
}  
input[type=checkbox]{
 margin-right:7px; 
}
input[type=text], select, textarea {
width: 100%;
padding: 5px;
border: 1px solid #bbb;
border-radius: 1px;
resize: vertical;
margin-bottom:12px;
font-size:16px !important;
}
label {
padding: 5px 5px 5px 0;
display: inline-block;
}
input[type=submit] {
background-color: #a8328b;
color: white;
padding: 12px 20px;
border: none;
border-radius: 1px;
cursor: pointer;
float: right;
}
input[type=submit]:hover {
background-color: #45a049;
}
.col-25 {
float: left;
width: 25%;
margin-top: 6px;
}
.col-75 {
float: left;
width: 75%;
margin-top: 6px;
}
/* Clear floats after the columns */
.row:after {
content: "";
display: table;
clear: both;
}
.list-game {
height: 200px;
background:white;
overflow: scroll;
overflow-x: hidden;
width: 100%;
margin-top:2px;
padding: 3px;
border: 1px solid rgba(0,0,0,0.25);
}
.list-game label {
padding: 0;
display: inline-block;
} 
.list-mangle {
height: 564px;
background:#fff;
overflow: scroll;
width: 100%;
padding: 4px;
margin-top:8px;
border: 1px solid #ccc;
}
table, tr, td {
border: none;
}
/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}
/* Modal Content */
.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
}

/* The Close Button */
.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
} 
</style>
</head>
<body onLoad="callmodal()"> 

<div id="wrap">
<div class="logo">	  
<a href="https://mikrotiktool.github.io"><span style="color:#a8328b !important">mikrotiktool</span>.Github.io</a> <img style="float:right; padding-top:10px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=hits&edge_flat=false" alt="Hits"/>
</div>	
<div class="header">
<span style="float:left; display:inline;margin-right:6px; margin-top: 10px">
<a href="https://mikrotiktool.github.io/simple-queue-generator-shared.html"><img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="HOME"></a>
</span>
<h1>SIMPLE QUEUE SCRIPT GENERATOR MIKROTIK + BANDWIDTH SHARED (UPTO) - FOR IP SUBNET /24 ONLY</h1>
</div>
<div class="main-wrap">
    <div class="sidebar">
		<div style="margin-bottom:10px; height:190px">
		 <label>Parent Name Queue</label><div style="clear: both;"></div>
         <input type="text" id="input-parent" value="Global-Connection" placeholder="Global-Connection">
         <label>Target Local IP / Interface</label><div style="clear: both;"></div>
         <input type="text" id="input-ip-parent" value="************/24" placeholder="************/24 or ether1">
		 <div style="clear: both;"></div>
		 <div style="float:left; width:48%;">
		 <label>Upload Max-Limit</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-up-parent" value="5M" placeholder="5M">
		 </div>
		 <div style="float:left; width:48%;margin-left:10px">
         <label>Download Max-Limit</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-down-parent" value="10M" placeholder="10M">
		 </div>
		 </div>
         <div style="clear: both;"></div>
         <div style="margin-top:10px;margin-bottom:10px;border-bottom:1px solid #bbb">Sub Parent Queue / Child Queue</div>
		 <div style="float:left;width:48%;">
		 <label>Client Name Queue</label><div style="clear: both;"></div>
         <input type="text" id="input-client" value="Client-" placeholder="Client-">
		 </div>
		 <div style="float:left;width:48%;margin-left:10px;">
		 <label>Client Identity</label>
         <input type="text" id="input-client-initial" value="1" placeholder="1">
		 </div>
     	  <div style="float:left; width:48%;">
		 <label>Start IP Client</label><div style="clear: both;"></div>
         <input type="text" id="input-start-ip-client" value="*************" placeholder="*************">
		 </div>
		 <div style="float:left; width:48%;margin-left:10px;">
         <label>End IP Client</label><div style="clear: both;"></div>
         <input type="text" id="input-end-ip-client" value="*************" placeholder="*************">
		 </div>

		 <div style="float:left; width:48%;">
		 <label>Upload Max-Limit</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-up-client" value="512K" placeholder="512K">
		 </div>
		 <div style="float:left; width:48%;margin-left:10px;">
         <label>Download Max-Limit</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-down-client" value="1M" placeholder="1M">
		 </div>
		 <div style="margin-bottom:10px; height:220px">
		 <div style="float:left; width:48%;">
		 <label>Upload Limit-At</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-limit-up" value="0" placeholder="0">
		 </div>
		 <div style="float:left; width:48%;margin-left:10px;">
         <label>Download Limit-At</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-limit-down" value="0" placeholder="0">
		 </div>
		 </div>
         
		 		 	 <div style="margin-top:20px;margin-bottom:10px">
	 <input type="checkbox" id="auto-shared"><label for="auto-shared" style="padding-left:0px">Auto Set For Bandwidth Shared (UP-TO)</label><div style="clear: both;"></div>

         
	 </div>
         <button style="margin-top:10px;" type="button" onclick="myFunctionInput()">Generate Script</button> <button style="margin-left:10px" type="button" onclick="location.reload()">Clear All Script</button> 
         <div style="clear: both;"></div>
    </div>
    <div class="content"> 
        <span style="margin-left:2px">Script Generator Result</span>
         <div class="list-mangle">
		 	<div style="width:100%; height:90px; margin-top:10px; margin-left:10px; margin-bottom:5px;" >	
			<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
			<!-- MIKROTIK SCRIPT DB3 -->
			<ins class="adsbygoogle"
			style="display:inline-block;width:728px;height:90px"
			data-ad-client="ca-pub-3815059393374366"
			data-ad-slot="4637807296"></ins>
			<script>
			(adsbygoogle = window.adsbygoogle || []).push({});
			</script> 
			</div>
            <table id="showScript" style="padding:5px; white-space: nowrap;">
               <tr>
                  <td>
                     <span style="color:#a8328b;">
                     ####################################################################<br>
                     # Mikrotik Simple Queue Script Generator + Bandwidth Shared (UpTo)<br>
                     # Date/Time: <span id="tanggalwaktu"></span><br>
                     # By mikrotiktool.Github.io +  <br>
                     ####################################################################<br><br>
                     </span>
					 <div id="list-output"></div>
				</td>
				</tr>			
			</table>
         </div>
         <br>
         <button style="margin-top:-5px" id="copyscript" type="button" onclick="selectElementContents(document.getElementById('showScript'))">Copy Script</button><span style="padding:7px; font-weight:normal;margin-left:10px"><b>Copy-Paste the Script into the Terminal!</b></span>
    </div>
    </div>
   <div class="footer">
© Copyright 2020-2021 <a href="https://mikrotiktool.github.io/">mikrotiktool.github.io</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a> - Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a>  
   </div>
 </div>
 	  
	<script type="text/javascript">
var modal = document.getElementById("myModal");
var span = document.getElementsByClassName("close")[0];
function callmodal() {
  modal.style.display = "block";
}
span.onclick = function() {
  modal.style.display = "none";
}
window.onclick = function(event) {
  if (event.target == modal) {
    modal.style.display = "none";
  }
}
</script> 
<script> 
var _0xa2ca=['512K','276934pObqRD','input-up-client','getSelection','addRange','294222dCHdSo','createTextRange','790498FqalAZ','lastIndexOf','input-start-ip-client','body','</span>\x22\x20limit-at=\x22<span\x20style=\x22color:#a8328b;\x22>','</span>\x22<br>','toLocaleString','input-parent','add\x20max-limit=\x22<span\x20style=\x22color:#a8328b;\x22>','input-ip-parent','</span>\x22\x20target=\x22<span\x20style=\x22color:#a8328b;\x22>','input-down-client','</span>\x22\x20name=\x22<span\x20style=\x22color:#a8328b;\x20\x22>','copy','match','</span>\x22\x20comment=\x22Script\x20Generator\x20<br>','value','selectNodeContents','auto-shared','checked','1534588plzoFd','innerHTML','104335TFHzUS','toUpperCase','input-limit-down','input-client','input-up-parent','disabled','removeAllRanges','1kUFUmM','getElementById','createRange','69098qqJnMb','input-down-parent','1ZOUqYK','ceil','input-limit-up','input-end-ip-client','replace','substring','list-output','add\x20max-limit=\x22<span\x20style=\x22color:#a8328b;\x20\x22>','193468hHeJvx'];var _0x15ab=function(_0x2f2834,_0x3c01e8){_0x2f2834=_0x2f2834-0xf2;var _0xa2caae=_0xa2ca[_0x2f2834];return _0xa2caae;};var _0x2e4fd2=_0x15ab;(function(_0x27d345,_0x514521){var _0x5070f6=_0x15ab;while(!![]){try{var _0x2bdcd3=-parseInt(_0x5070f6(0xfb))+parseInt(_0x5070f6(0xf9))*parseInt(_0x5070f6(0x11d))+-parseInt(_0x5070f6(0xf3))+-parseInt(_0x5070f6(0xf5))+-parseInt(_0x5070f6(0x111))+-parseInt(_0x5070f6(0x11b))*-parseInt(_0x5070f6(0x118))+parseInt(_0x5070f6(0x10f));if(_0x2bdcd3===_0x514521)break;else _0x27d345['push'](_0x27d345['shift']());}catch(_0x2cd787){_0x27d345['push'](_0x27d345['shift']());}}}(_0xa2ca,0x820c1));var dt=new Date();document['getElementById']('tanggalwaktu')[_0x2e4fd2(0x110)]=dt[_0x2e4fd2(0x101)]();function upperCaseF(_0x48b9f1){setTimeout(function(){var _0x1453fe=_0x15ab;_0x48b9f1[_0x1453fe(0x10b)]=_0x48b9f1[_0x1453fe(0x10b)][_0x1453fe(0x112)]();},0x1);}function resultToFixed(_0x1ff286){return Number['parseFloat'](_0x1ff286)['toFixed'](0x2);}document[_0x2e4fd2(0x119)]('auto-shared')['onchange']=function(){var _0x1edf6b=_0x2e4fd2,_0xb3c182=document[_0x1edf6b(0x119)](_0x1edf6b(0x10d));if(_0xb3c182['checked']){var _0x14162d=document[_0x1edf6b(0x119)](_0x1edf6b(0xfd))[_0x1edf6b(0x10b)],_0x15d03d=document[_0x1edf6b(0x119)](_0x1edf6b(0x120))[_0x1edf6b(0x10b)],_0xece0fb=document[_0x1edf6b(0x119)](_0x1edf6b(0x115))['value'],_0x546e0c=document['getElementById'](_0x1edf6b(0x11c))[_0x1edf6b(0x10b)],_0x55964f=_0x14162d,_0x2f08ce=_0x55964f[_0x1edf6b(0xfc)]('.'),_0x34ea=_0x55964f[_0x1edf6b(0x122)](_0x2f08ce+0x1),_0x7a44e1=_0x15d03d,_0x27ea22=_0x7a44e1[_0x1edf6b(0xfc)]('.'),_0x1342fd=_0x7a44e1[_0x1edf6b(0x122)](_0x27ea22+0x1),_0x3f8737=_0x14162d[_0x1edf6b(0x122)](0x0,_0x14162d['lastIndexOf']('.')),_0x163070=_0xece0fb,_0x32cefd=_0x163070[_0x1edf6b(0x109)](/[a-zA-Z]/),_0x4b78ad=_0x163070['replace'](/[^0-9]/g,''),_0x54f8c3=_0x546e0c,_0x547559=_0x54f8c3['match'](/[a-zA-Z]/),_0x5b672e=_0x54f8c3[_0x1edf6b(0x121)](/[^0-9]/g,''),_0x569f1b=0x400*_0x4b78ad,_0x1fc42b=0x400*_0x5b672e,_0x20cdc9=_0x569f1b/(_0x1342fd-_0x34ea),_0x9c3b23=_0x1fc42b/(_0x1342fd-_0x34ea);document[_0x1edf6b(0x119)](_0x1edf6b(0xf6))[_0x1edf6b(0x116)]=!![],document['getElementById'](_0x1edf6b(0x106))['disabled']=!![],document[_0x1edf6b(0x119)](_0x1edf6b(0x11f))[_0x1edf6b(0x116)]=!![],document['getElementById'](_0x1edf6b(0x113))[_0x1edf6b(0x116)]=!![],document['getElementById'](_0x1edf6b(0xf6))['value']=document[_0x1edf6b(0x119)](_0x1edf6b(0x115))[_0x1edf6b(0x10b)],document[_0x1edf6b(0x119)](_0x1edf6b(0x106))[_0x1edf6b(0x10b)]=document[_0x1edf6b(0x119)](_0x1edf6b(0x11c))[_0x1edf6b(0x10b)],document['getElementById']('input-limit-up')[_0x1edf6b(0x10b)]=Math[_0x1edf6b(0x11e)](_0x20cdc9)+''+'K',document[_0x1edf6b(0x119)](_0x1edf6b(0x113))[_0x1edf6b(0x10b)]=Math[_0x1edf6b(0x11e)](_0x9c3b23)+''+'K',document[_0x1edf6b(0x119)]('list-output')[_0x1edf6b(0x110)]='';}else document[_0x1edf6b(0x119)](_0x1edf6b(0xf6))['disabled']=![],document[_0x1edf6b(0x119)](_0x1edf6b(0x106))[_0x1edf6b(0x116)]=![],document[_0x1edf6b(0x119)](_0x1edf6b(0x11f))[_0x1edf6b(0x116)]=![],document[_0x1edf6b(0x119)](_0x1edf6b(0x113))[_0x1edf6b(0x116)]=![],document['getElementById']('input-up-client')[_0x1edf6b(0x10b)]=_0x1edf6b(0xf4),document['getElementById']('input-down-client')[_0x1edf6b(0x10b)]='1M',document['getElementById'](_0x1edf6b(0x11f))[_0x1edf6b(0x10b)]='0',document[_0x1edf6b(0x119)](_0x1edf6b(0x113))[_0x1edf6b(0x10b)]='0',document[_0x1edf6b(0x119)](_0x1edf6b(0x123))[_0x1edf6b(0x110)]='';};function myFunctionInput(){var _0x721bb=_0x2e4fd2,_0x42f867='',_0x30bae7='',_0x4756ae,_0x46752c=document[_0x721bb(0x119)](_0x721bb(0x102))['value'],_0x10f17e=document[_0x721bb(0x119)]('input-up-parent')[_0x721bb(0x10b)],_0x926dfc=document['getElementById'](_0x721bb(0x11c))[_0x721bb(0x10b)],_0x49ade9=document[_0x721bb(0x119)](_0x721bb(0x104))['value'],_0x42f867=_0x721bb(0x103)+_0x10f17e[_0x721bb(0x112)]()+'/'+_0x926dfc[_0x721bb(0x112)]()+_0x721bb(0xff)+_0x10f17e+'/'+_0x926dfc+_0x721bb(0x105)+_0x49ade9+'</span>\x22\x20name=\x22<span\x20style=\x22color:#a8328b;\x22>'+_0x46752c+_0x721bb(0x10a),_0x54281c=document[_0x721bb(0x119)](_0x721bb(0x114))['value'],_0x1468c8=document[_0x721bb(0x119)]('input-client-initial')[_0x721bb(0x10b)],_0x446dce=document['getElementById'](_0x721bb(0xf6))['value'],_0x1b83b1=document[_0x721bb(0x119)](_0x721bb(0x106))[_0x721bb(0x10b)],_0x3fe7ad=document['getElementById'](_0x721bb(0xfd))['value'],_0x115252=document[_0x721bb(0x119)]('input-end-ip-client')[_0x721bb(0x10b)],_0x46e33b=document['getElementById'](_0x721bb(0x11f))[_0x721bb(0x10b)],_0x2d0b95=document[_0x721bb(0x119)]('input-limit-down')[_0x721bb(0x10b)],_0x152a7d=_0x3fe7ad,_0x3b59d3=_0x152a7d['lastIndexOf']('.'),_0x185ee4=_0x152a7d[_0x721bb(0x122)](_0x3b59d3+0x1),_0x55d6ff=_0x115252,_0x2e13a3=_0x55d6ff['lastIndexOf']('.'),_0x1cda77=_0x55d6ff[_0x721bb(0x122)](_0x2e13a3+0x1),_0x543196=_0x3fe7ad['substring'](0x0,_0x3fe7ad['lastIndexOf']('.')),_0x3afaf3=_0x10f17e,_0x4cf85d=_0x3afaf3[_0x721bb(0x109)](/[a-zA-Z]/),_0x231bc5=_0x3afaf3[_0x721bb(0x121)](/[^0-9]/g,''),_0x590584=_0x926dfc,_0x7ddf20=_0x590584[_0x721bb(0x109)](/[a-zA-Z]/),_0x510e39=_0x590584['replace'](/[^0-9]/g,''),_0x4e62d2=0x400*_0x231bc5,_0x3da02e=0x400*_0x510e39,_0x52367d=_0x4e62d2/(_0x1cda77-_0x185ee4),_0x40d846=_0x3da02e/(_0x1cda77-_0x185ee4),_0x468d46=document[_0x721bb(0x119)](_0x721bb(0x10d));_0x468d46[_0x721bb(0x10e)]&&(document[_0x721bb(0x119)](_0x721bb(0xf6))[_0x721bb(0x10b)]=document[_0x721bb(0x119)](_0x721bb(0x115))[_0x721bb(0x10b)],document[_0x721bb(0x119)](_0x721bb(0x106))['value']=document['getElementById'](_0x721bb(0x11c))[_0x721bb(0x10b)],document[_0x721bb(0x119)](_0x721bb(0x11f))[_0x721bb(0x10b)]=Math[_0x721bb(0x11e)](_0x52367d)+''+'K',document['getElementById'](_0x721bb(0x113))['value']=Math[_0x721bb(0x11e)](_0x40d846)+''+'K',document['getElementById'](_0x721bb(0x123))[_0x721bb(0x110)]='');var _0x46e33b=document[_0x721bb(0x119)](_0x721bb(0x11f))[_0x721bb(0x10b)],_0x2d0b95=document[_0x721bb(0x119)](_0x721bb(0x113))[_0x721bb(0x10b)];for(var _0x4756ae=parseInt(_0x185ee4);_0x4756ae<=parseInt(_0x1cda77);_0x4756ae++){_0x30bae7+=_0x721bb(0xf2)+_0x446dce['toUpperCase']()+'/'+_0x1b83b1[_0x721bb(0x112)]()+_0x721bb(0xff)+_0x46e33b+'/'+_0x2d0b95+_0x721bb(0x105)+_0x543196+'.'+_0x4756ae+_0x721bb(0x107)+_0x54281c+_0x1468c8++ +'</span>\x22\x20parent=\x22<span\x20style=\x22color:#a8328b;\x20\x22>'+_0x46752c+_0x721bb(0x100);}document['getElementById']('list-output')[_0x721bb(0x110)]='/queue\x20simple<br>'+_0x42f867+''+_0x30bae7;}function selectElementContents(_0x2c8906){var _0x125b66=_0x2e4fd2,_0x47629b=document[_0x125b66(0xfe)],_0x36d952,_0x602612;if(document[_0x125b66(0x11a)]&&window[_0x125b66(0xf7)]){_0x36d952=document['createRange'](),_0x602612=window[_0x125b66(0xf7)](),_0x602612[_0x125b66(0x117)]();try{_0x36d952[_0x125b66(0x10c)](_0x2c8906),_0x602612['addRange'](_0x36d952);}catch(_0xa86fb3){_0x36d952['selectNode'](_0x2c8906),_0x602612[_0x125b66(0xf8)](_0x36d952);}}else _0x47629b[_0x125b66(0xfa)]&&(_0x36d952=_0x47629b[_0x125b66(0xfa)](),_0x36d952['moveToElementText'](_0x2c8906),_0x36d952['select']());document['execCommand'](_0x125b66(0x108));}
</script>
</body>
</html>