<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Automatic Expired Hotspot Page Without A Proxy - Mikrotik Script RouterOS</title>
<meta content='Automatic Expired Hotspot Page Without A Proxy - Mikrotik Router Script Database' name='description'/>
<meta content='mikrotik script, routeros script, script' name='keywords'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Automatic Expired Hotspot Page Without A Proxy - Mikrotik Script RouterOS</h1>
<pre>Create an automatic expired hotspot page without a proxy
First download <span style="color:red">"expired.html"</span> <a target="_blank" href="https://drive.google.com/file/d/1tAukNei3AXqfo0pXmjI226UpTHJAZLtc/view?usp=sharing" >here</a>
if you have entered <span style="color:red">"expired.html"</span> into the hotspot folder section, then please edit the "login.html" page
search for tag <span style="color:red">&lt;body&gt; </span> and place the script below above it
<code class="routeros">&lt;script type="text/javascript"&gt;
 var error = "$(error)";  
 if (error == "user $(username) has reached uptime limit") {  
 window.location.href = "http://$(hostname)/expired.html";  
 } else if (error == "user $(username) has reached traffic limit") {  
 window.location.href = "http://$(hostname)/expired.html";  
 } else if (error == "no more sessions are allowed for user $(username)") {  
 window.location.href = "http://$(hostname)/expired.html";  
 } else if (error == "session limit reached ($(error-orig))") {  
 window.location.href = "http://$(hostname)/expired.html";  
 } else if (error !== "") {  
 window.location.href = "http://$(hostname)/error.html";  
 } else { document.write("$(error)"); }  
 &lt;/script&gt;</code>
 Notes: this script is not made by me, I just modified it, whoever owns it, because I have been using it for a long time.
 Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
