<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Enable or Disable New Guest User Account Daily Hotspot - MikroTik Script RouterOS</title>
<meta content='Enable or Disable New Guest User Account Daily Hotspot - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Enable or Disable New Guest User Account Daily Hotspot - MikroTik Script RouterOS</h1>
<pre>We have hotspot gateways at different locations. Each uses radius authentication, for employees, on our Active Directory(AD) domain. To prevent abuse of the access by guests, we wanted them to have to login as well. Rather than creating a single or even a handful of guest accounts, I wanted to have a new one for each day. This should provide for better security by using 366 different combinations. Also, depending on the size of your password it would take a considerable amount of time to "guess" the correct password for that day. In addition, if I were to use an AD account, I would have to manually enable/disable the accounts daily, which is definitely something to avoid.

I have 366 hotspot user accounts created. Each is named guestX where X is the day of year. As in guest1 for the guest account for January 1st. Each account has a different password.

- The Disable script is scheduled to run at 23:58 everyday to disable the last 5 days guest accounts. I do 5 days just in case there were some sort of power failure and the script hasn't run for some time.
- The Enable script runs at 00:01 everyday to enable that day's guest account.
- The Autoit script creates a text file with all the guest accounts with random passwords ready to paste onto the command line. Autoit is a free scripting language for windows that will do most anything. Autoit Homepage
- The last piece is up to you, which is how to distribute the usernames/passwords. We have a page on our sharepoint server that will give you that day's username/password.

All scripts were written and run on 2.9.51 systems.

Disable Script

<code class="routeros">:local date
#get system date
:set date [/system clock get date]
:local day
:local dayc
:local month
:local year
:local leap
:local dayof
#sets the values
:set month [:pick $date 0 3]
:set day [:pick $date 4 6]
#this is just incase the day has a 0 before the number
:set dayc [:pick $date 5 6]
:set year [:pick $date 7 11]
:set leap [:pick $date 0 6]
:set dayof 0
#sets starting date for month
:if ($month = "feb") do={ :set dayof 31 }
:if ($month = "mar") do={ :set dayof 59 } 
:if ($month = "apr") do={ :set dayof 90 }
:if ($month = "may") do={ :set dayof 120 }
:if ($month = "jun") do={ :set dayof 151 }
:if ($month = "jul") do={ :set dayof 181 }
:if ($month = "aug") do={ :set dayof 212 }
:if ($month = "sep") do={ :set dayof 243 }
:if ($month = "oct") do={ :set dayof 273 }
:if ($month = "nov") do={ :set dayof 304 }
:if ($month = "dec") do={ :set dayof 334 }
#check for leap year
:if ($leap = "feb/29") do={ :set dayof 366 }
#this removes any leading 0 s from the day
:if ([:pick $date 4 5] = 0) do={ :set day ($dayc)}
#determines day of year #
:if ($dayof < 366) do={ :set dayof ($dayof + $day) }
#disables last 5 days worth, just incase of power failure
:for e from ( $dayof - 5 ) to ( $dayof ) do={  /ip hotspot user disable ("guest" . $e) }
#this accounts for first of the year
:if ($dayof < 4) do={ :set dayof 366 }
:if ($dayof = 366) do={:for e from ( $dayof - 5 ) to ( $dayof ) do={  /ip hotspot user disable ("guest" . $e) }}</code>
Enable Script

<code class="routeros">:local date
:set date [/system clock get date]
:local day
:local dayc
:local month
:local year
:local leap
:local dayof
#sets variables
:set month [:pick $date 0 3]
#just incase there is a leading 0 in the day
:set dayc [:pick $date 5 6]
:set day [:pick $date 4 6]
:set year [:pick $date 7 11]
:set leap [:pick $date 0 6]
:set dayof 0
#sets start day of year
:if ($month = "feb") do={ :set dayof 31 }
:if ($month = "mar") do={ :set dayof 59 } 
:if ($month = "apr") do={ :set dayof 90 }
:if ($month = "may") do={ :set dayof 120 }
:if ($month = "jun") do={ :set dayof 151 }
:if ($month = "jul") do={ :set dayof 181 }
:if ($month = "aug") do={ :set dayof 212 }
:if ($month = "sep") do={ :set dayof 243 }
:if ($month = "oct") do={ :set dayof 273 }
:if ($month = "nov") do={ :set dayof 304 }
:if ($month = "dec") do={ :set dayof 334 }
#checks for leap year
:if ($leap = "feb/29") do={ :set dayof 366 }
#this removes any leading 0 s from the day
:if ([:pick $date 4 5] = 0) do={ :set day ($dayc)}
#totals up the day of year
:if ($dayof < 366) do={ :set dayof ($day + $dayof) }
:log info ($dayof)
#enables proper day
/ip hotspot user enable ("guest" . $dayof)</code>
Autoit Script

<code class="routeros">;generate usernames and passwords for hotspot.
$file = FileOpen(@ScriptDir & "\users.txt", 1)
; Check if file opened for writing OK
If $file = -1 Then
    MsgBox(0, "Error", "Unable to open file.")
    Exit
EndIf
for $x = 1 to 366
FileWriteLine($file, "add name=""guest" & $x & """ password=""" & Random(111111, 999999, 1) & """ profile=default  disabled=yes" & @CRLF)
Next
FileClose($file)</code>
Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
