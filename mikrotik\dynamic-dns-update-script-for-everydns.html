<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Dynamic DNS Update Script for EveryDNS - MikroTik Script RouterOS</title>
<meta content='Dynamic DNS Update Script for EveryDNS - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>

<h1>Dynamic DNS Update Script for EveryDNS - MikroTik Script RouterOS</h1>
<pre>This script allow you to easily maintain dynamic updates at EveryDNS free DNS server.
First you have to setup an account at http://www.everydns.com/, and with few steps create dynamic domain. After that you have to setup EasyDNS servers NS DNS parameters at domain seller site.

Next script is build with target to update DNS with two or more PPPoE connection with dynamics IP addresses.

<code class="routeros"># Define user variables
# Please NOTE that your username and password will be sent cleartext across the internet!
:local eduser "USERNAME"
:local edpass "PASS"
:local eddomain "mydomain.com" 
:local edinterface "INTERFACE_NAME"

# Change this global variable if you want more than 1 script
:global edlastip1
:local str
:local edip
:local ip

:if ([ :typeof $edlastip1 ] = nil ) do={ :global edlastip1 "0" }

:local edip [ /ip address get [/ip address find interface=$edinterface ] address ]

:if ([ :typeof $edip ] = nil ) do={
   :log info ("EveryDNS: No ip address on $edinterface .")
} else={

# strip off netmask correctly (MRz)
   :for i from=( [:len $edip] - 1) to=0 do={ 
      :if ( [:pick $edip $i] = "/") do={ 
	   :set edip [:pick $edip 0 $i];
      } 
   }

  :if ($edip != $edlastip1) do={

    :log info ("EveryDNS: $eddomain -> $edip")
# use url directly and mode http (MRz)
    :local str "/index.php?ver=0.1&ip=$edip&domain=$eddomain"
    /tool fetch url="http://dyn.everydns.net/$str" mode=http user=$eduser password=$edpass \
       dst-path=("/EveryDNS.".$eddomain)
    :delay 1 

# output any error messages received from file
    :local str [/file find name="EveryDNS.$eddomain"];
    :log info [/file get $str contents];
    /file remove $str
    :global edlastip1 $edip

  } 

}
# Coded by Paxy</code>
You should setup this script, so using username and password as you registered at http://www.everydns.com/. Dynamic domain should be name of sub-domain that you want to be updated automaticly ex. r.paxy.in.rs . Interface name field should be exact name of interface that you want to monitor for IP change, in my case it is PPPoE type interfaces.

If you want to monitor more than one interface, create another script, fill username and pass, change sub-domain to another sub-domain name, and change interface that monitor IP address. Because of spoofing protection, you have to change variable "edlastip1" to any other name, like "edlastip2" in whole script.
Script is using server with DNS name dyn.everydns.net.

All what is left now is to make scheduler to run this or those scripts every minute.

<code class="routeros">/system scheduler add name=EveryDNS1 interval=00:01 \
  on-event="/system script run everyDns1\r/system script run everyDns2"</code>

Happy hacking ...

Modified to work with version 3.23
use correct netmask stripping
use url in fetch tool
get any error messages received from server and write to system log.

If DNS record is not being updated, you can look at log entries, which will contain error messages received from the server, for example:

Authentication given Time check failed: wait longer between updates 
Additional info: 1240831067 - 1240831062 = 5 Exit code: 5 

Getting it to work with 3.22
If the /tool fetch url gives you problems, try to rewrite this line :

<code class="routeros"> /tool fetch url="http://dyn.everydns.net/$str" mode=http user=$eduser password=$edpass \
   dst-path=("/EveryDNS.".$eddomain)</code>
to

<code class="routeros">/tool fetch address="dyn.everydns.net" host="dyn.everydns.net" mode=http src-path="$str" \
   user=$eduser password=$edpass dst-path=("/EveryDNS.".$eddomain)</code>
Credit: https://wiki.mikrotik.com  
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
