<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Get Active Vpn Connections Via E-Mail - MikroTik Script RouterOS</title>
<meta content='Get Active Vpn Connections Via E-Mail - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Get Active Vpn Connections Via E-Mail - MikroTik Script RouterOS</h1>
<pre>The script sends an e-mail with all PPTP and L2TP servers and clients and their statuses.

It is pretty simple and there is no problem in adding OVPN and others.

Declare global and local variables (we only need to reuse the variable oldvlist). I like to create seperate variables for each VPN type.

<code class="routeros">:global oldvlist ;
:local pptplist "PPTP interfaces  " ;
:local l2tplist "L2TP interfaces  " ;
:local vlist ;
:local int ;</code>
Fill the variables with all VPN connections and their statuses.

<code class="routeros">:set pptplist "$pptplist SERVERS: " ;
:foreach int in=[/interface pptp-server find] do={
  :set pptplist "$pptplist PPTP: $[/interface pptp-server get $int name] :  $[/interface pptp-server get $int running] " ;
  :if ( [/interface pptp-server get $int running] = true ) do={
     :log error "PPTP: $[/interface pptp-server get $int name] : true" ;
  }
}
:set pptplist "$pptplist   CLIENTS: " ;
:foreach int in=[/interface pptp-client find] do={
  :set pptplist "$pptplist PPTP: $[/interface pptp-client get $int name] :  $[/interface pptp-client get $int running] " ;
  :if ( [/interface pptp-client get $int running] = true ) do={
     :log error "PPTP: $[/interface pptp-client get $int name] : true" ;
  }
}
:set l2tplist "$l2tplist SERVERS: " ;
:foreach int in=[/interface l2tp-server find] do={
  :set l2tplist "$l2tplist L2TP: $[/interface l2tp-server get $int name] :  $[/interface l2tp-server get $int running] " ;
  :if ( [/interface l2tp-server get $int running] = true ) do={
     :log error "L2TP: $[/interface l2tp-server get $int name] : true" ;
  }
}
:set l2tplist "$l2tplist   CLIENTS: " ;
:foreach int in=[/interface l2tp-client find] do={
  :set l2tplist "$l2tplist L2TP: $[/interface l2tp-client get $int name] :  $[/interface l2tp-client get $int running] " ;
  :if ( [/interface l2tp-client get $int running] = true ) do={
     :log error "L2TP: $[/interface l2tp-client get $int name] : true" ;
  }
}</code>
If you don't like the list of all interfaces, you can move the ':set list...' line inside the IF statement. That way the e-mail will contain only active connections. And beware that variable length is not unlimited so this will not work if you have a great number of VPN connections.

Fill the 'vlist' variable and send it vie a-mail if it is different than the 'oldvlist' variable

<code class="routeros">:set vlist "** Active VPN list **   $pptplist   $l2tplist" ;
:if ( $oldvlist != $vlist) do={
   /tool e-mail send body="$vlist" subject="VPN" to="RECIPIENT_EMAIL_ADDRESS" server="IP_OF_SMTP" from="$[/system identity get name]@EMAIL_DOMAIN"
   :set voldlist $vlist
   :log error "VPN status list sent"
} else={
   :log error "VPN no change"
}</code>
Replace IP_OD_SMTP with the IP address of hostname of the SMTP server that you can use (usually your ISP has one or two), RECIPIENT_EMAIL_LIST with your e-mail address, EMAIL_DOMAIN with the domain name that is allowed as a sender on that specific SMTP server.

It is my assumption that anyone connected needs at least two minutes to do something so I have set my scheduler to repeat every 2 minutes. You can set the scheduler to run in greater intervals but note that if someone connects after the script was run, and disconnects in under the schedule interval time, you will not see the connection.

Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
