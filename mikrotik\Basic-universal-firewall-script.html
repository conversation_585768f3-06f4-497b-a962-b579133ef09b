<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Basic universal firewall script - MikroTik Script RouterOS</title>
<meta content='Basic universal firewall script - Mikrotik RouterOS Script database' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Basic universal firewall script - Mikrotik Script RouterOS</h1>
<pre>This is a basic firewall that can be applied to any Router.
This script has basic rules to protect your router and avoid some unnecessary forwarding traffic. Pay attention for all comments before apply each DROP rules.

HANDS ON! First we need to create our ADDRESS LIST with all IPs we will use most times

Below you need to change x.x.x.x/x for your technical subnet. This subnet will have full access to the router.

<code class="routeros">/ip firewall address-list add address=x.x.x.x/x disabled=no list=support</code>

Below we have the bogon list.

<code class="routeros">/ip firewall address-list
add address=0.0.0.0/8 comment="Self-Identification [RFC 3330]" disabled=no list=bogons
add address=10.0.0.0/8 comment="Private[RFC 1918] - CLASS A # Check if you need this subnet before enable it"\
disabled=yes list=bogons
add address=*********/8 comment="Loopback [RFC 3330]" disabled=no list=bogons
add address=***********/16 comment="Link Local [RFC 3330]" disabled=no list=bogons
add address=**********/12 comment="Private[RFC 1918] - CLASS B # Check if you need this subnet before enable it"\
disabled=yes list=bogons
add address=***********/16 comment="Private[RFC 1918] - CLASS C # Check if you need this subnet before enable it"\
disabled=yes list=bogons
add address=*********/24 comment="Reserved - IANA - TestNet1" disabled=no list=bogons
add address=***********/24 comment="6to4 Relay Anycast [RFC 3068]" disabled=no list=bogons
add address=**********/15 comment="NIDB Testing" disabled=no list=bogons
add address=************/24 comment="Reserved - IANA - TestNet2" disabled=no list=bogons
add address=***********/24 comment="Reserved - IANA - TestNet3" disabled=no list=bogons
add address=*********/4 comment="MC, Class D, IANA # Check if you need this subnet before enable it"\
disabled=yes list=bogons</code>

Now we have protection against: SynFlood, ICMP Flood, Port Scan, Email Spam and much more. For more information read the comments.

<code class="routeros">/ip firewall filter
add action=add-src-to-address-list address-list=Syn_Flooder address-list-timeout=30m chain=input \
comment="Add Syn Flood IP to the list" connection-limit=30,32 disabled=no protocol=tcp tcp-flags=syn
add action=drop chain=input comment="Drop to syn flood list" disabled=no src-address-list=Syn_Flooder
add action=add-src-to-address-list address-list=Port_Scanner address-list-timeout=1w chain=input comment="Port Scanner Detect"\
disabled=no protocol=tcp psd=21,3s,3,1
add action=drop chain=input comment="Drop to port scan list" disabled=no src-address-list=Port_Scanner
add action=jump chain=input comment="Jump for icmp input flow" disabled=no jump-target=ICMP protocol=icmp
add action=drop chain=input\
comment="Block all access to the winbox - except to support list # DO NOT ENABLE THIS RULE BEFORE ADD YOUR SUBNET IN THE SUPPORT ADDRESS LIST"\
disabled=yes dst-port=8291 protocol=tcp src-address-list=!support
add action=jump chain=forward comment="Jump for icmp forward flow" disabled=no jump-target=ICMP protocol=icmp
add action=drop chain=forward comment="Drop to bogon list" disabled=no dst-address-list=bogons
add action=add-src-to-address-list address-list=spammers address-list-timeout=3h chain=forward comment="Add Spammers to the list for 3 hours"\
connection-limit=30,32 disabled=no dst-port=25,587 limit=30/1m,0 protocol=tcp
add action=drop chain=forward comment="Avoid spammers action" disabled=no dst-port=25,587 protocol=tcp src-address-list=spammers
add action=accept chain=input comment="Accept DNS - UDP" disabled=no port=53 protocol=udp
add action=accept chain=input comment="Accept DNS - TCP" disabled=no port=53 protocol=tcp
add action=accept chain=input comment="Accept to established connections" connection-state=established\
disabled=no
add action=accept chain=input comment="Accept to related connections" connection-state=related disabled=no
add action=accept chain=input comment="Full access to SUPPORT address list" disabled=no src-address-list=support
add action=drop chain=input comment="Drop anything else! # DO NOT ENABLE THIS RULE BEFORE YOU MAKE SURE ABOUT ALL ACCEPT RULES YOU NEED"\
disabled=yes
add action=accept chain=ICMP comment="Echo request - Avoiding Ping Flood, adjust the limit as needed" disabled=no icmp-options=8:0 limit=2,5 protocol=icmp
add action=accept chain=ICMP comment="Echo reply" disabled=no icmp-options=0:0 protocol=icmp
add action=accept chain=ICMP comment="Time Exceeded" disabled=no icmp-options=11:0 protocol=icmp
add action=accept chain=ICMP comment="Destination unreachable" disabled=no icmp-options=3:0-1 protocol=icmp
add action=accept chain=ICMP comment=PMTUD disabled=no icmp-options=3:4 protocol=icmp
add action=drop chain=ICMP comment="Drop to the other ICMPs" disabled=no protocol=icmp
add action=jump chain=output comment="Jump for icmp output" disabled=no jump-target=ICMP protocol=icmp
</code>

I think this is basic. You can add or remove anything else according to your needs. I hope it helps!

By Guilherme Ramires

Credit: https://wiki.mikrotik.com/wiki/Basic_universal_firewall_script
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
