<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Paypal With Hotspot And Walled Garden Bypass - MikroTik Script RouterOS</title>
<meta content='Paypal With Hotspot And Walled Garden Bypass - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Paypal With Hotspot And Walled Garden Bypass - MikroTik Script RouterOS</h1>
<pre>These scripts maintain a current list of PayPal ips in the walled garden.

These scripts are necessary to compensate for a deficiency in Mikrotik's hotspot walled-garden routine. The walled garden uses "/ip dns cache" to allow ips through the walled garden for SSL connections. Unfortunately, www.paypal.com ips (the actual webpage) stay in the cache for less than 5 minutes, and www.paypalobjects.com (the cascaded style sheet) ips stay there less than 20 seconds. Once Mikrotik corrects the challenge, these scripts will no longer be necessary, but until then...
These two domains should be entered in "/ip hotspot walled-garden":

www.paypal.com
www.paypalobjects.com

This script is paypal. It should be scheduled to run every 15 seconds. Use a start-time that will schedule this script to run 5 seconds before or after the ppupdate script below, so they don't run at the same time. I use 00:00:05 as the start-time.

<code class="routeros">:local ppobjip [:resolve www.paypalobjects.com];
:local today [/system clock get date];
:local paypalobject [/ip hotspot walled-garden ip find dst-address=$ppobjip];
:local thisdate none;
:local noip true;

:foreach i in=$paypalobject do={
    :set noip false;
    :set thisdate [/ip hotspot walled-garden ip get $i comment];
    :if ($thisdate != ("ppobj $today")) do={
            /ip hotspot walled-garden ip set $i comment="ppobj $today";
    }
}

:if ($noip) do={
    :log info "paypal script adding $ppobjip";
    /ip hotspot walled-garden ip add dst-address=$ppobjip comment="ppobj $today";
}</code>
This script is ppupdate. It should be run manually once before use, and then scheduled to run once a day at 23:30:00.

<code class="routeros">:local today [/system clock get date];
:local old [/ip hotspot walled-garden ip find comment~"ppobj*"];
:local thisrem none;
:local thisip none;

:foreach i in=$old do={
    :set thisrem [/ip hotspot walled-garden ip get $i comment];

    :if ($thisrem != ("ppobj $today")) do={
        /ip hotspot walled-garden ip remove $i;
    }
}

:resolve www.paypal.com;

:global paypalips [/ip dns cache find name="www.paypal.com"];
:global oldips [/ip hotspot walled-garden ip find comment="paypal"];

:foreach x in=$oldips do={
    /ip hotspot walled-garden ip remove $x;
}

:foreach i in=$paypalips do={
    :set thisip [/ip dns cache get $i address];
    /ip hotspot walled-garden ip add comment="paypal" dst-address=$thisip;
}</code>
Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
