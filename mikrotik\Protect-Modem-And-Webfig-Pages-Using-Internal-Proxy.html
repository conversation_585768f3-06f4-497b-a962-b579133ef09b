<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Protect Modem And Webfig Pages Using Internal Proxy - MikroTik Script RouterOS</title>
<meta content='Protect Modem And Webfig Pages Using Internal Proxy - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Protect Modem And Webfig Pages Using Internal Proxy - MikroTik Script RouterOS</h1>
<pre>This trick is to display the spice of the message page for those who want to try nosy to enter the modem or webfig Mikrotik page so that it is exciting and different, here we only use the error page on the proxy internal proxy.

The condition is to make sure the web proxy is not used and the WWW 80 IP service should change to another port

Ok, just go straight to the tutorial:
IP MODEM = *********** (just adjust the ip towards the modem)
IP WINBOX = ************ (just adjust ip towards mikrotik)
 
PUBLIC IP = *********** (public ip can be automated later, this is only an example)

1. Paste this in new terminal

<code class="routeros">/ip firewall nat
add action=redirect chain=dstnat dst-address="***********" disabled=no dst-port=80 protocol=tcp to-ports=3128 comment="Modem Protect"
add action=redirect chain=dstnat dst-address="***********" disabled=no dst-port=80 protocol=tcp to-ports=3128
add action=redirect chain=dstnat dst-address="************" disabled=no dst-port=80 protocol=tcp to-ports=3128
/ip proxy 
set enabled=yes port=3128</code>
2. Then paste the new terminal function to get a public IP

<code class="routeros">/system scheduler
add name=GET-IP-PUBLIC on-event="/tool fetch url=\"http://myip.dnsomatic.com/mypub\
licip.txt\" mode=http\r\
:local mypublicip [file get mypublicip.txt contents ]\r\
/ip firewall nat set [find comment=\"Modem Protect\"] dst-address=\$mypu\
blicip" policy=\
ftp,reboot,read,write,policy,test,password,sniff,sensitive,romon \
start-time=startup</code>
3. To activate the html error page we just need to press the Reset HTML button

4. Don't forget to block all access to the proxy, we can make an IP exception that has been specified. in this example I use global

5.Edit the webproxy / <span style="color:red"><b>error.html</b></span> page
here basic html skills are needed, please change it yourself :D	

6. If you don't want to be bothered, download <span style="color:red"><b>error.html</b></span> first then just edit and copy the code below to the <span style="color:red"><b>error.html</b></span> page

<code class="routeros">&lt;!doctype html public &quot;-//w3c//dtd html 3.2//en&quot;&gt;
&lt;html&gt;
&lt;head&gt;
&lt;title&gt;protect and secure&lt;/title&gt;
&lt;style id=&#039;page-skin-1&#039; type=&#039;text/css&#039;&gt;
body {
background-color: black;
color: green;
}
h1, h2, h3, h4, Ah5, h6{font-weight:700; font-family: &#039;open sans&#039;, helvetica, arial, sans-serif;font-size: 300%; color:#green}
&lt;/style&gt;
&lt;/head&gt;
&lt;body bgcolor=&quot;#111111&quot;&gt;
&lt;table border=&quot;0&quot; height=&quot;100%&quot; width=&quot;100%&quot;&gt;
&lt;tr valign=&quot;middle&quot;&gt;
&lt;td align=&quot;center&quot;&gt;
&lt;table border=&quot;0&quot;&gt;
&lt;tr&gt;
&lt;td colspan=&quot;3&quot;&gt;
&lt;center&gt;&lt;h1&gt;[ protect and secure ]&lt;/h1&gt;&lt;/center&gt;
&lt;/td&gt;
&lt;/tr&gt;
&lt;tr&gt;
&lt;td width=&quot;20%&quot;&gt;
&lt;/td&gt;
&lt;td&gt;
&lt;font face=&quot;tahoma,verdana,comic sans ms&quot;&gt;
&lt;br&gt;
demi keamanan halaman ini diproteksi dan dijaga ketat oleh administrator jaringan&lt;/font&gt;
&lt;/td&gt;
&lt;td width=&quot;20%&quot;&gt;
&lt;/td&gt;
&lt;/tr&gt;
&lt;tr&gt;
&lt;td width=&quot;20%&quot;&gt;
&lt;/td&gt;
&lt;td width=&quot;20%&quot;&gt;
&lt;/td&gt;
&lt;/tr&gt;
&lt;/table&gt;
&lt;/td&gt;
&lt;/tr&gt;
&lt;tr valign=&quot;bottom&quot;&gt;
&lt;td&gt;
&lt;hr size=&quot;1&quot;&gt;
&lt;table width=&quot;100%&quot; border=&quot;0&quot;&gt;
&lt;tr&gt;
&lt;td width=&quot;40%&quot;&gt;
&lt;font face=&quot;tahoma,verdana,comic sans ms&quot;&gt;
contact: 08132829XXX&lt;br /&gt;e-mail: <EMAIL>
&lt;/font&gt;
&lt;/td&gt;
&lt;td width=&quot;10%&quot; align=&quot;center&quot;&gt;
&copy; copyright  secure
&lt;/td&gt;
&lt;/table&gt;
&lt;/td&gt;
&lt;/tr&gt;
&lt;/table&gt;
&lt;/body&gt;
&lt;/html&gt; </code>

Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>


