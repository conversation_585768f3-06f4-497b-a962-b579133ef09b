<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Script to Create Directory - MikroTik Script RouterOS</title>
<meta content='Script to Create Directory - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Script to Create Directory - MikroTik Script RouterOS</h1>
<pre>This script will allow you to create directories in RouterOS using the fetch tool.

<code class="routeros">:local input "mydir/test/dir"
:local passwd ([/system resource get cpu-load] . \
		[/system identity get name] . \
		[/system resource get free-memory])
# Create group
:if ([:len [/user group find name=("dircreate")]] > 0) do={
   /user group remove "dircreate" }
/user group add name=dircreate policy=ftp comment="dircreate group"

# Create user
# Note: this user is restricted to 127.0.0.1 (no outside logins allowed)
:if ([:len [/user find name=("dircreate")]] > 0) do={
   /user remove "dircreate" }
/user add name=dircreate group=dircreate address=127.0.0.1 netmask=*************** \
	comment="dircreate user" password=[:tostr $passwd] disabled=no

# Create tmp file
/system identity export file=dircreate

# Copy tmp file to new location (creating directories as needed)
/tool fetch address=127.0.0.1 mode=ftp user=dircreate password=[:tostr $passwd] \
	src-path=dircreate.rsc dst-path=($input . "/dircreate.rsc")

# Clean up
/file remove "dircreate.rsc"
/file remove ($input . "/dircreate.rsc")
/user remove "dircreate"
/user group remove "dircreate"</code>
Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
