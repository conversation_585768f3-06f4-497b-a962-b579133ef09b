<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Dynamic DNS Update Script for ChangeIP behind NAT - MikroTik Script RouterOS</title>
<meta content='Dynamic DNS Update Script for ChangeIP behind NAT - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>

<h1>Dynamic DNS Update Script for ChangeIP behind NAT - MikroTik Script RouterOS</h1>
<pre>This script is intended to update a ChangeIP.com Dynamic DNS account when your device is behind nat.

You need to set your username, password and hostname.

This was tested successfully on RouterOS 5.7

<code class="routeros"># Dynamic DNS for ChangeIP.com behind NAT
# Modified by Jorge Amaral, officelan.pt
# For support send mail to support at offficelan dot pt
#
# The original script was written by "webasdf" on the Mikrotik foruns, i just modified it to work with ChangeIP.com
#
# Here is where you need to set your definitions
:local user "user"
:local pass "pass"
:local host "host"
##############
##############
:global lastwanip;
:if ([ :typeof $lastwanip ] = "nothing" ) do={ :global lastwanip 0.0.0.0 };
:local wanip [:resolve $host];
:if ( $wanip != $lastwanip ) do={
	/tool fetch mode=http address="checkip.dyndns.org" src-path="/" dst-path="/dyndns.checkip.html"
	:local result [/file get dyndns.checkip.html contents]
	:local resultLen [:len $result]
	:local startLoc [:find $result ": " -1]
	:set startLoc ($startLoc + 2)
	:local endLoc [:find $result "</body>" -1]
	:local currentIP [:pick $result $startLoc $endLoc]
	:set lastwanip $currentIP;
	:put [/tool dns-update name=$host address=$currentIP key-name=$user key=$pass ]
}</code>
You may copy and paste the script below into the terminal window.

<code class="routeros"># oct/13/2011 00:51:52 by RouterOS 5.7
#
/system script
add name=DDNS policy=\
    ftp,reboot,read,write,policy,test,winbox,password,sniff,sensitive,api \
    source="# Dynamic DNS for ChangeIP.com behind NA\
    T\r\
    # Modified by Jorge Amaral, officelan.pt\r\
    # For support send mail to support at offficelan dot pt\r\
    #\r\
    # The original script was written by \"webasdf\" on the Mikrotik foruns, i just modified it to work with ChangeIP.com\r\
    #\r\
    # Here is where you need to set your definitions\r\
    :local user \"user\"\r\
    :local pass \"pass\"\r\
    :local host \"host\"\r\
    ##############\r\
    ##############\r\
    :global lastwanip;\r\
    :if ([ :typeof \$lastwanip ] = \"nothing\" ) do={ :global lastwanip 0.0.0.0 };\r\
    :local wanip [:resolve \$host];\r\
    :if ( \$wanip != \$lastwanip ) do={\r\
    \t/tool fetch mode=http address=\"checkip.dyndns.org\" src-path=\"/\" dst-path=\"/dyndns.checkip.html\"\r\
    \t:local result [/file get dyndns.checkip.html contents]\r\
    \t:local resultLen [:len \$result]\r\
    \t:local startLoc [:find \$result \": \" -1]\r\
    \t:set startLoc (\$startLoc + 2)\r\
    \t:local endLoc [:find \$result \"</body>\" -1]\r\
    \t:local currentIP [:pick \$result \$startLoc \$endLoc]\r\
    \t:set lastwanip \$currentIP;\r\
    \t:put [/tool dns-update name=\$host address=\$currentIP key-name=\$user key=\$pass ]\r\
    }"</code>
Credit: https://wiki.mikrotik.com	
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
