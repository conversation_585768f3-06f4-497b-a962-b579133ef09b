<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Get IP Address List Netflix With RAW - MikroTik Script RouterOS</title>
<meta content='Get IP Address List Netflix With RAW - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Get IP Address List Netflix With RAW - MikroTik Script RouterOS</h1>
<pre>Get IP Address List for Netflix With RAW, get content via console developer Netflix.com, this used method take from Content and create dynamic ip address list with expired interval time. You can change it.

Please change ether2 with your self network.

<code class="routeros">/ip firewall mangle
add action=mark-connection chain=prerouting dst-address-list=Netflix new-connection-mark=NetflixConnection comment="Netflix by Heirro Networking" passthrough=yes src-address-list=IP-Local
add action=mark-packet chain=forward connection-mark=NetflixConnection in-interface=ether2 new-packet-mark=NetflixDownload passthrough=yes
add action=mark-packet chain=forward connection-mark=NetflixConnection new-packet-mark=NetflixUpload out-interface=ether2 passthrough=yes

/ip firewall raw
add action=add-dst-to-address-list address-list=Netflix address-list-timeout=1d chain=prerouting comment="Netflix by Heirro Networking" content=netflix.com
add action=add-dst-to-address-list address-list=Netflix address-list-timeout=1d chain=prerouting content=nflxso.net
add action=add-dst-to-address-list address-list=Netflix address-list-timeout=1d chain=prerouting content=nflxext.com
add action=add-dst-to-address-list address-list=Netflix address-list-timeout=1d chain=prerouting content=nflxvideo.net</code>
Credit: Heirro Networking
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

