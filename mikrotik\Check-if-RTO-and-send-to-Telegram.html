<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Check if RTO and send to Telegram - Mikrotik Script RouterOS</title>
<meta content='Check if RTO and send to Telegram - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>  
</head>  
<body>
<div id="hidelink"></div>
<h1>Check if RTO and send to Telegram - Mikrotik Script RouterOS</h1>
<pre>
Mikrotik script to check RTO on the routing path and send info directly to Telegram

just adjust "WAN-1" in the script with the name of each routing-table and for ip can be changed for example to *******

for telegram please learn how to make the boot yourself

<code class="routeros">:if ([/ping ******* routing-table=WAN-1 count=5] = 0) do={
:local datetime1 [/system clock get date]
:local datetime2 [/system clock get time]
:log error "RTO pada jam [ $datetime2 ] tanggal [ $datetime1 ]"
/tool fetch url="https://api.telegram.org/botxxxx:xxxxxxxxxxxxxxxxxxxxxxxxxxxxx/sendMessage\?chat_id=xxxx&text= ->> Jalur WAN-1 RTO pada jam [ $datetime2 ] tanggal [ $datetime1 ] " keep-result=no
}</code>
</pre>
Credit: www.o-om.com
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
