<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Mikrotik PCQ Generator For Queue Tree And Queue Simple - mikrotiktool.Github.io</title>
<meta content='Mikrotik PCQ Generator For Queue Tree And Queue Simple - mikrotiktool.Github.io' name='description'/>
<meta content='queue, pcq, script generator, mikrotik, router, winbox, termimal, rsc, script, hotspot, wireless, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon">
<meta property="og:title" content="Mikrotik PCQ Generator For Queue Tree And Queue Simple ">
<meta property="og:description" content="Mikrotik PCQ Generator For Queue Tree And Queue Simple ">
<meta property="og:image" content="https://user-images.githubusercontent.com/42666125/113652386-e468f080-96bd-11eb-9415-47195e1da871.png">
<meta property="og:image:alt" content="Mikrotik PCQ Generator For Queue Tree And Queue Simple ">
<meta property="og:type" content="website">
<meta property="og:url" content="https://mikrotiktool.github.io/mikrotik-pcq-generator.html">
<script data-ad-client="ca-pub-3815059393374366" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M5TL99Q9M2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-M5TL99Q9M2');
</script>	
<style>
*{-webkit-transition: all .3s ease-in;-moz-transition: all .3s ease-in;-ms-transition: all .3s ease-in;transition: all .3s ease-in;}
body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table{margin:0; padding:0}
body { font-size:16px; font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; margin:20px;}
html, body {
height: 100%;
width:100%;
margin-top: 0px;
padding:0;
}
* {
box-sizing: border-box;
}
.logo {
margin-top:30px; 
color: #111;
text-decoration:none;
font-size:27px;
font-weight:bold; 
}	
.logo a{
color:#111 !important;
text-decoration:none !important;
}
.Menu{
width:100%;
border-bottom:1px solid #ccc;
height:100%;
padding-bottom: 15px;
margin-bottom: 20px;
font-size:14px;
font-weight:bold;
}
#wrap{
width:1220px;
margin:0 auto;
padding:10px;
background:#fff
}
.header{
height:auto; 
width:100%;
margin-top:0px;
margin-bottom:10px;
border-top:1px solid #ccc;
}
.main-wrap{
display: flex;
}   
.content{
flex:1;
float:right;
width:800px;
padding: 10px;
border-top:1px solid #bbb;
border-bottom:1px solid #bbb;
border-right:1px solid #bbb;
background:#ddd;
}
.sidebar{
float:left;
width:400px;
Padding-top:8px;
padding-left: 10px;
Padding-right:10px;
Padding-bottom:20px;
background-color:#ddd;
border:1px solid #bbb; 
height:auto;
}
.footer{
font-size:14px;
padding-top:15px;
text-align:center;
clear:both;
width:auto;
}

h1 {
color:#111;
font-size:22px;
margin-bottom:10px;
margin-top:10px;
}
a:link {
color: #9E2A2B;
}
a:visited {
color: #9E2A2B;
}
a:hover {
color: #9E2A2B;
}
a:active {
color: #9E2A2B;
}
.menu {
clear:both;
width:auto;
padding-bottom:13px;
}
.menu1 {
width:360px;
margin-bottom:1px;
font-weight:bold;
background-color:#4CAF50;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu1 a:link {
text-decoration:none;
color:white;
}
.menu1 a:visited{
text-decoration:none;
color:white;
}
.menu2 {
width:300px;
margin-bottom:1px;
font-weight:bold;
background-color:#bbb;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu2 a:link{
text-decoration:none;
color:white;
}
.menu2 a:visited{
text-decoration:none;
color:white;
}
button {
color: #fff;
background-color: #9E2A2B;
border-color: #9E2A2B;
border:none;
padding:8px;
width:177px;
font-size:16px;
font-weight:bold;
}
.row:after {
content: "";
display: table;
clear: both;
}  
input[type=checkbox]{
margin-right:7px; 
}
input[type=text], select, textarea {
width: 100%;
padding: 5px;
border: 1px solid #bbb;
border-radius: 1px;
resize: vertical;
margin-bottom:12px;
font-size:16px !important;
}
label {
padding: 5px 5px 5px 0;
display: inline-block;
}
input[type=submit] {
background-color: #9E2A2B;
color: white;
padding: 12px 20px;
border: none;
border-radius: 1px;
cursor: pointer;
float: right;
}
input[type=submit]:hover {
background-color: #45a049;
}
.col-25 {
float: left;
width: 25%;
margin-top: 6px;
}
.col-75 {
float: left;
width: 75%;
margin-top: 6px;
}
/* Clear floats after the columns */
.row:after {
content: "";
display: table;
clear: both;
}
.list-game {
height: 200px;
background:white;
overflow: scroll;
overflow-x: hidden;
width: 100%;
margin-top:2px;
padding: 3px;
border: 1px solid rgba(0,0,0,0.25);
}
.list-game label {
padding: 0;
display: inline-block;
} 
.list-mangle {
height: 490px;
background:#fff;
overflow: scroll;
width: 100%;
padding: 4px;
margin-top:8px;
border: 1px solid #ccc;
}
table, tr, td {
border: none;
}
/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}
/* Modal Content */
.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
}

/* The Close Button */
.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
} 
</style>
</head>
<body onLoad="callmodal()"> 
<div id="wrap">
<div class="logo">	  
<a href="https://mikrotiktool.github.io"><span style="color:#9E2A2B !important">mikrotiktool</span>.Github.io</a> <img style="float:right; padding-top:10px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=hits&edge_flat=false" alt="Hits"/>
</div>	
<div class="header">
<span style="float:left; display:inline;margin-right:6px; margin-top:10px">
<a href="https://mikrotiktool.github.io/mikrotik-pcq-generator.html"><img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="HOME"></a>
</span>
<h1>MIKROTIK <span style="color:#9E2A2B;">PCQ (PER-CONNECTION-QUEUE)</span> GENERATOR FOR QUEUE TREE AND QUEUE SIMPLE</h1>
</div>
<div class="main-wrap">
<div class="sidebar">
<div style="margin-bottom:10px;">
<label>Queue Option [ Queue Tree / Queue Simple ]</label><div style="clear: both;"></div><div style="clear: both;"></div>
<select onchange="myFunctionOuput()" id="pcq-list">
<option value="queue-tree">Queue Tree</option>
<option value="queue-simple">Queue Simple</option>
</select>
<div style="clear: both;"></div>
<label>Parent Name Queue</label><div style="clear: both;"></div>
<input type="text" id="input-parent" value="GLOBAL-CONNECTION" placeholder="GLOBAL-CONNECTION">
<div style="float:left; width:180px;">
<label>Sub Queue Upload</label><div style="clear: both;"></div>
<input type="text" id="input-up-sub-parent" value="Upload-Client" placeholder="Upload-Client">
</div>
<div style="float:left; width:180px;margin-left:10px">
<label>Sub Queue Download</label><div style="clear: both;"></div>
<input type="text" id="input-down-sub-parent" value="Download-Client" placeholder="Download-Client">
</div>
<div style="float:left; width:180px;">
<label>Upload Max-Limit</label><div style="clear: both;"></div>
<input onkeydown="upperCaseF(this)" type="text" id="input-up-parent" value="10M" placeholder="10M">
</div>
<div style="float:left; width:180px;margin-left:10px">
<label>Download Max-Limit</label><div style="clear: both;"></div>
<input onkeydown="upperCaseF(this)" type="text" id="input-down-parent" value="50M" placeholder="50M">
</div>
</div>
<div style="clear: both;"></div>
<div style="margin-top:10px;margin-bottom:10px;border-bottom:1px solid #bbb">Setup PCQ Custom Name and Rate for limit Client</div>
<div style="float:left;width:180px;">
<label>PCQ Upload Name</label><div style="clear: both;"></div>
<input type="text" id="pcq-upload-name" value="PCQ-Up" placeholder="PCQ-Up">
</div>
<div style="float:left;width:180px;margin-left:10px;">
<label>PCQ Download Name</label>
<input type="text" id="pcq-download-name" value="PCQ-Down" placeholder="PCQ-Down">
</div>
<div style="float:left; width:180px;">
<label>PCQ Up Limit / Client</label><div style="clear: both;"></div>
<input onkeydown="upperCaseF(this)" type="text" id="pcq-up-rate-limit" value="1M" placeholder="1M">
</div>
<div style="float:left; width:180px;margin-left:10px;">
<label>PCQ Down Limit / Client</label><div style="clear: both;"></div>
<input onkeydown="upperCaseF(this)" type="text" id="pcq-down-rate-limit" value="2M" placeholder="2M">
</div>
<div style="margin-top:20px;margin-bottom:0px">
<input type="checkbox" id="auto-shared"><label for="auto-shared" style="padding-left:0px">Auto set PCQ for full Bandwidth Shared</label><div style="clear: both;"></div>
</div>
<button style="margin-top:20px;" type="button" onclick="myFunctionInput()">Generate Script</button> <button style="margin-left:10px" type="button" onclick="location.reload()">Clear All Script</button> 
<div style="clear: both;"></div>
</div>
<div class="content"> 
<span style="margin-left:2px">Script Generator Result</span>
<div class="list-mangle">
<div style="width:100%; height:90px; margin-top:10px; margin-left:10px; margin-bottom:5px;">	
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<!-- MIKROTIK SCRIPT DB3 -->
<ins class="adsbygoogle"
style="display:inline-block;width:728px;height:90px"
data-ad-client="ca-pub-3815059393374366"
data-ad-slot="4637807296"></ins>
<script>
(adsbygoogle = window.adsbygoogle || []).push({});
</script> 
</div>
<table id="showScript" style="padding:5px; white-space: nowrap;">
<tr>
<td>
<span style="color:#9E2A2B;">
####################################################################<br>
# MIKROTIK PCQ GENERATOR FOR QUEUE TREE AND QUEUE SIMPLE<br>
# Date/Time: <span id="tanggalwaktu"></span><br>
# By mikrotiktool.Github.io +  <br>
####################################################################<br><br>
</span>
<div id="list-output">
<b>/queue type</b><br>
add kind=pcq name="<span id="queue-type-name-up" style="color:#9E2A2B;">PCQ-Up</span>" pcq-classifier=src-address pcq-rate="<span id="queue-type-rate-up" style="color:#9E2A2B;">1M</span>"<br>
add kind=pcq name="<span id="queue-type-name-down" style="color:#9E2A2B;">PCQ-Down</span>" pcq-classifier=dst-address pcq-rate="<span id="queue-type-rate-down" style="color:#9E2A2B;">2M</span>"<br>
<span style="display:none" id="hide-show-queue-simple">
<b>/queue simple</b><br>
add comment="Script Generator  Secure!" name="<span id="queue-simple-gb-1" style="color:#9E2A2B;">GLOBAL-CONNECTION</span>" target="***********/16,**********/12,10.0.0.0/8"<br>
add max-limit="<span style="color:#9E2A2B;"><span id="queue-simple-max-1a">10M</span>/<span id="queue-simple-max-1b">10M</span></span>" name="<span id="queue-simple-name-1" style="color:#9E2A2B;">Upload-Client</span>" packet-marks="upload-client"  parent="<span id="queue-simple-gb-2" style="color:#9E2A2B;">GLOBAL-CONNECTION</span>" queue="<span style="color:#9E2A2B;"><span id="queue-simple-pcq-1">PCQ-Up</span>/default-small</span>" target="***********/16,**********/12,10.0.0.0/8"<br>
add max-limit="<span style="color:#9E2A2B;"><span id="queue-simple-max-2a">50M</span>/<span id="queue-simple-max-2b">50M</span></span>" name="<span id="queue-simple-name-2" style="color:#9E2A2B;">Download-Client</span>" packet-marks="download-client" parent="<span id="queue-simple-gb-3" style="color:#9E2A2B;">GLOBAL-CONNECTION</span>" queue="<span style="color:#9E2A2B;">default-small/<span id="queue-simple-pcq-2">PCQ-Down</span></span>" target="***********/16,**********/12,10.0.0.0/8"<br>
</span>
<span id="hide-show-queue-tree">
<b>/queue tree</b><br>
add comment="Script Generator  Secure!" name="<span id="queue-tree-gb-1" style="color:#9E2A2B;">GLOBAL-CONNECTION</span>" parent=global<br>
add max-limit="<span id="queue-tree-max-1" style="color:#9E2A2B;">10M</span>" name="<span id="queue-tree-name-1" style="color:#9E2A2B;">Upload-Client</span>" packet-mark="upload-client" parent="<span id="queue-tree-gb-2" style="color:#9E2A2B;">GLOBAL-CONNECTION</span>" queue="<span id="queue-tree-pcq-1" style="color:#9E2A2B;">PCQ-Up</span>"<br>
add max-limit="<span id="queue-tree-max-2" style="color:#9E2A2B;">50M</span>" name="<span id="queue-tree-name-2" style="color:#9E2A2B;">Download-Client</span>" packet-mark="download-client" parent="<span id="queue-tree-gb-3" style="color:#9E2A2B;">GLOBAL-CONNECTION</span>" queue="<span id="queue-tree-pcq-2" style="color:#9E2A2B;">PCQ-Down</span>"<br>
</span>
<b>/ip firewall mangle</b><br>
add comment="Script Generator  Secure!" action=mark-connection chain=forward new-connection-mark="global-conn" passthrough=yes src-address-list=LOCAL-IP<br>
add action=mark-packet chain=forward connection-mark="global-conn" src-address-list=LOCAL-IP new-packet-mark="upload-client" passthrough=no<br>
add action=mark-packet chain=forward connection-mark="global-conn" dst-address-list=LOCAL-IP new-packet-mark="download-client" passthrough=no<br>
<b>/ip firewall address-list</b><br>
add address=***********/16 list=LOCAL-IP<br>
add address=**********/12 list=LOCAL-IP<br>
add address=10.0.0.0/8 list=LOCAL-IP<br>
</div>
</td>
</tr>			
</table>
</div>
<br>
<button style="margin-top:-5px" id="copyscript" type="button" onclick="selectElementContents(document.getElementById('showScript'))">Copy Script</button><span style="padding:7px; font-weight:normal;margin-left:10px"><b>Copy-Paste the Script into the Terminal!</b></span>
</div>
</div>	
<div class="footer">
© Copyright 2020-2021 <a href="https://mikrotiktool.github.io/">mikrotiktool.github.io</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a> - Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a>  
</div>
</div>
 	  
	<script type="text/javascript">
var modal = document.getElementById("myModal");
var span = document.getElementsByClassName("close")[0];
function callmodal() {
  modal.style.display = "block";
}
span.onclick = function() {
  modal.style.display = "none";
}
window.onclick = function(event) {
  if (event.target == modal) {
    modal.style.display = "none";
  }
}
</script> 
<script>  
var _0x498a=['queue-tree-gb-2','queue-tree-name-1','queue-tree-gb-1','queue-simple-max-2a','input-down-sub-parent','tanggalwaktu','1546709ciityq','selectNodeContents','pcq-download-name','3TSYEpZ','style','512K','queue-tree','block','innerHTML','body','toFixed','queue-type-rate-down','queue-type-rate-up','parseFloat','276543gPiANt','15sJHhOe','execCommand','queue-type-name-up','auto-shared','input-up-sub-parent','addRange','queue-tree-gb-3','916452Nhegwg','getElementById','hide-show-queue-simple','pcq-upload-name','input-up-parent','value','queue-simple-gb-2','queue-simple-name-2','1RjytGL','57289ecpsLO','pcq-list','pcq-up-rate-limit','hide-show-queue-tree','copy','none','queue-simple-max-1a','display','queue-tree-max-2','queue-simple-max-2b','queue-tree-max-1','928604UfGgvq','queue-tree-pcq-2','createTextRange','select','pcq-down-rate-limit','moveToElementText','checked','930662XcnZBB','queue-tree-name-2','disabled','299701XaBvcb','getSelection','queue-tree-pcq-1','queue-simple-gb-3'];var _0x28eb=function(_0x2c45b2,_0x15f1e0){_0x2c45b2=_0x2c45b2-0x75;var _0x498a34=_0x498a[_0x2c45b2];return _0x498a34;};var _0x2fe440=_0x28eb;(function(_0x55a1d5,_0x5c4805){var _0xfd85a0=_0x28eb;while(!![]){try{var _0x134f33=-parseInt(_0xfd85a0(0x8f))+-parseInt(_0xfd85a0(0xaf))+-parseInt(_0xfd85a0(0x79))*-parseInt(_0xfd85a0(0x8c))+-parseInt(_0xfd85a0(0x9c))*parseInt(_0xfd85a0(0xa7))+-parseInt(_0xfd85a0(0x7a))*parseInt(_0xfd85a0(0xa8))+parseInt(_0xfd85a0(0x85))+parseInt(_0xfd85a0(0x99));if(_0x134f33===_0x5c4805)break;else _0x55a1d5['push'](_0x55a1d5['shift']());}catch(_0x3c9773){_0x55a1d5['push'](_0x55a1d5['shift']());}}}(_0x498a,0x7a47a));var dt=new Date();document[_0x2fe440(0xb0)](_0x2fe440(0x98))['innerHTML']=dt['toLocaleString']();function upperCaseF(_0x7f7f12){setTimeout(function(){var _0x678495=_0x28eb;_0x7f7f12[_0x678495(0x76)]=_0x7f7f12['value']['toUpperCase']();},0x1);}function resultToFixed(_0x2f430a){var _0x2ddb92=_0x2fe440;return Number[_0x2ddb92(0xa6)](_0x2f430a)[_0x2ddb92(0xa3)](0x2);}document[_0x2fe440(0xb0)]('auto-shared')['onchange']=function(){var _0x4fd8df=_0x2fe440,_0x168532=document[_0x4fd8df(0xb0)](_0x4fd8df(0xab)),_0x58aaa6=document['getElementById'](_0x4fd8df(0x7c))[_0x4fd8df(0x76)],_0x2876bb=document[_0x4fd8df(0xb0)]('pcq-down-rate-limit')[_0x4fd8df(0x76)];_0x168532[_0x4fd8df(0x8b)]?(document['getElementById']('pcq-up-rate-limit')['disabled']=!![],document[_0x4fd8df(0xb0)](_0x4fd8df(0x89))[_0x4fd8df(0x8e)]=!![],document['getElementById']('pcq-up-rate-limit')[_0x4fd8df(0x76)]='0',document[_0x4fd8df(0xb0)](_0x4fd8df(0x89))[_0x4fd8df(0x76)]='0',document[_0x4fd8df(0xb0)](_0x4fd8df(0xa5))['innerHTML']='0',document[_0x4fd8df(0xb0)](_0x4fd8df(0xa4))[_0x4fd8df(0xa1)]='0'):(document[_0x4fd8df(0xb0)](_0x4fd8df(0x7c))[_0x4fd8df(0x8e)]=![],document[_0x4fd8df(0xb0)](_0x4fd8df(0x89))[_0x4fd8df(0x8e)]=![],document['getElementById'](_0x4fd8df(0x7c))[_0x4fd8df(0x76)]=_0x4fd8df(0x9e),document[_0x4fd8df(0xb0)](_0x4fd8df(0x89))['value']='1M',document['getElementById'](_0x4fd8df(0xa5))['innerHTML']=_0x4fd8df(0x9e),document['getElementById'](_0x4fd8df(0xa4))[_0x4fd8df(0xa1)]='1M');};function myFunctionInput(){var _0x44daed=_0x2fe440,_0x583605=document[_0x44daed(0xb0)]('input-parent')['value'],_0x5052f6=document['getElementById'](_0x44daed(0xac))[_0x44daed(0x76)],_0x220a0c=document['getElementById'](_0x44daed(0x97))['value'],_0x45dee8=document['getElementById'](_0x44daed(0x75))[_0x44daed(0x76)],_0x3d6578=document['getElementById']('input-down-parent')[_0x44daed(0x76)],_0x4a9f2b=document[_0x44daed(0xb0)](_0x44daed(0xb2))[_0x44daed(0x76)],_0x17248e=document[_0x44daed(0xb0)](_0x44daed(0x9b))['value'],_0x445001=document['getElementById'](_0x44daed(0x7c))[_0x44daed(0x76)],_0x40b9c8=document['getElementById']('pcq-down-rate-limit')[_0x44daed(0x76)];document[_0x44daed(0xb0)]('queue-simple-gb-1')['innerHTML']=_0x583605,document[_0x44daed(0xb0)](_0x44daed(0x77))['innerHTML']=_0x583605,document[_0x44daed(0xb0)](_0x44daed(0x92))['innerHTML']=_0x583605,document[_0x44daed(0xb0)](_0x44daed(0x80))['innerHTML']=_0x45dee8,document[_0x44daed(0xb0)](_0x44daed(0x96))[_0x44daed(0xa1)]=_0x3d6578,document[_0x44daed(0xb0)]('queue-simple-max-1b')['innerHTML']=_0x45dee8,document['getElementById'](_0x44daed(0x83))[_0x44daed(0xa1)]=_0x3d6578,document['getElementById']('queue-simple-name-1')['innerHTML']=_0x5052f6,document[_0x44daed(0xb0)](_0x44daed(0x78))[_0x44daed(0xa1)]=_0x220a0c,document[_0x44daed(0xb0)]('queue-simple-pcq-1')[_0x44daed(0xa1)]=_0x4a9f2b,document[_0x44daed(0xb0)]('queue-simple-pcq-2')[_0x44daed(0xa1)]=_0x17248e,document[_0x44daed(0xb0)](_0x44daed(0x95))[_0x44daed(0xa1)]=_0x583605,document['getElementById'](_0x44daed(0x93))['innerHTML']=_0x583605,document[_0x44daed(0xb0)](_0x44daed(0xae))[_0x44daed(0xa1)]=_0x583605,document['getElementById'](_0x44daed(0x84))['innerHTML']=_0x45dee8,document[_0x44daed(0xb0)](_0x44daed(0x82))['innerHTML']=_0x3d6578,document[_0x44daed(0xb0)](_0x44daed(0x94))[_0x44daed(0xa1)]=_0x5052f6,document[_0x44daed(0xb0)](_0x44daed(0x8d))[_0x44daed(0xa1)]=_0x220a0c,document[_0x44daed(0xb0)](_0x44daed(0x91))[_0x44daed(0xa1)]=_0x4a9f2b,document['getElementById'](_0x44daed(0x86))[_0x44daed(0xa1)]=_0x17248e,document[_0x44daed(0xb0)](_0x44daed(0xaa))[_0x44daed(0xa1)]=_0x4a9f2b,document[_0x44daed(0xb0)]('queue-type-name-down')[_0x44daed(0xa1)]=_0x17248e,document[_0x44daed(0xb0)](_0x44daed(0xa5))['innerHTML']=_0x445001,document[_0x44daed(0xb0)](_0x44daed(0xa4))['innerHTML']=_0x40b9c8;}function myFunctionOuput(){var _0x4f4cfc=_0x2fe440;document['getElementById']('pcq-list')['value']=='queue-simple'&&(document[_0x4f4cfc(0xb0)](_0x4f4cfc(0x7d))[_0x4f4cfc(0x9d)]['display']=_0x4f4cfc(0x7f),document[_0x4f4cfc(0xb0)](_0x4f4cfc(0xb1))[_0x4f4cfc(0x9d)][_0x4f4cfc(0x81)]=_0x4f4cfc(0xa0)),document[_0x4f4cfc(0xb0)](_0x4f4cfc(0x7b))['value']==_0x4f4cfc(0x9f)&&(document[_0x4f4cfc(0xb0)](_0x4f4cfc(0x7d))[_0x4f4cfc(0x9d)][_0x4f4cfc(0x81)]='block',document[_0x4f4cfc(0xb0)]('hide-show-queue-simple')['style'][_0x4f4cfc(0x81)]=_0x4f4cfc(0x7f));}function selectElementContents(_0x560bbe){var _0x4c65a8=_0x2fe440,_0x3c2721=document[_0x4c65a8(0xa2)],_0x3b1635,_0x9e986e;if(document['createRange']&&window[_0x4c65a8(0x90)]){_0x3b1635=document['createRange'](),_0x9e986e=window[_0x4c65a8(0x90)](),_0x9e986e['removeAllRanges']();try{_0x3b1635[_0x4c65a8(0x9a)](_0x560bbe),_0x9e986e['addRange'](_0x3b1635);}catch(_0x287076){_0x3b1635['selectNode'](_0x560bbe),_0x9e986e[_0x4c65a8(0xad)](_0x3b1635);}}else _0x3c2721['createTextRange']&&(_0x3b1635=_0x3c2721[_0x4c65a8(0x87)](),_0x3b1635[_0x4c65a8(0x8a)](_0x560bbe),_0x3b1635[_0x4c65a8(0x88)]());document[_0x4c65a8(0xa9)](_0x4c65a8(0x7e));}
</script>
</body>
</html>