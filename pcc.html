<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Load Balancing PCC (LB PCC) Script Generator - mikrotiktool.Github.io</title>
<meta content='Load Balancing (LB) PCC Script Generator ' name='description'/>
<meta content='script generator, mikrotik, LB, PCC, load balancing, router, winbox, termimal, rsc, script, hotspot, wireless, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon">
<meta property="og:title" content="Load Balancing PCC (LB PCC) Script Generator ">
<meta property="og:description" content="Load Balancing PCC (LB PCC) Script Generator ">
<meta property="og:image" content="https://user-images.githubusercontent.com/42666125/111255160-1c45c080-8649-11eb-8238-ae9fb987a97d.png">
<meta property="og:image:alt" content="Load Balancing PCC (LB PCC) Script Generator ">
<meta property="og:type" content="website">
<meta property="og:url" content="https://mikrotiktool.github.io/pcc.html">
<script data-ad-client="ca-pub-3815059393374366" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M5TL99Q9M2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-M5TL99Q9M2');
</script>
<style>

*{-webkit-transition: all .3s ease-in;-moz-transition: all .3s ease-in;-ms-transition: all .3s ease-in;transition: all .3s ease-in;}
body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table{margin:0; padding:0}
body { font-size:16px; font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; margin:20px;}
html, body {
height: 100%;
width:100%;
margin-top: 0px;
padding:0;
}
* {
box-sizing: border-box;
}
.logo {
margin-top:0px; 
color: #111;
text-decoration:none;
font-size:27px;
font-weight:bold; 
}	
.logo a{
color:#111 !important;
text-decoration:none !important;
}
.Menu{
width:100%;
border-bottom:1px solid #ccc;
height:100%;
padding-bottom: 15px;
margin-bottom: 20px;
font-size:14px;
font-weight:bold;
}
#wrap{
width:1120px;
margin:0 auto;
padding:10px;
background:#fff
}
.header{
height:auto; 
width:100%;
margin-top:0px;
margin-bottom:10px;
border-top:1px solid #ccc;
}
.main-wrap{
display:flex;
}   
.content{
flex:1;
float:right;
width:800px;
padding: 10px;
border-top:1px solid #bbb;
border-bottom:1px solid #bbb;
border-right:1px solid #bbb;
background:#ddd;
height:auto;
}
.sidebar{
float:left;
width:300px;
Padding-top:8px;
padding-left: 10px;
Padding-right:10px;
Padding-bottom:20px;
background-color:#ddd;
border:1px solid #bbb; 
height:auto;
}
.footer{
font-size:14px;
padding-top:15px;
clear:both;
width:auto;
}
h1 {
color:#111;
font-size:22px;
margin-bottom:10px;
margin-top:10px;
}
a:link {
color: #08c;
}
a:visited {
color: #08c;
}
a:hover {
color: #08c;
}
a:active {
color: #08c;
}
.menu {
clear:both;
width:auto;
padding-bottom:13px;
}
.menu1 {
width:360px;
margin-bottom:1px;
font-weight:bold;
background-color:#4CAF50;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu1 a:link {
text-decoration:none;
color:white;
}
.menu1 a:visited{
text-decoration:none;
color:white;
}
.menu2 {
width:300px;
margin-bottom:1px;
font-weight:bold;
background-color:#bbb;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu2 a:link{
text-decoration:none;
color:white;
}
.menu2 a:visited{
text-decoration:none;
color:white;
}
button {
margin-top: 15px;
color: #fff;
background-color: #08c;
border-color: #08c;
border:none;
padding:8px;
width:130px;
font-size:16px;
font-weight:bold;
display:inline-block;
}
.row:after {
content: "";
display: table;
clear: both;
}  
input[type=text], select, textarea {
width: 100%;
padding: 5px;
border: 1px solid #bbb;
border-radius: 1px;
margin-bottom:10px;
 margin-top:10px;
font-size:16px !important;
}
input[type=submit] {
background-color: #08c;
color: white;
padding: 12px 20px;
border: none;
border-radius: 1px;
cursor: pointer;
float: right;
}
input[type=submit]:hover {
background-color: #45a049;
}
.col-25 {
float: left;
width: 25%;
margin-top: 6px;
}
.col-75 {
float: left;
width: 75%;
margin-top: 6px;
}
/* Clear floats after the columns */
.row:after {
content: "";
display: table;
clear: both;
}
.list-game {
height: 200px;
background:white;
overflow: scroll;
overflow-x: hidden;
width: 100%;
margin-top:2px;
padding: 3px;
border: 1px solid rgba(0,0,0,0.25);
}
.list-game label {
padding: 0;
display: inline-block;
} 
.list-mangle {
height: 582px;
background:#fff;
overflow: scroll;
width: 100%;
padding: 4px;
margin-top:8px;
border: 1px solid #ccc;
}
table, tr, td {
border: none;
}
/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}
/* Modal Content */
.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
}

/* The Close Button */
.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}
</style>
</head>
<body onLoad="callmodal()"> 
<div id="wrap">
<div class="logo">	  
<a href="https://mikrotiktool.github.io"><span style="color:#08c !important">mikrotiktool</span>.Github.io</a> <img style="float:right; padding-top:10px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=hits&edge_flat=false" alt="Hits"/>
</div>		
<div class="header">
<span style="float:left; display:inline;margin-right:6px; margin-top:10px">
<a href="https://mikrotiktool.github.io/pcc.html"><img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="HOME"></a>
</span>
<h1>LB PCC / LOAD BALANCING <span style="color:#08c">PCC</span> SCRIPT GENERATOR FOR MIKROTIK ROUTEROS</h1>
</div>
<div class="main-wrap">
    <div class="sidebar">
			<label>Select Number Your ISP Line</label>
			<select style="margin-top:10px" id="wan-list" onclick="myFunctionOuput()">
				<option value="line2">2 Line ISP</option>
				<option value="line3">3 Line ISP</option>
				<option value="line4">4 Line ISP</option>
				<option value="line5">5 Line ISP</option>
				<option value="line6">6 Line ISP</option>
			</select>
			<div style="color:#08c; border:1px solid #08c; padding:7px;margin-top:0px;margin-bottom:10px">Atention! Please Change Your WAN Interface Name With Your Router Condition, Example: ether1 or E1-WAN or e1-indihome or pppoe-out1
			</div>

			<div style="float:left; width:130px;">
				<label>WAN ISP-1</label>
				<input type="text" id="wan1-isp" placeholder="Ex: ether1">
			</div>	
			<div style="float:left; width:130px;margin-left:10px">
			     <label>Gateway ISP-1</label>
				<input type="text" id="wan1-isp-ip" placeholder="Ex: ***********">
			</div>

			<div style="float:left; width:130px;">
				<label>WAN ISP-2</label>
				<input type="text" id="wan2-isp" placeholder="Ex: ether2">
			</div>
			<div style="float:left; width:130px;margin-left:10px">
				<label>Gateway ISP-2</label>
				<input type="text" id="wan2-isp-ip" placeholder="Ex: ***********">
			</div>
			<div style="float:left; width:130px;">
				<label>WAN ISP-3</label>
				<input type="text" id="wan3-isp" placeholder="Ex: ether3" disabled>
			</div>
			<div style="float:left; width:130px;margin-left:10px">
				<label>Gateway ISP-3</label>

				<input type="text" id="wan3-isp-ip" placeholder="Ex: ***********" disabled >
			</div>
			<div style="float:left; width:130px;">
				<label>WAN ISP-4</label>
				<input type="text" id="wan4-isp" placeholder="Ex: ether4" disabled>
			</div>
			<div style="float:left; width:130px;margin-left:10px">
				<label>Gateway ISP-4</label>
				<input type="text" id="wan4-isp-ip" placeholder="Ex: ***********" disabled >
			</div>

			<div style="float:left; width:130px;">
				<label>WAN ISP-5</label>
				<input type="text" id="wan5-isp" placeholder="Ex: ether5" disabled>
			</div>
			<div style="float:left; width:130px;margin-left:10px">
				<label>Gateway ISP-5</label>
	
				<input type="text" id="wan5-isp-ip" placeholder="Ex: ***********" disabled >
			</div>
	
			<div style="float:left; width:130px;">
				<label>WAN ISP-6</label>

				<input type="text" id="wan6-isp" placeholder="Ex: ether6" disabled>
			</div>
			<div style="float:left; width:130px;margin-left:10px;">
				<label>Gateway ISP-6</label>

				<input type="text" id="wan6-isp-ip" placeholder="Ex: ***********" disabled >
			</div>
			<button style="margin-right:2px" type="button" onclick="myFunctionInput()">Generate</button>
			<button type="button" onclick="location.reload()">Clear All</button>
	
    </div>
    <div class="content"> 
        <span style="margin-left:2px">Script Generator Result</span>
         <div class="list-mangle">
		 <div style="width:100%; height:90px; margin-top:10px; margin-left:10px; margin-bottom:5px;">	
			<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
			<!-- MIKROTIK SCRIPT DB3 -->
			<ins class="adsbygoogle"
			style="display:inline-block;width:728px;height:90px"
			data-ad-client="ca-pub-3815059393374366"
			data-ad-slot="4637807296"></ins>
			<script>
			(adsbygoogle = window.adsbygoogle || []).push({});
			</script> 
		</div>
            <table id="showScript" style="padding:5px; white-space: nowrap;">
               <tr>
                  <td>
                     <span style="color:#08c;">
                     ################################################<br>
                     # LOAD BALANCING (LB) PCC SCRIPT GENERATOR<br>
                     # Date/Time: <span id="tanggalwaktu"></span><br>
                     #  <br>
                     # Load Balancing Metode -> <span id="vpn-list-text-info" style="font-weight:bold;text-transform: uppercase">PCC</span><br>
                     ################################################<br><br>
                     </span>
					 <b>/ip firewall address-list</b><br>
					 add address=***********/16 list=LOCAL-IP comment="LB By BNT"<br>
					 add address=**********/12 list=LOCAL-IP comment="LB By BNT" <br>
					 add address=10.0.0.0/8 list=LOCAL-IP comment="LB By BNT"<br>
					 
					<b>/ip firewall nat</b><br>
					<span id="wan1-nat">add chain=srcnat out-interface="<span id="wan1-nat-text"></span>" action=masquerade comment="LB By BNT"<br></span>
					<span id="wan2-nat">add chain=srcnat out-interface="<span id="wan2-nat-text"></span>" action=masquerade comment="LB By BNT"<br></span>
					<span id="wan3-nat" style="display:none">add chain=srcnat out-interface="<span id="wan3-nat-text"></span>" action=masquerade comment="LB By BNT"<br></span>
					<span id="wan4-nat" style="display:none">add chain=srcnat out-interface="<span id="wan4-nat-text"></span>" action=masquerade comment="LB By BNT"<br></span>
					<span id="wan5-nat" style="display:none">add chain=srcnat out-interface="<span id="wan5-nat-text"></span>" action=masquerade comment="LB By BNT"<br></span>
					<span id="wan6-nat" style="display:none">add chain=srcnat out-interface="<span id="wan6-nat-text"></span>" action=masquerade comment="LB By BNT"<br></span>

					<b>/ip route</b><br>
					<span id="wan1-route">add check-gateway=ping distance=1 gateway="<span id="wan1-route-text"></span>" routing-mark="<span id="wan1-route-text-r"></span>" comment="LB By BNT"<br></span>
					<span id="wan2-route">add check-gateway=ping distance=1 gateway="<span id="wan2-route-text"></span>" routing-mark="<span id="wan2-route-text-r"></span>" comment="LB By BNT"<br></span>
					<span id="wan3-route" style="display:none">add check-gateway=ping distance=1 gateway="<span id="wan3-route-text"></span>" routing-mark="<span id="wan3-route-text-r"></span>" comment="LB By BNT"<br></span>
					<span id="wan4-route" style="display:none">add check-gateway=ping distance=1 gateway="<span id="wan4-route-text"></span>" routing-mark="<span id="wan4-route-text-r"></span>" comment="LB By BNT"<br></span>
					<span id="wan5-route" style="display:none">add check-gateway=ping distance=1 gateway="<span id="wan5-route-text"></span>" routing-mark="<span id="wan5-route-text-r"></span>" comment="LB By BNT"<br></span>
					<span id="wan6-route" style="display:none">add check-gateway=ping distance=1 gateway="<span id="wan6-route-text"></span>" routing-mark="<span id="wan6-route-text-r"></span>" comment="LB By BNT"<br></span>
					<span id="wan1-route-failover">add check-gateway=ping distance=1 gateway="<span id="wan1-route-failover-text"></span>" comment="LB By BNT"<br></span>
					<span id="wan2-route-failover">add check-gateway=ping distance=2 gateway="<span id="wan2-route-failover-text"></span>" comment="LB By BNT"<br></span>
					<span id="wan3-route-failover" style="display:none">add check-gateway=ping distance=3 gateway="<span id="wan3-route-failover-text"></span>" comment="LB By BNT"<br></span>
					<span id="wan4-route-failover" style="display:none">add check-gateway=ping distance=4 gateway="<span id="wan4-route-failover-text"></span>" comment="LB By BNT"<br></span>
					<span id="wan5-route-failover" style="display:none">add check-gateway=ping distance=5 gateway="<span id="wan5-route-failover-text"></span>" comment="LB By BNT"<br></span>
					<span id="wan6-route-failover" style="display:none">add check-gateway=ping distance=6 gateway="<span id="wan6-route-failover-text"></span>" comment="LB By BNT"<br></span>

					<b>/ip firewall mangle</b><br>
					<span id="wan1-mangle-input">add action=mark-connection chain=input in-interface="<span id="wan1-mangle-input-text"></span>" new-connection-mark="<span id="wan1-mangle-input-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>
					<span id="wan2-mangle-input">add action=mark-connection chain=input in-interface="<span id="wan2-mangle-input-text"></span>" new-connection-mark="<span id="wan2-mangle-input-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>
					<span id="wan3-mangle-input" style="display:none">add action=mark-connection chain=input in-interface="<span id="wan3-mangle-input-text"></span>" new-connection-mark="<span id="wan3-mangle-input-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>
					<span id="wan4-mangle-input" style="display:none">add action=mark-connection chain=input in-interface="<span id="wan4-mangle-input-text"></span>" new-connection-mark="<span id="wan4-mangle-input-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>
					<span id="wan5-mangle-input" style="display:none">add action=mark-connection chain=input in-interface="<span id="wan5-mangle-input-text"></span>" new-connection-mark="<span id="wan5-mangle-input-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>
					<span id="wan6-mangle-input" style="display:none">add action=mark-connection chain=input in-interface="<span id="wan6-mangle-input-text"></span>" new-connection-mark="<span id="wan6-mangle-input-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>

					<span id="wan1-mangle-output">add action=mark-routing chain=output connection-mark="<span id="wan1-mangle-output-text"></span>" new-routing-mark="<span id="wan1-mangle-output-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>
					<span id="wan2-mangle-output">add action=mark-routing chain=output connection-mark="<span id="wan2-mangle-output-text"></span>" new-routing-mark="<span id="wan2-mangle-output-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>
					<span id="wan3-mangle-output" style="display:none">add action=mark-routing chain=output connection-mark="<span id="wan3-mangle-output-text"></span>" new-routing-mark="<span id="wan3-mangle-output-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>
					<span id="wan4-mangle-output" style="display:none">add action=mark-routing chain=output connection-mark="<span id="wan4-mangle-output-text"></span>" new-routing-mark="<span id="wan4-mangle-output-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>
					<span id="wan5-mangle-output" style="display:none">add action=mark-routing chain=output connection-mark="<span id="wan5-mangle-output-text"></span>" new-routing-mark="<span id="wan5-mangle-output-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>
					<span id="wan6-mangle-output" style="display:none">add action=mark-routing chain=output connection-mark="<span id="wan6-mangle-output-text"></span>" new-routing-mark="<span id="wan6-mangle-output-text-r"></span>" passthrough=yes comment="LB By BNT"<br></span>

					<span id="wan1-mangle-pcc">add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP dst-address-type=!local new-connection-mark="<span id="wan1-mangle-pcc-text"></span>" passthrough=yes per-connection-classifier=both-addresses-and-ports:<span id="wan1-mangle-pcc-both"><span style='color:#08c; font-weight:bold'>2/0</span></span> src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
					<span id="wan2-mangle-pcc">add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP dst-address-type=!local  new-connection-mark="<span id="wan2-mangle-pcc-text"></span>" passthrough=yes per-connection-classifier=both-addresses-and-ports:<span id="wan2-mangle-pcc-both"><span style='color:#08c; font-weight:bold'>2/1</span></span> src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
					<span id="wan3-mangle-pcc" style="display:none">add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP dst-address-type=!local new-connection-mark="<span id="wan3-mangle-pcc-text"></span>" passthrough=yes per-connection-classifier=both-addresses-and-ports:<span id="wan3-mangle-pcc-both"></span> src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
					<span id="wan4-mangle-pcc" style="display:none">add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP dst-address-type=!local  new-connection-mark="<span id="wan4-mangle-pcc-text"></span>" passthrough=yes per-connection-classifier=both-addresses-and-ports:<span id="wan4-mangle-pcc-both"></span> src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
					<span id="wan5-mangle-pcc" style="display:none">add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP  dst-address-type=!local  new-connection-mark="<span id="wan5-mangle-pcc-text"></span>" passthrough=yes per-connection-classifier=both-addresses-and-ports:<span id="wan5-mangle-pcc-both"></span> src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
					<span id="wan6-mangle-pcc" style="display:none">add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP  dst-address-type=!local  new-connection-mark="<span id="wan6-mangle-pcc-text"></span>" passthrough=yes per-connection-classifier=both-addresses-and-ports:<span id="wan6-mangle-pcc-both"></span> src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
								 
					<span id="wan1-mangle-pre">add action=mark-routing chain=prerouting connection-mark="<span id="wan1-mangle-pre-text"></span>" dst-address-list=!LOCAL-IP new-routing-mark="<span id="wan1-mangle-pre-text-r"></span>" passthrough=yes src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
					<span id="wan2-mangle-pre">add action=mark-routing chain=prerouting connection-mark="<span id="wan2-mangle-pre-text"></span>" dst-address-list=!LOCAL-IP new-routing-mark="<span id="wan2-mangle-pre-text-r"></span>" passthrough=yes src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
					<span id="wan3-mangle-pre" style="display:none">add action=mark-routing chain=prerouting connection-mark="<span id="wan3-mangle-pre-text"></span>" dst-address-list=!LOCAL-IP new-routing-mark="<span id="wan3-mangle-pre-text-r"></span>" passthrough=yes src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
					<span id="wan4-mangle-pre" style="display:none">add action=mark-routing chain=prerouting connection-mark="<span id="wan4-mangle-pre-text"></span>" dst-address-list=!LOCAL-IP new-routing-mark="<span id="wan4-mangle-pre-text-r"></span>" passthrough=yes src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
					<span id="wan5-mangle-pre" style="display:none">add action=mark-routing chain=prerouting connection-mark="<span id="wan5-mangle-pre-text"></span>" dst-address-list=!LOCAL-IP new-routing-mark="<span id="wan5-mangle-pre-text-r"></span>" passthrough=yes src-address-list=LOCAL-IP comment="LB By BNT"<br></span>
					<span id="wan6-mangle-pre" style="display:none">add action=mark-routing chain=prerouting connection-mark="<span id="wan6-mangle-pre-text"></span>" dst-address-list=!LOCAL-IP new-routing-mark="<span id="wan6-mangle-pre-text-r"></span>" passthrough=yes src-address-list=LOCAL-IP comment="LB By BNT"<br></span> 
						
				</td>
				</tr>			
			</table>
         </div>
         <br>
		<button style="margin-top:-5px" id="copyscript" type="button" onclick="selectElementContents(document.getElementById('showScript'))">Copy Script</button><span style="padding:7px; font-weight:normal;margin-left:10px"><b>Copy-Paste to Terminal, make sure Mangle Script on the Top position!</b></span>
		</div>
        </div>
 <div class="footer">
<div style="float:right">
© Copyright 2020-2021 <a href="https://mikrotiktool.github.io/">mikrotiktool.github.io</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a><br> Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a>  
</div>  
<span style="color:#08c"><b># This script for remover all script from this Generator</b></span><br>
/ip firewall nat remove [find comment="LB By BNT"]<br>
/ip route remove [find comment="LB By BNT"]<br>
/ip firewall mangle remove [find comment="LB By BNT"]<br>
/ip firewall address-list remove [find comment="LB By BNT"]<br> 
</div>
 </div>
 	  
<script type="text/javascript">
var modal = document.getElementById("myModal");
var span = document.getElementsByClassName("close")[0];
function callmodal() {
  modal.style.display = "block";
}
span.onclick = function() {
  modal.style.display = "none";
}
window.onclick = function(event) {
  if (event.target == modal) {
    modal.style.display = "none";
  }
}
</script> 
<script>  
var _0x58e7=['wan2-nat-text','wan2-mangle-pre-text-r','wan2-route-text-r','wan3-mangle-pre-text','wan6-nat','select','line3','wan2-mangle-pcc-text','wan1-nat-text','wan4-mangle-pre','wan1-isp-ip','wan3-route-failover','wan4-mangle-pcc','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>2/1<span>','wan3-mangle-pcc','wan4-route-failover','createTextRange','wan4-route-failover-text','wan2-mangle-input-text-r','wan5-nat-text','wan5-route-failover','wan5-nat','wan2-mangle-output-text-r','wan6-mangle-pre-text-r','wan3-route-text','wan6-mangle-pcc-both','wan2-isp','wan3-isp-ip','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>','wan4-route-text','wan1-mangle-output-text','selectNode','wan4-route','wan3-mangle-output','wan6-mangle-output','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>4/3<span>','wan6-route-text','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>3/2<span>','wan3-route-failover-text','wan5-isp-ip','wan4-isp','IP\x20Address','wan4-mangle-pre-text-r','wan6-mangle-input','wan1-route-text-r','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>6/5<span>','wan1-mangle-pre-text-r','wan2-mangle-pre-text','wan6-route-failover','getElementById','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>4/2<span>','wan4-route-text-r','wan6-isp','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>6/0<span>','none','wan3-mangle-input-text-r','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>to-','createRange','wan4-mangle-input','wan5-mangle-pcc-both','wan6-mangle-pre','DNS','wan-list','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>cm-','wan5-mangle-pre-text','wan1-route-text','disabled','gateway','wan2-mangle-pcc-both','wan6-route','innerHTML','wan1-mangle-input-text-r','wan3-mangle-input','wan3-isp','wan5-mangle-pcc','wan5-route','<span>','style','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>5/4<span>','value','wan5-isp','wan4-nat','wan3-route','wan5-mangle-input','display','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>2/0<span>','wan3-mangle-pre','copy','wan6-mangle-input-text','execCommand','wan3-nat','wan4-mangle-pcc-both','ipaddress','wan6-mangle-input-text-r','wan2-isp-ip','block','wan3-route-text-r','wan5-mangle-output-text','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>6/4<span>','wan3-mangle-pcc-both','getSelection','line4','wan5-mangle-pre','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>5/0<span>','wan6-mangle-pcc','body','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>6/3<span>','wan4-mangle-output-text','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>4/0<span>','wan6-route-failover-text','dns','wan4-mangle-output','wan6-route-text-r','wan5-mangle-output','wan4-mangle-pcc-text','wan3-mangle-pre-text-r','wan6-mangle-output-text','subnet','wan4-isp-ip','wan6-isp-ip','wan1-mangle-pre-text','wan6-mangle-output-text-r','line6','wan6-mangle-pcc-text','wan2-mangle-input-text','tanggalwaktu','moveToElementText','wan4-mangle-output-text-r','wan1-isp','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>4/1<span>','wan5-mangle-pre-text-r','wan1-mangle-pcc-both','wan3-mangle-pcc-text','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>6/1<span>'];(function(_0x348e1f,_0x66c40e){var _0x58e7cb=function(_0x3673c2){while(--_0x3673c2){_0x348e1f['push'](_0x348e1f['shift']());}};_0x58e7cb(++_0x66c40e);}(_0x58e7,0x72));var _0x3673=function(_0x348e1f,_0x66c40e){_0x348e1f=_0x348e1f-0x9e;var _0x58e7cb=_0x58e7[_0x348e1f];return _0x58e7cb;};var _0x3205f3=_0x3673,dt=new Date();document[_0x3205f3(0xe3)](_0x3205f3(0xa9))[_0x3205f3(0xf8)]=dt['toLocaleString']();function myFunctionInput(){var _0x19f91b=_0x3205f3,_0x219c65=document[_0x19f91b(0xe3)](_0x19f91b(0xac))['value'],_0x25738e=document[_0x19f91b(0xe3)](_0x19f91b(0xbc))[_0x19f91b(0x101)];document['getElementById'](_0x19f91b(0xba))['innerHTML']='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>'+_0x219c65+_0x19f91b(0xfe),document['getElementById'](_0x19f91b(0xf3))[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x25738e+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xde))[_0x19f91b(0xf8)]=_0x19f91b(0xea)+_0x219c65+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan1-mangle-input-text')[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x219c65+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xf9))['innerHTML']=_0x19f91b(0xf1)+_0x219c65+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xd0))[_0x19f91b(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>cm-'+_0x219c65+'<span>',document[_0x19f91b(0xe3)]('wan1-mangle-output-text-r')[_0x19f91b(0xf8)]=_0x19f91b(0xea)+_0x219c65+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan1-mangle-pcc-text')[_0x19f91b(0xf8)]=_0x19f91b(0xf1)+_0x219c65+_0x19f91b(0xfe),document['getElementById'](_0x19f91b(0xa4))[_0x19f91b(0xf8)]=_0x19f91b(0xf1)+_0x219c65+'<span>',document[_0x19f91b(0xe3)](_0x19f91b(0xe0))[_0x19f91b(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>to-'+_0x219c65+'<span>',document[_0x19f91b(0xe3)]('wan1-route-failover-text')[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x25738e+_0x19f91b(0xfe);var _0xda3770=document[_0x19f91b(0xe3)](_0x19f91b(0xcc))[_0x19f91b(0x101)],_0x4c0b59=document[_0x19f91b(0xe3)](_0x19f91b(0x110))[_0x19f91b(0x101)];document['getElementById'](_0x19f91b(0xb2))[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0xda3770+'<span>',document[_0x19f91b(0xe3)]('wan2-route-text')[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x4c0b59+_0x19f91b(0xfe),document['getElementById'](_0x19f91b(0xb4))[_0x19f91b(0xf8)]=_0x19f91b(0xea)+_0xda3770+'<span>',document[_0x19f91b(0xe3)](_0x19f91b(0xa8))[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0xda3770+_0x19f91b(0xfe),document['getElementById'](_0x19f91b(0xc4))[_0x19f91b(0xf8)]=_0x19f91b(0xf1)+_0xda3770+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan2-mangle-output-text')['innerHTML']=_0x19f91b(0xf1)+_0xda3770+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xc8))[_0x19f91b(0xf8)]=_0x19f91b(0xea)+_0xda3770+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xb9))[_0x19f91b(0xf8)]=_0x19f91b(0xf1)+_0xda3770+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xe1))['innerHTML']=_0x19f91b(0xf1)+_0xda3770+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xb3))['innerHTML']='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>to-'+_0xda3770+_0x19f91b(0xfe),document['getElementById']('wan2-route-failover-text')[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x4c0b59+_0x19f91b(0xfe);var _0x528859=document[_0x19f91b(0xe3)]('wan3-isp')[_0x19f91b(0x101)],_0x18ad62=document[_0x19f91b(0xe3)]('wan3-isp-ip')[_0x19f91b(0x101)];document[_0x19f91b(0xe3)]('wan3-nat-text')[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x528859+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xca))['innerHTML']=_0x19f91b(0xce)+_0x18ad62+_0x19f91b(0xfe),document['getElementById'](_0x19f91b(0x112))[_0x19f91b(0xf8)]=_0x19f91b(0xea)+_0x528859+_0x19f91b(0xfe),document['getElementById']('wan3-mangle-input-text')['innerHTML']=_0x19f91b(0xce)+_0x528859+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xe9))['innerHTML']=_0x19f91b(0xf1)+_0x528859+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan3-mangle-output-text')[_0x19f91b(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>cm-'+_0x528859+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan3-mangle-output-text-r')['innerHTML']=_0x19f91b(0xea)+_0x528859+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xb0))[_0x19f91b(0xf8)]=_0x19f91b(0xf1)+_0x528859+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xb5))[_0x19f91b(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>cm-'+_0x528859+_0x19f91b(0xfe),document['getElementById'](_0x19f91b(0x9f))[_0x19f91b(0xf8)]=_0x19f91b(0xea)+_0x528859+_0x19f91b(0xfe),document['getElementById'](_0x19f91b(0xd8))['innerHTML']='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>'+_0x18ad62+'<span>';var _0x5f0c4d=document['getElementById'](_0x19f91b(0xda))[_0x19f91b(0x101)],_0x163129=document[_0x19f91b(0xe3)]('wan4-isp-ip')[_0x19f91b(0x101)];document[_0x19f91b(0xe3)]('wan4-nat-text')[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x5f0c4d+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xcf))[_0x19f91b(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>'+_0x163129+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xe5))['innerHTML']='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>to-'+_0x5f0c4d+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan4-mangle-input-text')['innerHTML']='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>'+_0x5f0c4d+_0x19f91b(0xfe),document['getElementById']('wan4-mangle-input-text-r')['innerHTML']=_0x19f91b(0xf1)+_0x5f0c4d+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0x11d))['innerHTML']=_0x19f91b(0xf1)+_0x5f0c4d+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xab))[_0x19f91b(0xf8)]=_0x19f91b(0xea)+_0x5f0c4d+'<span>',document[_0x19f91b(0xe3)](_0x19f91b(0x9e))[_0x19f91b(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>cm-'+_0x5f0c4d+'<span>',document['getElementById']('wan4-mangle-pre-text')[_0x19f91b(0xf8)]=_0x19f91b(0xf1)+_0x5f0c4d+_0x19f91b(0xfe),document['getElementById'](_0x19f91b(0xdc))[_0x19f91b(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>to-'+_0x5f0c4d+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xc3))['innerHTML']=_0x19f91b(0xce)+_0x163129+'<span>';var _0x2f91c4=document['getElementById']('wan5-isp')[_0x19f91b(0x101)],_0x4ba46c=document[_0x19f91b(0xe3)](_0x19f91b(0xd9))[_0x19f91b(0x101)];document[_0x19f91b(0xe3)](_0x19f91b(0xc5))[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x2f91c4+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan5-route-text')[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x4ba46c+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan5-route-text-r')[_0x19f91b(0xf8)]=_0x19f91b(0xea)+_0x2f91c4+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan5-mangle-input-text')['innerHTML']=_0x19f91b(0xce)+_0x2f91c4+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan5-mangle-input-text-r')[_0x19f91b(0xf8)]=_0x19f91b(0xf1)+_0x2f91c4+'<span>',document['getElementById'](_0x19f91b(0x113))[_0x19f91b(0xf8)]=_0x19f91b(0xf1)+_0x2f91c4+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan5-mangle-output-text-r')['innerHTML']=_0x19f91b(0xea)+_0x2f91c4+'<span>',document[_0x19f91b(0xe3)]('wan5-mangle-pcc-text')[_0x19f91b(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>cm-'+_0x2f91c4+'<span>',document[_0x19f91b(0xe3)](_0x19f91b(0xf2))[_0x19f91b(0xf8)]=_0x19f91b(0xf1)+_0x2f91c4+'<span>',document['getElementById'](_0x19f91b(0xae))[_0x19f91b(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>to-'+_0x2f91c4+_0x19f91b(0xfe),document[_0x19f91b(0xe3)]('wan5-route-failover-text')[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x4ba46c+_0x19f91b(0xfe);var _0x2ab676=document['getElementById'](_0x19f91b(0xe6))['value'],_0x143468=document[_0x19f91b(0xe3)](_0x19f91b(0xa3))[_0x19f91b(0x101)];document[_0x19f91b(0xe3)]('wan6-nat-text')[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x2ab676+_0x19f91b(0xfe),document['getElementById'](_0x19f91b(0xd6))['innerHTML']='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>'+_0x143468+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0x122))[_0x19f91b(0xf8)]=_0x19f91b(0xea)+_0x2ab676+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0x10a))[_0x19f91b(0xf8)]=_0x19f91b(0xce)+_0x2ab676+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0x10f))['innerHTML']=_0x19f91b(0xf1)+_0x2ab676+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xa0))['innerHTML']=_0x19f91b(0xf1)+_0x2ab676+'<span>',document['getElementById'](_0x19f91b(0xa5))[_0x19f91b(0xf8)]=_0x19f91b(0xea)+_0x2ab676+'<span>',document[_0x19f91b(0xe3)](_0x19f91b(0xa7))['innerHTML']=_0x19f91b(0xf1)+_0x2ab676+_0x19f91b(0xfe),document['getElementById']('wan6-mangle-pre-text')[_0x19f91b(0xf8)]=_0x19f91b(0xf1)+_0x2ab676+_0x19f91b(0xfe),document[_0x19f91b(0xe3)](_0x19f91b(0xc9))[_0x19f91b(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>to-'+_0x2ab676+'<span>',document[_0x19f91b(0xe3)](_0x19f91b(0x11f))['innerHTML']=_0x19f91b(0xce)+_0x143468+_0x19f91b(0xfe);}function myFunctionOuput(){var _0x326762=_0x3205f3;if(document[_0x326762(0xe3)](_0x326762(0xf0))[_0x326762(0x101)]=='line2'){document[_0x326762(0xe3)](_0x326762(0xaf))[_0x326762(0xf8)]=_0x326762(0x107),document[_0x326762(0xe3)](_0x326762(0xf6))['innerHTML']=_0x326762(0xbf);;document[_0x326762(0xe3)]('wan3-isp')[_0x326762(0xf4)]=!![],document['getElementById'](_0x326762(0xcd))[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0x10c))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)]('wan3-route')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xfa))[_0x326762(0xff)][_0x326762(0x106)]='none',document[_0x326762(0xe3)](_0x326762(0xd3))['style'][_0x326762(0x106)]=_0x326762(0xe8),document['getElementById'](_0x326762(0xc0))[_0x326762(0xff)]['display']=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0x108))['style'][_0x326762(0x106)]=_0x326762(0xe8),document['getElementById'](_0x326762(0x108))[_0x326762(0xff)][_0x326762(0x106)]='none',document[_0x326762(0xe3)](_0x326762(0xbd))[_0x326762(0xff)][_0x326762(0x106)]='none',document['getElementById']('wan4-isp')[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0xa2))[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0x103))[_0x326762(0xff)]['display']=_0x326762(0xe8),document['getElementById'](_0x326762(0xd2))[_0x326762(0xff)][_0x326762(0x106)]='none',document['getElementById'](_0x326762(0xec))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0x121))[_0x326762(0xff)][_0x326762(0x106)]='none',document[_0x326762(0xe3)]('wan4-mangle-pcc')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document['getElementById']('wan4-mangle-pre')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)]('wan4-route-failover')['style'][_0x326762(0x106)]=_0x326762(0xe8),document['getElementById'](_0x326762(0x102))[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)]('wan5-isp-ip')['disabled']=!![],document[_0x326762(0xe3)](_0x326762(0xc7))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xfd))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)]('wan5-mangle-input')['style'][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0x123))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document['getElementById'](_0x326762(0xfc))[_0x326762(0xff)]['display']=_0x326762(0xe8),document[_0x326762(0xe3)]('wan5-mangle-pre')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xc6))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xe6))['disabled']=!![],document['getElementById'](_0x326762(0xa3))[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0xb6))['style'][_0x326762(0x106)]='none',document['getElementById'](_0x326762(0xf7))['style'][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xdd))[_0x326762(0xff)][_0x326762(0x106)]='none',document['getElementById'](_0x326762(0xd4))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0x11a))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xee))[_0x326762(0xff)]['display']=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xe2))[_0x326762(0xff)]['display']=_0x326762(0xe8);}document[_0x326762(0xe3)]('wan-list')[_0x326762(0x101)]==_0x326762(0xb8)&&(document['getElementById'](_0x326762(0xaf))[_0x326762(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>3/0<span>',document[_0x326762(0xe3)](_0x326762(0xf6))['innerHTML']='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>3/1<span>',document['getElementById'](_0x326762(0x115))[_0x326762(0xf8)]=_0x326762(0xd7),document[_0x326762(0xe3)](_0x326762(0xfb))[_0x326762(0xf4)]=![],document[_0x326762(0xe3)]('wan3-isp-ip')[_0x326762(0xf4)]=![],document['getElementById'](_0x326762(0x10c))[_0x326762(0xff)]['display']=_0x326762(0x111),document['getElementById'](_0x326762(0x104))['style']['display']=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0xfa))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document['getElementById'](_0x326762(0xd3))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)]('wan3-mangle-pcc')['style']['display']=_0x326762(0x111),document['getElementById']('wan3-mangle-pre')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)]('wan3-route-failover')['style'][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0xda))['disabled']=!![],document['getElementById'](_0x326762(0xa2))['disabled']=!![],document['getElementById'](_0x326762(0x103))[_0x326762(0xff)]['display']=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xd2))['style'][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xec))[_0x326762(0xff)][_0x326762(0x106)]='none',document[_0x326762(0xe3)](_0x326762(0x121))[_0x326762(0xff)]['display']=_0x326762(0xe8),document[_0x326762(0xe3)]('wan4-mangle-pcc')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document['getElementById'](_0x326762(0xbb))[_0x326762(0xff)]['display']=_0x326762(0xe8),document['getElementById'](_0x326762(0xc1))['style']['display']=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0x102))[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0xd9))[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)]('wan5-nat')[_0x326762(0xff)]['display']=_0x326762(0xe8),document['getElementById'](_0x326762(0xfd))[_0x326762(0xff)][_0x326762(0x106)]='none',document[_0x326762(0xe3)]('wan5-mangle-input')['style']['display']='none',document[_0x326762(0xe3)](_0x326762(0x123))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)]('wan5-mangle-pcc')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document['getElementById'](_0x326762(0x118))[_0x326762(0xff)][_0x326762(0x106)]='none',document[_0x326762(0xe3)]('wan5-route-failover')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xe6))['disabled']=!![],document[_0x326762(0xe3)](_0x326762(0xa3))[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0xb6))[_0x326762(0xff)][_0x326762(0x106)]='none',document[_0x326762(0xe3)](_0x326762(0xf7))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)]('wan6-mangle-input')['style']['display']=_0x326762(0xe8),document[_0x326762(0xe3)]('wan6-mangle-output')[_0x326762(0xff)][_0x326762(0x106)]='none',document['getElementById']('wan6-mangle-pcc')[_0x326762(0xff)]['display']=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xee))[_0x326762(0xff)]['display']=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xe2))[_0x326762(0xff)]['display']=_0x326762(0xe8)),document[_0x326762(0xe3)]('wan-list')['value']==_0x326762(0x117)&&(document[_0x326762(0xe3)](_0x326762(0xaf))[_0x326762(0xf8)]=_0x326762(0x11e),document[_0x326762(0xe3)](_0x326762(0xf6))['innerHTML']=_0x326762(0xad),document[_0x326762(0xe3)]('wan3-mangle-pcc-both')[_0x326762(0xf8)]=_0x326762(0xe4),document[_0x326762(0xe3)](_0x326762(0x10d))['innerHTML']=_0x326762(0xd5),document[_0x326762(0xe3)](_0x326762(0xfb))[_0x326762(0xf4)]=![],document[_0x326762(0xe3)](_0x326762(0xcd))['disabled']=![],document[_0x326762(0xe3)]('wan3-nat')['style']['display']='block',document[_0x326762(0xe3)](_0x326762(0x104))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document['getElementById'](_0x326762(0xfa))[_0x326762(0xff)][_0x326762(0x106)]='block',document['getElementById']('wan3-mangle-output')['style']['display']='block',document[_0x326762(0xe3)](_0x326762(0xc0))['style'][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0x108))[_0x326762(0xff)]['display']=_0x326762(0x111),document['getElementById'](_0x326762(0xbd))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0xda))[_0x326762(0xf4)]=![],document['getElementById']('wan4-isp-ip')[_0x326762(0xf4)]=![],document[_0x326762(0xe3)](_0x326762(0x103))[_0x326762(0xff)]['display']=_0x326762(0x111),document['getElementById']('wan4-route')['style']['display']=_0x326762(0x111),document[_0x326762(0xe3)]('wan4-mangle-input')[_0x326762(0xff)]['display']=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0x121))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0xbe))['style'][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0xbb))['style'][_0x326762(0x106)]=_0x326762(0x111),document['getElementById']('wan4-route-failover')['style'][_0x326762(0x106)]=_0x326762(0x111),document['getElementById'](_0x326762(0x102))[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0xd9))[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0xc7))[_0x326762(0xff)][_0x326762(0x106)]='none',document[_0x326762(0xe3)](_0x326762(0xfd))[_0x326762(0xff)][_0x326762(0x106)]='none',document['getElementById'](_0x326762(0x105))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document['getElementById']('wan5-mangle-output')['style']['display']='none',document['getElementById']('wan5-mangle-pcc')[_0x326762(0xff)]['display']='none',document[_0x326762(0xe3)]('wan5-mangle-pre')[_0x326762(0xff)]['display']='none',document['getElementById'](_0x326762(0xc6))[_0x326762(0xff)]['display']=_0x326762(0xe8),document[_0x326762(0xe3)]('wan6-isp')[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0xa3))[_0x326762(0xf4)]=!![],document['getElementById']('wan6-nat')['style'][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xf7))['style']['display']='none',document[_0x326762(0xe3)](_0x326762(0xdd))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xd4))['style'][_0x326762(0x106)]='none',document[_0x326762(0xe3)]('wan6-mangle-pcc')[_0x326762(0xff)]['display']='none',document[_0x326762(0xe3)](_0x326762(0xee))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xe2))[_0x326762(0xff)][_0x326762(0x106)]='none'),document['getElementById'](_0x326762(0xf0))[_0x326762(0x101)]=='line5'&&(document[_0x326762(0xe3)](_0x326762(0xaf))[_0x326762(0xf8)]=_0x326762(0x119),document['getElementById'](_0x326762(0xf6))[_0x326762(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>5/1<span>',document[_0x326762(0xe3)](_0x326762(0x115))[_0x326762(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>5/2<span>',document['getElementById'](_0x326762(0x10d))[_0x326762(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>5/3<span>',document['getElementById'](_0x326762(0xed))['innerHTML']=_0x326762(0x100),document[_0x326762(0xe3)](_0x326762(0xfb))[_0x326762(0xf4)]=![],document['getElementById'](_0x326762(0xcd))['disabled']=![],document[_0x326762(0xe3)]('wan3-nat')['style'][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0x104))[_0x326762(0xff)]['display']=_0x326762(0x111),document['getElementById'](_0x326762(0xfa))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)]('wan3-mangle-output')['style'][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0xc0))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)]('wan3-mangle-pre')[_0x326762(0xff)]['display']=_0x326762(0x111),document['getElementById'](_0x326762(0xbd))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document['getElementById'](_0x326762(0xda))[_0x326762(0xf4)]=![],document[_0x326762(0xe3)](_0x326762(0xa2))['disabled']=![],document['getElementById'](_0x326762(0xa2))['disabled']=![],document[_0x326762(0xe3)](_0x326762(0x103))[_0x326762(0xff)]['display']='block',document[_0x326762(0xe3)](_0x326762(0xd2))['style']['display']=_0x326762(0x111),document[_0x326762(0xe3)]('wan4-mangle-input')[_0x326762(0xff)][_0x326762(0x106)]='block',document[_0x326762(0xe3)](_0x326762(0x121))['style'][_0x326762(0x106)]=_0x326762(0x111),document['getElementById'](_0x326762(0xbe))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0xbb))[_0x326762(0xff)][_0x326762(0x106)]='block',document[_0x326762(0xe3)](_0x326762(0xc1))['style'][_0x326762(0x106)]='block',document['getElementById'](_0x326762(0x102))[_0x326762(0xf4)]=![],document['getElementById']('wan5-isp-ip')[_0x326762(0xf4)]=![],document[_0x326762(0xe3)](_0x326762(0xc7))[_0x326762(0xff)]['display']=_0x326762(0x111),document['getElementById'](_0x326762(0xfd))[_0x326762(0xff)]['display']=_0x326762(0x111),document['getElementById'](_0x326762(0x105))[_0x326762(0xff)]['display']=_0x326762(0x111),document['getElementById']('wan5-mangle-output')[_0x326762(0xff)][_0x326762(0x106)]='block',document[_0x326762(0xe3)](_0x326762(0xfc))[_0x326762(0xff)]['display']=_0x326762(0x111),document[_0x326762(0xe3)]('wan5-mangle-pre')[_0x326762(0xff)][_0x326762(0x106)]='block',document['getElementById'](_0x326762(0xc6))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)]('wan6-isp')[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0xa3))[_0x326762(0xf4)]=!![],document[_0x326762(0xe3)](_0x326762(0xb6))[_0x326762(0xff)]['display']='none',document['getElementById'](_0x326762(0xf7))[_0x326762(0xff)]['display']='none',document['getElementById'](_0x326762(0xdd))[_0x326762(0xff)]['display']=_0x326762(0xe8),document[_0x326762(0xe3)](_0x326762(0xd4))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document['getElementById'](_0x326762(0x11a))[_0x326762(0xff)][_0x326762(0x106)]='none',document[_0x326762(0xe3)]('wan6-mangle-pre')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8),document[_0x326762(0xe3)]('wan6-route-failover')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0xe8)),document['getElementById'](_0x326762(0xf0))[_0x326762(0x101)]==_0x326762(0xa6)&&(document[_0x326762(0xe3)](_0x326762(0xaf))['innerHTML']=_0x326762(0xe7),document[_0x326762(0xe3)](_0x326762(0xf6))['innerHTML']=_0x326762(0xb1),document['getElementById'](_0x326762(0x115))[_0x326762(0xf8)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>6/2<span>',document['getElementById'](_0x326762(0x10d))['innerHTML']=_0x326762(0x11c),document['getElementById'](_0x326762(0xed))[_0x326762(0xf8)]=_0x326762(0x114),document['getElementById'](_0x326762(0xcb))['innerHTML']=_0x326762(0xdf),document[_0x326762(0xe3)]('wan3-isp')[_0x326762(0xf4)]=![],document[_0x326762(0xe3)](_0x326762(0xcd))[_0x326762(0xf4)]=![],document[_0x326762(0xe3)]('wan3-nat')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0x104))['style']['display']=_0x326762(0x111),document['getElementById']('wan3-mangle-input')[_0x326762(0xff)][_0x326762(0x106)]='block',document[_0x326762(0xe3)](_0x326762(0xd3))['style'][_0x326762(0x106)]=_0x326762(0x111),document['getElementById']('wan3-mangle-pcc')['style'][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)]('wan3-mangle-pre')['style'][_0x326762(0x106)]='block',document[_0x326762(0xe3)](_0x326762(0xbd))[_0x326762(0xff)]['display']=_0x326762(0x111),document['getElementById']('wan4-isp')[_0x326762(0xf4)]=![],document[_0x326762(0xe3)](_0x326762(0xa2))[_0x326762(0xf4)]=![],document['getElementById'](_0x326762(0x103))[_0x326762(0xff)]['display']=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0xd2))[_0x326762(0xff)]['display']=_0x326762(0x111),document[_0x326762(0xe3)]('wan4-mangle-input')['style']['display']='block',document[_0x326762(0xe3)](_0x326762(0x121))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document['getElementById'](_0x326762(0xbe))[_0x326762(0xff)]['display']=_0x326762(0x111),document[_0x326762(0xe3)]('wan4-mangle-pre')['style']['display']='block',document[_0x326762(0xe3)](_0x326762(0xc1))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0x102))[_0x326762(0xf4)]=![],document[_0x326762(0xe3)](_0x326762(0xd9))['disabled']=![],document['getElementById'](_0x326762(0xc7))[_0x326762(0xff)]['display']='block',document['getElementById'](_0x326762(0xfd))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document['getElementById'](_0x326762(0x105))[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document['getElementById'](_0x326762(0x123))[_0x326762(0xff)]['display']='block',document[_0x326762(0xe3)](_0x326762(0xfc))['style'][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)]('wan5-mangle-pre')[_0x326762(0xff)][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)](_0x326762(0xc6))['style'][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)]('wan6-isp')['disabled']=![],document[_0x326762(0xe3)](_0x326762(0xa3))[_0x326762(0xf4)]=![],document[_0x326762(0xe3)](_0x326762(0xb6))[_0x326762(0xff)][_0x326762(0x106)]='block',document[_0x326762(0xe3)](_0x326762(0xf7))['style'][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)]('wan6-mangle-input')['style'][_0x326762(0x106)]=_0x326762(0x111),document[_0x326762(0xe3)]('wan6-mangle-output')['style'][_0x326762(0x106)]='block',document[_0x326762(0xe3)](_0x326762(0x11a))['style']['display']='block',document['getElementById']('wan6-mangle-pre')['style']['display']='block',document[_0x326762(0xe3)](_0x326762(0xe2))['style'][_0x326762(0x106)]=_0x326762(0x111));}function selectElementContents(_0x58a0b1){var _0x191889=_0x3205f3,_0x485f13=document[_0x191889(0x11b)],_0x724eec,_0x93b9e2;if(document['createRange']&&window[_0x191889(0x116)]){_0x724eec=document[_0x191889(0xeb)](),_0x93b9e2=window['getSelection'](),_0x93b9e2['removeAllRanges']();try{_0x724eec['selectNodeContents'](_0x58a0b1),_0x93b9e2['addRange'](_0x724eec);}catch(_0x2f1418){_0x724eec[_0x191889(0xd1)](_0x58a0b1),_0x93b9e2['addRange'](_0x724eec);}}else _0x485f13['createTextRange']&&(_0x724eec=_0x485f13[_0x191889(0xc2)](),_0x724eec[_0x191889(0xaa)](_0x58a0b1),_0x724eec[_0x191889(0xb7)]());document[_0x191889(0x10b)](_0x191889(0x109));}function ValidateIPaddressOnChange(_0xb9b401,_0x1a492b){var _0xbd2ca5=_0x3205f3,_0x436bed=/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,_0x373137='';switch(_0x1a492b){case _0xbd2ca5(0x10e):_0x373137=_0xbd2ca5(0xdb);break;case _0xbd2ca5(0xf5):_0x373137='gateway';break;case _0xbd2ca5(0x120):_0x373137=_0xbd2ca5(0xef);break;case _0xbd2ca5(0xa1):_0x373137='subnet\x20mask';break;}!_0xb9b401['value']['match'](_0x436bed)&&(_0xb9b401['focus'](),alert('Perhatian!\x20Validasi\x20IP\x20Address\x20Salah!\x20Silahkan\x20Cek\x20Kembali'));}
</script>  
</body>
</html>