<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Improved Netwatch - MikroTik Script RouterOS</title>
<meta content='Improved Netwatch - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Improved Netwatch - MikroTik Script RouterOS</h1>
<pre>This is an improved version of Netwatch that removes false positives. It works by pinging the target IP 5 times. If all 5 time out then the specified action is taken.

<code class="routeros">:local i 0; {:do {:set i ($i + 1)} while (($i < 5) && ([/ping ************* interval=3 count=1]=0))};
:if ($i=5 && [/ip route get [find comment="Default Route"] disabled]=false) do={:log info "Main Gateway down"; 
 /ip route set [find comment="Default Route"] disabled=yes}</code>
In this case it searches for a route labelled "Default Route" and disables it if 5 pings in a row to www.google.com time out. To re-enable on 5 succesfull pings you could have some thing like:

<code class="routeros">:local i 0; {:do {:set i ($i + 1)} while (($i < 5) && ([/ping ************* interval=3 count=1]=1))}; 
:if ($i=5 && [/ip route get [find comment="Default Route"] disabled]=true) do={:log info "Main Gateway up"; 
/ip route set [find comment="Default Route"] disabled=no}</code>
ping return number of recieved packets (at last on latest 3.x and щт 4.x). Next do same

<code class="routeros">:if ([/ping ************* interval=3 count=5]=5 && [/ip route get [find comment="Default Route"] disabled]=false) do={
:log info "Main Gateway down"; /ip route set [find comment="Default Route"] disabled=yes}</code>
Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
