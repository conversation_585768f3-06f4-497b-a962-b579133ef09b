<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>UPnP: Redirect The Port To Mikrotik Device - MikroTik Script RouterOS</title>
<meta content='UPnP: Redirect The Port To Mikrotik Device - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>UPnP: Redirect The Port To Mikrotik Device - MikroTik Script RouterOS</h1>
<pre>A common question for home users is How to redirect the port to MikroTik device, for the games and programs to work correctly.

The easiest option is to enable UPnP for devices on your home network. Most programs and games have UPnP support, but in some (Torrent) it must be enabled in the settings.

Port Forwarding (MikroTik UPnP)
Enable UPnP support in MikroTik:

<code class="routeros">[IP] -> [UPnP] -> [UPnP Settings: Enabled, Show Dummy Rules]</code>
In the UPnP settings window, specify the external and internal interfaces of the router.

After enabling UPnP and the Show Dummy Rules item in the Firewall window, you will see the automatic port forwarding rules.

How to forward port on your router using UPnP
Port forwarding (MikroTik Firewall)
An example is shown for the Torrent client:

Torrent port: 50237;
Torrent protocol: UDP;
Computer IP address: ***********;
Incoming interface (Internet): beeline;
Internal interface (local network): bridge1-lan.
Create a Firewall rule manually:

<code class="routeros">[IP] -> [Firewall] -> [NAT] -> [+] -> [General: Chain=dstnat, Protocol=17(udp), Dst.Port=50237, In.Interface=beeline] -> [Action: Action=netmap, To Addresses=***********, Port=50237]</code>
Let’s create a Firewall rule via the terminal:

<code class="routeros">/ip firewall nat add chain=dstnat protocol=udp dst-port=50237 in-interface=beeline action=netmap to-addresses=*********** to-ports=50237</code>
How to forward port on your router  using Firewall rules

How to set up port forwarding on a MikroTik router was discussed in this article. I hope now setting up port forwarding will not be difficult and programs and games will work as expected, use UPnP if possible. However, if you run into any problems while setting up, feel free to write in the comments. I will try to help.

Credit: <a href="https://mhelp.pro/mikrotik-how-to-forward-port-on-router/">https://mhelp.pro/mikrotik-how-to-forward-port-on-router/</a>
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>


