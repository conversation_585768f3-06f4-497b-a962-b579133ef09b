<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Dynamic DNS Update Script for dynDNS - MikroTik Script RouterOS</title>
<meta content='Dynamic DNS Update Script for dynDNS - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Dynamic DNS Update Script for dynDNS - MikroTik Script RouterOS</h1>
<pre>in order to support dyndns i took some parts from the changeip script and made it work for.

1) add a script called dynDNS:

<code class="routeros"># Define User Variables
:global ddnsuser "DYNDNSUSER"
:global ddnspass "DYNDNSPASS"
:global ddnshost "DYNDNSHOST"

# Define Global Variables
:global ddnsip
:global ddnslastip
:if ([ :typeof $ddnslastip ] = nil ) do={ :global ddnslastip "0" }

:global ddnsinterface
:global ddnssystem ("mt-" . [/system package get system version] )

# Define Local Variables
:local int

# Loop thru interfaces and look for ones containing
# default gateways without routing-marks
:foreach int in=[/ip route find dst-address=0.0.0.0/0 active=yes ] do={
  :if ([:typeof [/ip route get $int routing-mark ]] != str ) do={
     :global ddnsinterface [/ip route get $int interface]
  }
}

# Grab the current IP address on that interface.
:global ddnsip [ /ip address get [/ip address find interface=$ddnsinterface ] address ]

# Did we get an IP address to compare?
:if ([ :typeof $ddnsip ] = nil ) do={
   :log info ("DynDNS: No ip address present on " . $ddnsinterface . ", please check.")
} else={
  :if ($ddnsip != $ddnslastip) do={
    :log info "DynDNS: Sending UPDATE!"
    :local str "/nic/update?hostname=$ddnshost&myip=$ddnsip&wildcard=NOCHG&mx=NOCHG&backmx=NOCHG"
    /tool fetch address=members.dyndns.org src-path=$str mode=http user=$ddnsuser \
        password=$ddnspass dst-path=("/DynDNS.".$ddnshost)
    :delay 1
    :local str [/file find name="DynDNS.$ddnshost"];
    /file remove $str
    :global ddnslastip $ddnsip
  }
}</code>
2) Add a scheduler entry to run every minute

<code class="routeros">/system scheduler add name=dynDNS interval=00:01 on-event="/system script run dynDns\r"</code>
There is a thread for further questions.

Getting it to work with 4.XX

<code class="routeros"># Set needed variables
:local username "YourUsername"
:local password "YourPassword"
:local hostname "nohostset"
:global systemname [/system identity get name]

:if ($systemname  = "Site1" ) do= {
:set hostname "yourdomain1.dyndns.org"
}
:if ($systemname  = "Site2" ) do= {
:set hostname "yourdomain2.dyndns.org"
}
:if ($systemname  = "Site3" ) do= {
:set hostname "yourdomain3.dyndns.org"
}

:global dyndnsForce
:global previousIP

# print some debug info
:log info ("UpdateDynDNS: username = $username")
:log info ("UpdateDynDNS: password = $password")
:log info ("UpdateDynDNS: hostname = $hostname")
:log info ("UpdateDynDNS: previousIP = $previousIP")

# get the current IP address from the internet (in case of double-nat)
/tool fetch mode=http address="checkip.dyndns.org" src-path="/" dst-path="/dyndns.checkip.html"
:local result [/file get dyndns.checkip.html contents]

# parse the current IP result
:local resultLen [:len $result]
:local startLoc [:find $result ": " -1]
:set startLoc ($startLoc + 2)
:local endLoc [:find $result "</body>" -1]
:local currentIP [:pick $result $startLoc $endLoc]
:log info "UpdateDynDNS: currentIP = $currentIP"

# Remove the # on next line to force an update every single time - useful for debugging,
#  but you could end up getting blacklisted by DynDNS!

#:set dyndnsForce true

# Determine if dyndns update is needed
# more dyndns updater request details http://www.dyndns.com/developers/specs/syntax.html
:if (($currentIP != $previousIP) || ($dyndnsForce = true)) do={
    :set dyndnsForce false
    :set previousIP $currentIP
    /tool fetch user=$username password=$password mode=http address="members.dyndns.org" \
       src-path="/nic/update?hostname=$hostname&myip=$currentIP" dst-path="/dyndns.txt"
    :local result [/file get dyndns.txt contents]
    :log info ("UpdateDynDNS: Dyndns update needed")
    :log info ("UpdateDynDNS: Dyndns Update Result: ".$result)
    :put ("Dyndns Update Result: ".$result)
} else={
    :log info ("UpdateDynDNS: No dyndns update needed")
} </code>
If have problems to run these scripts, try it:

<code class="routeros">:local ddnsuser "your user name"
:local ddnspass "your password"
:local theinterface "device to renove IP"
:local ddnshost "yourhost.dyndns.org"
:local ipddns [:resolve $ddnshost];
:local ipfresh [ /ip address get [/ip address find interface=$theinterface ] address ]
:if ([ :typeof $ipfresh ] = nil ) do={
   :log info ("DynDNS: No ip address on $theinterface .")
} else={
   :for i from=( [:len $ipfresh] - 1) to=0 do={ 
      :if ( [:pick $ipfresh $i] = "/") do={ 
    :set ipfresh [:pick $ipfresh 0 $i];
      } 
}
 
:if ($ipddns != $ipfresh) do={
    :log info ("DynDNS: IP-DynDNS = $ipddns")
    :log info ("DynDNS: IP-Fresh = $ipfresh")
   :log info "DynDNS: Update IP needed, Sending UPDATE...!"
   :local str "/nic/update?hostname=$ddnshost&myip=$ipfresh&wildcard=NOCHG&mx=NOCHG&backmx=NOCHG"
   /tool fetch address=members.dyndns.org src-path=$str mode=http user=$ddnsuser \
         password=$ddnspass dst-path=("/DynDNS.".$ddnshost)
    :delay 1
    :local str [/file find name="DynDNS.$ddnshost"];
    /file remove $str
    :global ipddns $ipfresh
  :log info "DynDNS: IP updated to $ipfresh!"
    } else={
     :log info "DynDNS: dont need changes";
    }
}  </code>
another version of this script working in 5.x

<code class="routeros"># Set needed variables
:local username "YOURUSER"
:local password "YOURPASWORD"
:local hostname "YOURHOSTNAME.dyndns.org"

:global dyndnsForce
:global previousIP 

# print some debug info
:log info ("UpdateDynDNS: username = $username")
:log info ("UpdateDynDNS: password = $password")
:log info ("UpdateDynDNS: hostname = $hostname")
:log info ("UpdateDynDNS: previousIP = $previousIP")

# get the current IP address from the internet (in case of double-nat)
/tool fetch mode=http address="checkip.dyndns.org" src-path="/" dst-path="/dyndns.checkip.html"
:delay 1
:local result [/file get dyndns.checkip.html contents]

# parse the current IP result
:local resultLen [:len $result]
:local startLoc [:find $result ": " -1]
:set startLoc ($startLoc + 2)
:local endLoc [:find $result "</body>" -1]
:local currentIP [:pick $result $startLoc $endLoc]
:log info "UpdateDynDNS: currentIP = $currentIP"

# Remove the # on next line to force an update every single time - useful for debugging,
# but you could end up getting blacklisted by DynDNS!

#:set dyndnsForce true

# Determine if dyndns update is needed
# more dyndns updater request details http://www.dyndns.com/developers/specs/syntax.html

:if (($currentIP != $previousIP) || ($dyndnsForce = true)) do={
   :set dyndnsForce false
   :set previousIP $currentIP
   :log info "$currentIP or $previousIP"
   /tool fetch user=$username password=$password mode=http address="members.dyndns.org" \
      src-path="nic/update?system=dyndns&hostname=$hostname&myip=$currentIP&wildcard=no" \
      dst-path="/dyndns.txt"
   :delay 1
   :local result [/file get dyndns.txt contents]
   :log info ("UpdateDynDNS: Dyndns update needed")
   :log info ("UpdateDynDNS: Dyndns Update Result: ".$result)
   :put ("Dyndns Update Result: ".$result)
} else={
   :log info ("UpdateDynDNS: No dyndns update needed")
} </code>
Debugged version for RoS 6.x:

<code class="routeros">:global ddnsuser "theddnsusername"
:global ddnspass "theddnspassword"
:global theinterface "interfacename"
:global ddnshost blabla.dyndns.org
:global ipddns [:resolve $ddnshost];
:global ipfresh [ /ip address get [/ip address find interface=$theinterface ] address ]
:if ([ :typeof $ipfresh ] = nil ) do={
   :log info ("DynDNS: No ip address on $theinterface .")
} else={
   :for i from=( [:len $ipfresh] - 1) to=0 do={ 
      :if ( [:pick $ipfresh $i] = "/") do={ 
    :set ipfresh [:pick $ipfresh 0 $i];
      } 
}
 
:if ($ipddns != $ipfresh) do={
    :log info ("DynDNS: IP-DynDNS = $ipddns")
    :log info ("DynDNS: IP-Fresh = $ipfresh")
   :log info "DynDNS: Update IP needed, Sending UPDATE...!"
   :global str "/nic/update\?hostname=$ddnshost&myip=$ipfresh&wildcard=NOCHG&mx=NOCHG&backmx=NOCHG"
   /tool fetch address=members.dyndns.org src-path=$str mode=http user=$ddnsuser \
         password=$ddnspass dst-path=("/DynDNS.".$ddnshost)
    :delay 1
    :global str [/file find name="DynDNS.$ddnshost"];
    /file remove $str
    :global ipddns $ipfresh
  :log info "DynDNS: IP updated to $ipfresh!"
    } else={
     :log info "DynDNS: dont need changes";
    }
}  </code>
Scheduler:

<code class="routeros">/system scheduler
add interval=1m name=DynDns on-event=DynDns policy=ftp,reboot,read,write,policy,test,winbox,password,sniff,sensitive,api start-time=startup </code>
Credit: https://wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
