<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Load Balancing LB PCC 6 Line ISP Failover - Mikrotik Script RouterOS</title>
<meta content='Load Balancing LB PCC 6 Line ISP Failover - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script> 
</head>  
<body>
<div id="hidelink"></div>
<h1>Load Balancing LB PCC 6 Line ISP Failover - Mikrotik Script RouterOS</h1>
<pre>Load balance PCC 6 ISP on mikrotik is a technique for distributing traffic load on two or more connection lines in a balanced way, so that traffic can run optimally, maximize throughput, reduce response time and avoid overloading one of the connection lines.

<code class="routeros">################################################
# LOAD BALANCING (LB) PCC SCRIPT GENERATOR
# Date/Time: 2/13/2021, 9:19:38 PM
#  
# Load Balancing Metode -> PCC
################################################

/ip firewall address-list
add address=***********/16 list=LOCAL-IP
add address=**********/12 list=LOCAL-IP
add address=10.0.0.0/8 list=LOCAL-IP

/ip firewall nat
add chain=srcnat out-interface="ether1" action=masquerade
add chain=srcnat out-interface="ether2" action=masquerade
add chain=srcnat out-interface="ether3" action=masquerade
add chain=srcnat out-interface="ether4" action=masquerade
add chain=srcnat out-interface="ether5" action=masquerade
add chain=srcnat out-interface="ether6" action=masquerade

/ip route
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether1"
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether2"
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether3"
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether4"
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether5"
add check-gateway=ping distance=1 gateway="***********" routing-mark="to-ether6"
add check-gateway=ping distance=1 gateway="***********"
add check-gateway=ping distance=2 gateway="***********"
add check-gateway=ping distance=3 gateway="***********"
add check-gateway=ping distance=4 gateway="***********"
add check-gateway=ping distance=5 gateway="***********"
add check-gateway=ping distance=6 gateway="***********"

/ip firewall mangle
add action=mark-connection chain=input in-interface="ether1" new-connection-mark="cm-ether1" passthrough=yes
add action=mark-connection chain=input in-interface="ether2" new-connection-mark="cm-ether2" passthrough=yes
add action=mark-connection chain=input in-interface="ether3" new-connection-mark="cm-ether3" passthrough=yes
add action=mark-connection chain=input in-interface="ether4" new-connection-mark="cm-ether4" passthrough=yes
add action=mark-connection chain=input in-interface="ether5" new-connection-mark="cm-ether5" passthrough=yes
add action=mark-connection chain=input in-interface="ether6" new-connection-mark="cm-ether6" passthrough=yes
add action=mark-routing chain=output connection-mark="cm-ether1" new-routing-mark="to-ether1" passthrough=yes
add action=mark-routing chain=output connection-mark="cm-ether2" new-routing-mark="to-ether2" passthrough=yes
add action=mark-routing chain=output connection-mark="cm-ether3" new-routing-mark="to-ether3" passthrough=yes
add action=mark-routing chain=output connection-mark="cm-ether4" new-routing-mark="to-ether4" passthrough=yes
add action=mark-routing chain=output connection-mark="cm-ether5" new-routing-mark="to-ether5" passthrough=yes
add action=mark-routing chain=output connection-mark="cm-ether6" new-routing-mark="to-ether6" passthrough=yes
add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP dst-address-type=!local new-connection-mark="cm-ether1" passthrough=yes per-connection-classifier=both-addresses-and-ports:6/0 src-address-list=LOCAL-IP
add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP dst-address-type=!local new-connection-mark="cm-ether2" passthrough=yes per-connection-classifier=both-addresses-and-ports:6/1 src-address-list=LOCAL-IP
add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP dst-address-type=!local new-connection-mark="cm-ether3" passthrough=yes per-connection-classifier=both-addresses-and-ports:6/2 src-address-list=LOCAL-IP
add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP dst-address-type=!local new-connection-mark="cm-ether4" passthrough=yes per-connection-classifier=both-addresses-and-ports:6/3 src-address-list=LOCAL-IP
add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP dst-address-type=!local new-connection-mark="cm-ether5" passthrough=yes per-connection-classifier=both-addresses-and-ports:6/4 src-address-list=LOCAL-IP
add action=mark-connection chain=prerouting dst-address-list=!LOCAL-IP dst-address-type=!local new-connection-mark="cm-ether6" passthrough=yes per-connection-classifier=both-addresses-and-ports:6/5 src-address-list=LOCAL-IP
add action=mark-routing chain=prerouting connection-mark="cm-ether1" dst-address-list=!LOCAL-IP new-routing-mark="to-ether1" passthrough=yes src-address-list=LOCAL-IP
add action=mark-routing chain=prerouting connection-mark="cm-ether2" dst-address-list=!LOCAL-IP new-routing-mark="to-ether2" passthrough=yes src-address-list=LOCAL-IP
add action=mark-routing chain=prerouting connection-mark="cm-ether3" dst-address-list=!LOCAL-IP new-routing-mark="to-ether3" passthrough=yes src-address-list=LOCAL-IP
add action=mark-routing chain=prerouting connection-mark="cm-ether4" dst-address-list=!LOCAL-IP new-routing-mark="to-ether4" passthrough=yes src-address-list=LOCAL-IP
add action=mark-routing chain=prerouting connection-mark="cm-ether5" dst-address-list=!LOCAL-IP new-routing-mark="to-ether5" passthrough=yes src-address-list=LOCAL-IP
add action=mark-routing chain=prerouting connection-mark="cm-ether6" dst-address-list=!LOCAL-IP new-routing-mark="to-ether6" passthrough=yes src-address-list=LOCAL-IP</code>
Credit: https://www.o-om.com/2020/12/load-balancing-pcc-script-generator-for.html
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
