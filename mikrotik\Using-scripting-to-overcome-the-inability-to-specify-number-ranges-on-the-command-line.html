<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Using Scripting To Overcome The Inability To Specify Number Ranges On The Command Line - MikroTik Script RouterOS</title>
<meta content='Using Scripting To Overcome The Inability To Specify Number Ranges On The Command Line - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Using Scripting To Overcome The Inability To Specify Number Ranges On The Command Line - MikroTik Script RouterOS</h1>
<pre>Admittedly, I (mattx86) have not really bothered to request that number ranges be allowed on the command line for operations such as disable, remove, etc., and as we don't yet have it available as of RouterOS version 5.0rc11, I have devised a method in order to do so.

What am I talking about?

Say we have the following list (the extra IP addresses were created for demonstration purposes):

<code class="routeros">[admin@MikroTik] > /ip address print
Flags: X - disabled, I - invalid, D - dynamic
 #   ADDRESS            NETWORK         INTERFACE
 0   ***********/24     ***********     lan-bridge
 1   ***********/30     ***********     fast-ipip
 2 D xxx.xxx.xxx.xxx     xxx.xxx.xxx.xxx    dsl-pppoe
 3   ************/32    ************    lan-bridge
 4   ************/32    ************    lan-bridge
 5   ************/32    ************    lan-bridge
 6   ************/32    ************    lan-bridge</code>
Now let's say that you want to disable numbers 3-6.

Rather than doing:

<code class="routeros">/ip address disable 3,4,5,6</code>
Suppose you wanted to specify a range, such as:

<code class="routeros">/ip address disable 3-6</code>
Guess what? It won't work (at least at the time of this writing). Yes, I realize it's not a big deal to specify "3,4,5,6" in this case, but what if you have a bigger list?

Well, thankfully(?), I've devised a method that works:

<code class="routeros">Compact Version:
(for direct use on the command line)

:local from 3;:local to 6;:local i 0;:foreach id in=[/ip address find] do={:if ($i >= $from && $i <= $to) do={/ip address disable $id}; :set i ($i+1)}

Expanded Version:
(for use in scripts, and to explain it better)

:local from 3  #update this
:local to 6      #update this
:local i 0         #set to zero because my list starts at zero
:foreach id in=[/ip address find] do={
	:if ($i >= $from && $i <= $to) do={
		/ip address disable $id
	}
	:set i ($i+1)
}</code>
Just change the from and to variables, replace "/ip address" in the foreach line to whatever list it is you're dealing with and change the command in the if block to whatever command(s) you need to do. (You can even use the set operation, e.g.: /ip address set $id disabled=yes)

Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
