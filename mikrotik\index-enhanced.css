/* Enhanced Index Page Styles */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh;
    margin: 0 !important;
    padding: 20px !important;
}

#wrap {
    max-width: 1200px !important;
    min-width: 200px;
    margin: 0 auto !important;
    padding: 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.content {
    float: right;
    width: 64% !important;
    padding: 25px !important;
    background: rgba(255,255,255,0.95) !important;
    border-radius: 15px !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    backdrop-filter: blur(10px);
    min-height: 600px !important;
    margin-bottom: 20px;
    border: none !important;
}

.sidebar {
    overflow: hidden;
    float: left;
    width: 34% !important;
    padding: 25px !important;
    background: rgba(255,255,255,0.9) !important;
    border-radius: 15px !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    backdrop-filter: blur(10px);
    min-height: 600px !important;
    margin-right: 2%;
    margin-bottom: 20px;
    border: none !important;
}

.footer {
    font-size: 14px;
    padding: 25px !important;
    background: rgba(255,255,255,0.95) !important;
    color: #2c3e50 !important;
    clear: both;
    width: auto;
    text-align: center;
    border-radius: 15px !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    backdrop-filter: blur(10px);
    margin-top: 20px;
    border: none !important;
}

.footer a {
    color: #667eea !important;
    text-decoration: none !important;
    font-weight: 500;
}

/* Enhanced Menu Items */
.menu1 {
    width: 100% !important;
    margin-bottom: 3px !important;
    font-weight: bold;
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    padding: 15px !important;
    color: white !important;
    display: block !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3) !important;
    transition: all 0.3s ease;
    border: none !important;
}

.menu1:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4) !important;
}

.menu1 a:link, .menu1 a:visited {
    text-decoration: none !important;
    color: white !important;
    font-weight: bold;
}

.menu2 {
    width: 100% !important;
    margin-bottom: 3px !important;
    font-weight: bold;
    background: linear-gradient(45deg, #95a5a6, #7f8c8d) !important;
    padding: 15px !important;
    color: white !important;
    display: block !important;
    border-radius: 10px !important;
    box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3) !important;
    transition: all 0.3s ease;
    border: none !important;
}

.menu2:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4) !important;
}

.menu2 a:link, .menu2 a:visited {
    text-decoration: none !important;
    color: white !important;
    font-weight: bold;
}

/* Enhanced List Game */
.list-game {
    border-radius: 10px !important;
    height: 600px !important;
    background: white !important;
    overflow: auto !important;
    overflow-x: hidden;
    width: 100%;
    margin-top: 10px !important;
    padding: 15px !important;
    border: 2px solid #e0e0e0 !important;
    box-shadow: inset 0 2px 8px rgba(0,0,0,0.1) !important;
}

.list-mangle {
    height: 600px !important;
    background: #fff !important;
    width: 100%;
    padding: 20px !important;
    margin: 0;
    border: none !important;
    border-radius: 10px;
    box-shadow: inset 0 2px 8px rgba(0,0,0,0.1);
}

/* Enhanced Tables */
table {
    border-collapse: collapse !important;
    width: 100%;
    background: white;
    border-radius: 10px !important;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
}

tr {
    width: 100%;
    border: 1px solid #e0e0e0 !important;
    background: #f8f9fa !important;
    transition: background-color 0.3s ease;
}

tr:nth-child(even) {
    background: #fff !important;
    width: 100%;
}

tr:hover {
    background: #e3f2fd !important;
}

/* Enhanced Buttons */
button {
    color: #fff !important;
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 12px 20px !important;
    min-width: 100px !important;
    font-size: 16px !important;
    font-weight: bold !important;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

/* Enhanced Form Elements */
input[type=text], select, textarea {
    width: 100% !important;
    padding: 12px !important;
    border: 2px solid #ddd !important;
    border-radius: 8px !important;
    resize: vertical;
    font-weight: normal !important;
    margin-bottom: 10px;
    font-size: 16px !important;
    transition: border-color 0.3s ease;
}

input[type=text]:focus, select:focus, textarea:focus {
    outline: none !important;
    border-color: #667eea !important;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.2) !important;
}

input[type=submit] {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    padding: 12px 24px !important;
    border: none !important;
    border-radius: 8px !important;
    cursor: pointer;
    font-weight: bold !important;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3) !important;
}

input[type=submit]:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4) !important;
}

/* Enhanced Top Button */
#myBtn {
    display: none;
    position: fixed !important;
    bottom: 30px !important;
    right: 30px !important;
    z-index: 99 !important;
    font-size: 18px !important;
    border: none !important;
    outline: none !important;
    background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
    color: white !important;
    cursor: pointer;
    padding: 15px !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3) !important;
    transition: all 0.3s ease;
}

#myBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4) !important;
}

/* Welcome Section */
.welcome {
    width: 100% !important;
    height: auto !important;
    padding: 25px !important;
    background: rgba(255,255,255,0.95) !important;
    border-radius: 15px !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    backdrop-filter: blur(10px);
    margin-bottom: 20px;
}

.welcome a {
    color: #667eea !important;
    font-weight: 500;
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content, .sidebar, .footer, .welcome {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .content, .sidebar {
        float: none !important;
        width: 100% !important;
        margin-right: 0 !important;
        margin-bottom: 20px;
    }
    
    #wrap {
        padding: 10px !important;
    }
    
    .list-game {
        height: 400px !important;
    }
    
    .list-mangle {
        height: 400px !important;
    }
}
