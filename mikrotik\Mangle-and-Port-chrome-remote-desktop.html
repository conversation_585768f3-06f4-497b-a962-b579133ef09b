<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Mangle and Port Google Chrome Remote Desktop - MikroTik Script RouterOS</title>
<meta content='Mangle and Port Google Chrome Remote Desktop - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Mangle and Port Google Chrome Remote Desktop - MikroTik Script RouterOS</h1>
<pre>Chrome Remote Desktop. The easy way to remotely connect with your home or work computer, or share your screen with others. ... Built on Google's secure infrastructure using the latest open web technologies like WebRTC, you can connect to your devices or share your screen with confidence.

Port forward Chrome Remote Desktop:
TCP/UDP: 5222/5222

<code class="routeros">
/ip firewall mangle
add action=mark-connection chain=prerouting dst-port="5222" new-connection-mark=conn-GCDC protocol=tcp
add action=mark-connection chain=prerouting dst-port="5222" new-connection-mark=conn-GCDC protocol=udp
add action=mark-packet chain=prerouting connection-mark=conn-GCDC new-packet-mark=GCDC_pkt-up passthrough=no src-address=***********/24
add action=mark-packet chain=prerouting connection-mark=conn-GCDC dst-address=***********/24 new-packet-mark=GCDC_pkt-down passthrough=no</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>