<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Improved Netwatch 2 - MikroTik Script RouterOS</title>
<meta content='Improved Netwatch 2 - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Improved Netwatch 2 - MikroTik Script RouterOS</h1>
<pre>This setup now changes the distance number on the interfaces that is being used for gateways instead of disabling an interface. That way you could continue to monitor the unused interface to know whether it is actually up or down to the internet.

With these scripts you must use the scheduler to run them. I set mine to every 5 seconds. This will send 5 pings in a row out ether 1 and if all 5 fail it will increase the distance on that port to 3 instead of 1. To start this process you must set your distance on port 1 to 1. The other port that is being used as a failover gateway should be set to a distance of 2. By switching distance on the port instead of disabling it allows you to continue to ping out ether 1 until it pings all 5 times and at that point it will switch back to the main gateway.

script 1:

<code class="routeros">:local i 0; {:do {:set i ($i + 1)} while (($i < 5) && ([/ping ************* interval=3 count=1]=0))};
:if ($i=5 && [/ip route get [find comment="Default Route"] distance]=1) do={:log info "Main Gateway down"; 
/ip route set [find comment="Default Route"] distance=3}</code>
script 2:

<code class="routeros">:local i 0; {:do {:set i ($i + 1)} while (($i < 5) && ([/ping ************* interval=3 count=1]=1))}; 
:if ($i=5 && [/ip route get [find comment="Default Route"] distance]=3) do={:log info "Main Gateway up"; 
/ip route set [find comment="Default Route"] distance=1}</code>

Suggestion: A trick could be use ping with different sizes and have a different route for each. That would require creating a mangle rule based on packet size and place a routing mark on each.

Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
