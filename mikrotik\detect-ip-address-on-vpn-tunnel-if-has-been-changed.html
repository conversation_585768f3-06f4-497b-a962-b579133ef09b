<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Detect IP Address on VPN Tunnel if has been changed - MikroTik Script RouterOS</title>
<meta content='Detect IP Address on VPN Tunnel if has been changed - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Detect IP Address on VPN Tunnel if has been changed - MikroTik Script RouterOS</h1>
<pre>How to Detect IP Address on VPN Tunnel if has been changed?

I made this script for VPN Tunnel which suddenly changes the IP in the HostName / Domain name without confirmation first, meaning that sometimes they can change the IP at any time, actually it doesn't matter if we only use 1 WAN ISP but it will be a different story if we use 2 ISP and first select the VPN to one ISP line only.

You can use this script at starup on schedule and set duration for example 10 Minute (00:10:00)  

<code class="routeros">:global currentIP;
:local VpnHostName "my.tunel-vpn.net";
:local WanGatewayRouter "***********"; 
:local SetComment "MY-VPN-TUNNEL";

:local check [/ip route find where comment=$SetComment];
:local resolvedIP [:resolve $VpnHostName];

:if (check = "") do={
 /ip route add gateway=$WanGatewayRouter dst-address=$resolvedIP comment=$SetComment;
} else={
 :if ($resolvedIP != $currentIP) do={
 /ip route set [/ip route find where comment=$SetComment] dst-address=$resolvedIP gateway=$WanGatewayRouter;
 :set currentIP $resolvedIP;
 :log info "Detect New IP VPN TUNNEL = $resolvedIP"
 }
}</code>
Credit: https://www.o-om.com
</pre>
<br>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>




