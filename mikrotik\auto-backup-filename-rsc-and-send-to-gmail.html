<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Auto Backup Filename.rsc and Send To Gmail - MikroTik Script RouterOS</title>
<meta content='Auto Backup Filename.rsc and Send To Gmail - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Auto Backup Filename.rsc and Send To Gmail - MikroTik Script RouterOS</h1>
<pre>How To Auto Backup Filename.rsc And Send To Gmail

Setup Your mail first on <b>Tool > Email</b>

<code class="routeros">/tool e-mail set address=************** from=<EMAIL> password=xxxx port=587 start-tls=yes user=<EMAIL></code>
Input this script to Scheduler and you can set backup to gmail every week use interval like "7d 00:00:00"

<code class="routeros">#************************************************************
# BACKUP FILENAME.RSC AND SEND TO GMAIL 
#************************************************************
:global backupfilersc ([/system identity get name] . "-" . [/system clock get time])
/export file=$backupfilersc
:delay 30s
:log info "backup being emailed"
/tool e-mail send to="<EMAIL>" subject=([/system identity get name] . " - ". [/system clock get time] . " - " . [/system clock get date] . "") from=<EMAIL> file=("$backupfilersc" . ".rsc")
:delay 30s
/file remove ("$backupfilersc" . ".rsc")</code>
Credit: www.o-om.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>



