<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>GPS Text File Converter To Google Earth Or Maps - MikroTik Script RouterOS</title>
<meta content='GPS Text File Converter To Google Earth Or Maps - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>GPS Text File Converter To Google Earth Or Maps - MikroTik Script RouterOS</h1>
<pre>
Before you try this, insure your setup is complete in "/tool e-mail". The "server" entry must be the "to" email address email server. The "from" entry must be a valid email address. Try a test email, then check your email. It may take several minutes to get to your mailbox.

<code class="routeros">/tool email send to=<EMAIL> subject="Email Test" body="Email Test"</code>
Set the gps to output data to the file gps.txt.
Create an empty text file with a text editor, save as gps.kml, and upload to the router.
Go to "/system script" and add the script "gps", then enter the edit source mode.
Copy and paste the following code.
CHANGE THE EMAIL ADDRESS at the top of the code . This is the address to which the email will be sent.
Ctrl-o to save and exit.
Go to "/system schedule" and schedule the script "gps" to run at whatever interval is appropriate for your application. It sends email always if the position is not valid, and also when the position is valid and it changed from the last time the script was run.

<code class="routeros">:local email "<EMAIL>";
:global gpstext [/file get gps.txt contents];
:local longstart [:find $gpstext "longitude" -1];
:local longend [:find $gpstext "" $longstart];
:local latstart [:find $gpstext "latitude" -1];
:local latend [:find $gpstext "" $latstart];
:local validstart [:find $gpstext "valid" -1];
:local validend [:find $gpstext "" $validstart];
:local valid false;
:local zeros "";

:if ([:find $gpstext "yes" $validstart] > 0) do={:set valid true;};

:global longitude [:pick $gpstext ($longstart + 11) $longend];
:local degreestart [:find $longitude " " -1];
:local minutestart [:find $longitude " " $degreestart];
:local secondstart [:find $longitude "'" $minutestart];

:local secondend;
:local secfract;

:if ([:len [:find $longitude "." 0]] < 1) do={
    :set secondend [:find $longitude "'" $secondstart];
    :set secfract "0";
} else={
    :set secondend [:find $longitude "." $secondstart];
    :set secfract [:pick $longitude ($secondend + 1) ($secondend + 2)];
};

:local longdegree;
:local longdegreelink;

:if ([:pick $longitude 0 1] = "W") do={
    :set longdegree "-";
    :set longdegreelink "W";
} else={
    :set longdegree "+";
    :set longdegreelink "E";
};

:set longdegree ($longdegree . [:pick $longitude 2 $minutestart]);
:set longdegreelink ($longdegreelink . [:pick $longitude 2 $minutestart]);
:local longmin [:pick $longitude ($minutestart + 1) $secondstart];
:local longsec [:pick $longitude ($secondstart + 2) $secondend];
:local longfract ((([:tonum $longmin] * 6000) + ([:tonum $longsec] * 100) + ([:tonum $secfract] * 10) ) / 36);

:while (([:len $zeros] + [:len $longfract]) < 4) do={
    :set zeros ($zeros . "0");
};

:global newlong ($longdegree . "." . $zeros . $longfract);
:global newlonglink ($longdegreelink . "." . $zeros . $longfract);

:global latitude [:pick $gpstext (latstart + 10) $latend];
:set degreestart [:find $latitude " " -1];
:set minutestart [:find $latitude " " $degreestart];
:set secondstart [:find $latitude "'" $minutestart];

:if ([:len [:find $latitude "." 0]] < 1) do={
    :set secondend [:find $latitude "'" $secondstart];
    :set secfract "0";
} else={
    :set secondend [:find $latitude "." $secondstart];
    :set secfract [:pick $latitude ($secondend + 1) ($secondend +2)];
};

:local latdegree;
:local latdegreelink;

:if ([:pick $latitude 0 1] = "N") do={
    :set latdegree "+";
    :set latdegreelink "N";
} else={
    :set latdegree "-";
    :set latdegreelink "S";
};

:set latdegree ($latdegree . [:pick $latitude 2 $minutestart]);
:set latdegreelink ($latdegreelink . [:pick $latitude 2 $minutestart]);
:local latmin [:pick $latitude ($minutestart + 1) $secondstart];
:local latsec [:pick $latitude ($secondstart + 2) $secondend];
:local latfract ((([:tonum $latmin] * 6000) + ([:tonum $latsec] * 100) +([:tonum $secfract] * 10)) / 36);

:set zeros "";

:while (([:len $zeros] + [:len $latfract]) < 4) do={
    :set zeros ($zeros . "0");
};

:global newlat ($latdegree . "." . $zeros . $latfract);
:global newlatlink ($latdegreelink . "." . $zeros . $latfract);

:global coordinates ($newlong . "," . $newlat);

:global linkout "http://maps.google.com?q=$newlatlink+$newlonglink";
:global SMlinkout "http://www.openstreetmap.org/?lat=$newlat&lon=$newlong&zoom=8&layers=M";

:global kmlout "<?xml version=\"1.0\" encoding=\"UTF-8\"?>
<kml xmlns=\"http://www.opengis.net/kml/2.2\">
  <Placemark>
    <name>My router</name>
    <description>My router's location</description>
    <Point>
      <coordinates>$coordinates</coordinates>
    </Point>
  </Placemark>
</kml>
";

:if (valid) do={
    :global oldpos;

    :if ($oldpos != $coordinates) do={
        /file set [/file find name=gps.kml] contents=$kmlout
        /tool e-mail
        send to=$email subject="Router move" body="Moved to $latitude $longitude\r$linkout\r$SMlinkout" file=gps.kml
        :set oldpos $coordinates;
     };
} else={
         /tool e-mail
         send to=$email subject="Router gps position invalid" body="Router gps position invalid"
};</code>
Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
