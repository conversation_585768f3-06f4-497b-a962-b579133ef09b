<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Backup Graphing Data - Mikrotik Script RouterOS</title>
<meta content='Backup Graphing Data - Mikrotik Script RouterOS Database' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Backup Graphing Data - Mikrotik Script RouterOS</h1>
<pre>RouterOS shows graphs of queues, interfaces, memory, cpu, and disk usage. This script will allow you to backup such graphing data on RouterOS to files that are directly accessible.

Update: 3/10/2010: Now captures weekly.gif (missing from previous release)

<code class="routeros"># Backup graphing data
:local graphaddress "127.0.0.1"
:local graphport 80
:local graphimages "daily.gif, weekly.gif, monthly.gif, yearly.gif"

# Internal processing...
:local graphpath

# Main graphs page
:set graphpath ("graphs/")
/tool fetch address=$graphaddress port=$graphport mode=http \
	src-path=($graphpath) dst-path=($graphpath . "index.html")


# Resource graphs
:foreach g in=[:toarray "cpu,ram,hdd"] do={
   :set graphpath ("graphs/" . [:tostr $g] . "/")

   /tool fetch address=$graphaddress port=$graphport mode=http \
	src-path=($graphpath) dst-path=($graphpath . "index.html")

   :foreach i in=[:toarray $graphimages] do={
      /tool fetch address=$graphaddress port=$graphport mode=http \
	src-path=($graphpath . [:tostr $i]) dst-path=($graphpath . [:tostr $i])
   }
}

# Queue graphs
# Only simple queues are available (both enabled and disabled)
:foreach g in=[/queue simple find] do={
   :set graphpath ("graphs/queue/" . [/queue simple get $g name] . "/")

   /tool fetch address=$graphaddress port=$graphport mode=http \
	src-path=($graphpath) dst-path=($graphpath . "index.html")

   :foreach i in=[:toarray $graphimages] do={
      /tool fetch address=$graphaddress port=$graphport mode=http \
	src-path=($graphpath . [:tostr $i]) dst-path=($graphpath . [:tostr $i])
   }
}

# Interface graphs
# Only running interfaces are available
:foreach g in=[/interface find] do={
   :if ([/interface get $g running] = [:tobool true]) do={
      :set graphpath ("graphs/iface/" . [/interface get $g name] . "/")

      /tool fetch address=$graphaddress port=$graphport mode=http \
	src-path=($graphpath) dst-path=($graphpath . "index.html")

      :foreach i in=[:toarray $graphimages] do={
         /tool fetch address=127.0.0.1 port=8000 mode=http \
	src-path=($graphpath . [:tostr $i]) dst-path=($graphpath . [:tostr $i])
      }
   }
}</code>
Credit : https://wiki.mikrotik.com/wiki/Backup_graphing_data
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
