<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Removing Busy Leases From Dhcp Pool - MikroTik Script RouterOS</title>
<meta content='Removing Busy Leases From Dhcp Pool - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Removing Busy Leases From Dhcp Pool - MikroTik Script RouterOS</h1>
<pre>Whenever we’re designing a network, we’re trying to assign at least one continuous block of IP addresses for an automatic client assignment. Using the DHCP server, we can make network administration much easier. We can even use the DHCP server to assign an IP address for the servers in our network.

When the DHCP server spots a previously existing IP address from its DHCP leases pool, it will block that address from further assignment. This mechanism will prevent duplicated IP addresses in the same network segment.

The problem - a lack of the free IP addresses

These IP address will be marked as busy in the leases list. In most cases, the busy address is the static IP address of some host. If we have too many DHCP clients in the network, every unavailable lease may cause a problem for everyday operations.

The best way to avoid such a situation is to periodically remove these busy leases. The easiest way to do this, is to use the scheduled script.

<code class="routeros"># Delete BUSY leases from DHCP server
# version 1.0 (15.06.2017.)
# copyright (C) 2017. Srdjan Stanisic (<EMAIL>)
#
# the script will erase all DHCP leases in the status - BUSY
#
:local busyaddr [ip dhcp-server lease find status="busy"]
:local i
:foreach i in $busyaddr do={
/ip dhcp-server lease remove $i;
}</code>
Credit: https://mivilisnet.wordpress.com/2017/07/13/removing-busy-leases-from-mikrotik-dhcp-pool
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
