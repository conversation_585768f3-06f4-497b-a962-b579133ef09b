/* --------------------------------------------
################################################
# Tools For Mikrotik By mikrotiktool.Github.io
 
----------------------------------------------- */  
*{-webkit-transition: all .3s ease-in;-moz-transition: all .3s ease-in;-ms-transition: all .3s ease-in;transition: all .3s ease-in;}
body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table{margin:0; padding:0}
a:link {color: #08c;  text-decoration:none}
a:visited {color: #08c;  text-decoration:none}
a:hover {color: black;}
a:active {color: #08c;}  
* {
box-sizing: border-box;
}
body {
min-width: 200px;
max-width: 1000px;
margin:0 auto;	
padding: 20px;
-ms-text-size-adjust: 100%;
-webkit-text-size-adjust: 100%;
color: #24292e;
font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;
font-size: 16px;
line-height: 1.4;
word-wrap: break-word;
}
h1, h2, h3, h4, h5, h6{margin-top:20px; padding-bottom:20px; font-size:25px; color:#8E2DE2;}
pre {
    white-space: -moz-pre-wrap; /* Mozilla, supported since 1999 */
    white-space: -pre-wrap; /* Opera */
    white-space: -o-pre-wrap; /* Opera */
    white-space: pre-wrap; /* CSS3 - Text module (Candidate Recommendation) http://www.w3.org/TR/css3-text/#white-space */
    word-wrap: break-word; /* IE 5.5+ */
}
.logo {
border-bottom:0px solid #ccc;
margin-bottom:5px;
margin-top:10px;
color: #111;
font-size:30px;
font-weight:bold; 
}	
.logo a{
color:#111 !important;
text-decoration:none !important;
font-size:30px;
text-decoration:none;
}
.logo-left {
float:left;
}
.logo-right {
float:right;
}
.header{
padding:10px;
height:48px;
display:block;
background: #8E2DE2;  /* fallback for old browsers */
background: -webkit-linear-gradient(to left, #4A00E0, #8E2DE2);  /* Chrome 10-25, Safari 5.1-6 */
background: linear-gradient(to left, #4A00E0, #8E2DE2); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
color:#fff;
border-top:1px solid #ccc;
border-left:1px solid #ccc;
border-right:1px solid #ccc;
}
.header a{
color:#8E2DE2 !important;
text-decoration:none !important;
}
.header-left {
float:left;
width:425px;
}
.header-right {
float:right;
margin-top:-2px;
}
button {
color: #fff;
background-color: #8E2DE2;
border-color: #8E2DE2;
border-radius: 2px;
border:none;
padding:5px;
width:70px;
font-size:16px;
font-weight:bold;
}
@media screen and (max-width: 710px){
button {
margin-top:5px;
}  
.header{
height:auto;    
}   
.header-left, .header-right {
float:none;
display:block;
width:auto;
}
.logo-left, .logo-right {
float:none;
display:block;
width:auto;
text-align: center 
}
.header-left, .header-right {
  text-align: center
}   
}
@media screen and (max-width: 550px){
.logo-left a{
font-size:25px;
}	
.logo-left, .logo-right {
float:none;
display:block;
width:auto;
text-align: center 
}
.header-left, .header-right {
  text-align: center
} 
}