/* --------------------------------------------
################################################
# Tools For Mikrotik By mikrotiktool.Github.io
# Enhanced Colorful Design
----------------------------------------------- */

* {
    -webkit-transition: all .3s ease-in;
    -moz-transition: all .3s ease-in;
    -ms-transition: all .3s ease-in;
    transition: all .3s ease-in;
    box-sizing: border-box;
}

body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table {
    margin: 0;
    padding: 0;
}

body {
    min-width: 200px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    color: #2c3e50;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';
    font-size: 16px;
    line-height: 1.6;
    word-wrap: break-word;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* Enhanced Typography */
h1 {
    margin-top: 0;
    margin-bottom: 30px;
    padding: 25px;
    font-size: 2.5rem;
    color: #fff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    border-left: 5px solid #4CAF50;
}

h2, h3, h4, h5, h6 {
    margin-top: 25px;
    margin-bottom: 15px;
    padding: 15px 20px;
    font-size: 1.5rem;
    color: #fff;
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-left: 4px solid #FF6B6B;
}

/* Enhanced Links */
a:link {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

a:visited {
    color: #9b59b6;
    text-decoration: none;
}

a:hover {
    color: #e74c3c;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transform: translateY(-1px);
}

a:active {
    color: #2980b9;
}

/* Enhanced Pre and Code blocks */
pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
    padding: 25px;
    border-radius: 12px;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-left: 5px solid #3498db;
    font-family: 'Courier New', Consolas, Monaco, monospace;
    font-size: 14px;
    line-height: 1.5;
    overflow-x: auto;
}
/* Enhanced Logo */
.logo {
    border-bottom: 0;
    margin-bottom: 20px;
    margin-top: 20px;
    color: #2c3e50;
    font-size: 2rem;
    font-weight: bold;
    text-align: center;
    padding: 20px;
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.logo a {
    color: #667eea !important;
    text-decoration: none !important;
    font-size: 2rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-left {
    float: left;
}

.logo-right {
    float: right;
}

/* Enhanced Header */
.header {
    padding: 20px;
    height: auto;
    display: block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
    border: none;
}

.header a {
    color: #fff !important;
    text-decoration: none !important;
    font-weight: 500;
}

.header-left {
    float: left;
    width: 70%;
}

.header-right {
    float: right;
    margin-top: 0;
}

/* Enhanced Buttons */
button {
    color: #fff;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    min-width: 100px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

button:hover {
    background: linear-gradient(45deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
}
@media screen and (max-width: 710px){
button {
margin-top:5px;
}  
.header{
height:auto;    
}   
.header-left, .header-right {
float:none;
display:block;
width:auto;
}
.logo-left, .logo-right {
float:none;
display:block;
width:auto;
text-align: center 
}
.header-left, .header-right {
  text-align: center
}   
}
@media screen and (max-width: 550px){
    .logo-left a{
        font-size: 1.5rem;
    }
    .logo-left, .logo-right {
        float: none;
        display: block;
        width: auto;
        text-align: center;
    }
    .header-left, .header-right {
        text-align: center;
    }
}

/* Enhanced Code Highlighting */
code {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    color: #e74c3c;
    padding: 4px 8px;
    border-radius: 6px;
    font-family: 'Courier New', Consolas, Monaco, monospace;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.routeros {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    color: #ecf0f1 !important;
    border-left: 5px solid #e74c3c !important;
}

/* Content Container Enhancement */
#hidelink {
    margin-bottom: 20px;
}

/* Enhanced Content Area */
body > h1,
body > pre,
body > div {
    background: rgba(255,255,255,0.95);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

/* Special styling for script content */
pre code {
    background: none !important;
    color: inherit !important;
    padding: 0 !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    border: none !important;
}

/* Syntax highlighting colors */
.hljs-comment { color: #95a5a6; font-style: italic; }
.hljs-keyword { color: #3498db; font-weight: bold; }
.hljs-string { color: #2ecc71; }
.hljs-number { color: #e67e22; }
.hljs-built_in { color: #9b59b6; font-weight: bold; }
.hljs-literal { color: #e74c3c; }
.hljs-attribute { color: #f39c12; }
.hljs-title { color: #8e44ad; font-weight: bold; }

/* Animation for page load */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

body > * {
    animation: fadeInUp 0.6s ease-out;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
}

/* Credit text styling */
body > pre:last-child {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: #fff;
    text-align: center;
    font-style: italic;
    border-left: 5px solid #3498db;
}