<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Sync Address List from DNS Lookup Results - MikroTik Script RouterOS</title>
<meta content='Sync Address List from DNS Lookup Results - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Sync Address List from DNS Lookup Results - MikroTik Script RouterOS</h1>
<pre>This script might come in handy if you're trying to use domain names in firewall rules. To use this script you might write a script like the one below and schedule it. Be sure to declare three globals first : ListName, Servers, and Done. ListName and Servers are needed so that we can simulate an argument list. Done is necessary so that independent calls of the script don't step on each other since they share the same globals.

This will work with CNAMEs and follow them until we get A Records.

<code class="routeros">:global ListName google_voice
:global Servers {"talkr.l.google.com"}
/system script run dnsToAddressList</code>

This is the dnsToAddressList script:

<code class="routeros">:global ListName
:global Servers
:global Done
#has $Done been initialized?
:if ([:typeof $Done] != "boolean") do={
  :set Done true;
}
#make sure previous runs have finished
while (!$Done) do={
  :nothing;
}
#block any other runs
:set Done false;
#delete old address lists
:foreach aListItem in=[/ip firewall address-list find list=$ListName] do={
  /ip firewall address-list remove $aListItem;
}
:foreach aServer in=$Servers do={
#force the dns entries to be cached
  :resolve $aServer;

  :foreach dnsRecord in=[/ip dns cache all find where (name=$aServer)] do={
#if it's an A records add it directly
    :if ([/ip dns cache all get $dnsRecord type]="A") do={
       /ip firewall address-list add list=$ListName address=[/ip dns cache all get $dnsRecord data] comment=$aServer;
    }
#if it's a CNAME follow it until we get A records
    :if ([/ip dns cache all get $dnsRecord type]="CNAME") do={
      :local cname;
      :local nextCname
      :set cname [/ip dns cache all find where (name=$aServer && type="CNAME")];
      :set nextCname [/ip dns cache all find where (name=[/ip dns cache all get $cname data] && type="CNAME")];

      :while ($nextCname != "") do={
          :set cname $nextCname;
          :set nextCname [/ip dns cache all find where (name=[/ip dns cache all get $cname data] && type="CNAME")];
        }
#add the a records we found
    :foreach aRecord in=[/ip dns cache all find where (name=[/ip dns cache all get $cname data] && type="A")] do={
      /ip firewall address-list add list=$ListName address=[/ip dns cache all get $aRecord data] comment=$aServer;
      }
    }
  }
}
#allow other scripts to call this
:set Done true</code>
Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

