<?xml version='1.0' encoding='UTF-8'?><urlset xmlns='http://www.sitemaps.org/schemas/sitemap/0.9'>
<url><loc>mikrotiktool.github.io/</loc></url>
<url><loc>mikrotiktool.github.io/ecmp.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik-pcq-generator.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/A-Bit-of-Sounds.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/A-script-to-set-up-WAN-or-LAN-or-WLAN-to-get-you-started.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/ARP-Table-To-Dynamic-Simple-Queue.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Add-Static-DHCP-Leases-to-ARP-List.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Alignment-Script-that-reads-back-RSSI-with-Beeps.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Allow-use-ofntporg-pool-service-for-NTP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Audible-signal-test.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Auto-Connect-to-Internet-With-DHCP-Client.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Auto-Repair-Passthrough-In-Mangle.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Auto-Simple-Queue-From-DHCP-Server.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Auto-Update-RouterOS-Package-From-Script.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Auto-upgrade-script-V3x.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Automated-Usage-Script-without-usermanager.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Automatic-Expired-Hotspot-Page-Without-A-Proxy.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Backup-graphing-data.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Basic-universal-firewall-script.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Bittorrent-layer-7.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Block-MAC-Address-Device.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Block-Sites-User-Keywords-Use-The-Dns-Cache.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Block-Tiktok.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Block-Windows-Update.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Block-access-to-specific-websites.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Blocking-Modem.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Blog-Website-With-Keywords-Use-DNS-Static.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Bypass-Full-Speed-Local-Transfer-File.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Bypass-Local-Connection.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Calculate-with-decimal-numbers.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Calea-perl-trafr.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Change-All-Queue-Type-in-Interface-Queue.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Change-Mikrotik-identity-or-Winbox-Caption.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Check-bandwidth-and-add-limitations.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Check-if-RTO-and-send-to-Telegram.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Checking-for-the-update-New-Version.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Clear-Log-and-Session-User-Manager.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Complete-Bandwidth-Monitoring.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Complete-zoom-mangle-and-queue-.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Converting_network_and_gateway_from_routing_table_to_hexadecimal_string.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Create-Hotspot-Multi-Login-With-Bandwidth-Limit.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Create-Multiple-Queue-Tree-At-Once.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Create-Multiple-Simple-Queue-At-Once.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Create-a-file.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Custom-Limit-Bandwidth-Hotspot-With-Queue-Simple.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Custom-Limit-Bandwidth-Hotspot-With-Queue-Tree.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/DNSCRYPT-with-OpenDNS.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Default-Filter-Rules.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Delete-Arp-Trafic-For-Arp-Table.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Detect-new-log-entry.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Dial_PPPoE_until_a_Certain_IP_Range_is_Obtained.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Dynamic-DNS-on-private-DNS-server.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Enable-Disable-New-Guest-User-Account-Daily-Hotspot.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Extract-the-day-of-the-month.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Failover-with-Netwatch.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Failover-with-Scripting.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Force-Disconnect-Wireless-Stations-with-Low-CCQ.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Generate-backup-and-sendit-by-email.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Get-Day.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Google-Safe-Search-use-DNS-Static.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Hotspot-Auto-User-logout-user-When-internet-down.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/How-To-Backup-rsc-combination.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/How-To-Backup-rsc-compact.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/How-To-Backup-rsc-hide-sensitive.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/How-To-Backup-rsc-terse.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/How-To-Backup-rsc-verbose.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/How-To-Backup.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/How-to-Backup-Use-Export-rsc.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/How-to-Backup-Use-import-rsc.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/How-to-get-todays-date-info.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-List-AWS-EC2-Amazon.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-List-WhatsApp.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-List-twitter.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-list-Facebook.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-list-GGC.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-list-Speedtest.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-list-Telergram.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-list-ix-nice-rsc.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-list-netflix.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-list-tiktok.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Address-list-zoom-meeting.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Pool-Statistics.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/IP-Private-or-IP-Local-Standard-RFC1918.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Improved-Netwatch-2.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Improved-Netwatch.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Include-custom-function-in-another-script.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-ECMP-2-Line-ISP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-ECMP-3-Line-ISP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-ECMP-4-Line-ISP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-ECMP-5-Line-ISP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-ECMP-6-Line-ISP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-ECMP-Failover-Script.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-PCC-2-Line-ISP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-PCC-3-Line-ISP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-PCC-4-Line-ISP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-PCC-5-Line-ISP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LB-PCC-6-Line-ISP.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/LED-Antenna-Alignment.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Layer-7-For-All-File-Extention.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Layer-7-For-All-Media-Extention.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Layer-7-For-All-Social-Media-Socmed.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Layer-7-For-All-Streaming-Video.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Layer-7-Regex-For-Telkom-Ads.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Limit-Different-Bandwidth-In-Day-and-Night.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Limit-sharing-Hotspot-with-Change-TTL.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Limiting-a-User-To-a-Given-Amount-Of-Traffic-With-User-Levels.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Log-Parser-Event-Trigger-Script.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Make-an-Automated-Configuration-and-Uninstall.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Mangle-and-Port-AnyDesk.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Mangle-and-Port-RDC.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Mangle-and-Port-TeamViewer.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Mangle-and-Port-UltraViewer.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Mangle-and-Port-VNC.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Mangle-and-Port-chrome-remote-desktop.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Mangle-and-Port-employeespro.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Mikrotik-Terminal-Command-Line-and-Keyboard-Command.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Monitor-Logs-Send-Email-Alert-Or-Run-Script.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Monitoring-Script.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/No-Mark-Without-Mangle-in-Queue-Tree.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/PPP-Keepalive-ping.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/PPPoE-Secret-Random-Username-and-Password.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Parent-Simple-Queue-Always-On-Top.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Parse-file-to-add-ppp-secrets.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Ping-ICMP-To-Routing-ISP-Games.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Port-Forward-for-All-Remote-Desktop.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Print-function.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Protect-Modem-And-Webfig-Pages-Using-Internal-Proxy.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Protect-Open-Recursive-DNS.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Protect-Router-from-Port-Scanner.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Protect-web-open-porxy.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Pseudo-Random-Number-Generator.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Queue-Tree-And-E-Mailing-Stats.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Read-and-write-large-files.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Reboot-Boards-due-to-low-Memory-with-notification.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Reboot-mikrotik-from-terminal.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Remove-Default-Configuration.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Remove-Default-Simple-Queue-from-Hotspot.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Remove-Files-On-Mikrotik.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Removing-Busy-Leases-From-Dhcp-Pool.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Reset-DataBase-User-Manager-to-default.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Reset-Mikrotik-via-Script-from-Terminal.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Resolve-host-name.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Routing-FailOver-with-2-ISP-gateway.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Routing-FailOver-with-3-ISP-gateway.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Routing-Via-A-Dhcp-Allocated-Gateway.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/SXT-5HnD-Alignment-Script.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Scheduled-Check-For-Loaded-Interfaces.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Scheduled-WAN-Disconnect.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Script-to-Create-Directory.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Securing-L2TP-Server-for-IPSec.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Send-Email-About-Reboot.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Sending-Mails-When-Battery-Low.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Setting-NTP-Network-Time-Protocol.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Setting-Static-DNS-Record-For-Each-DHCP-Lease.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Signalorg-Mangle-And-Queue.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Solution-Hotspot-login-Page-LB-PCC.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Speedtest-Mangle-layer7.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Speedtest-Regexp-Layer7.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Speedtest-domain-List.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Sync-Address-List-with-DNS-Cache.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/UPnP-Multi-WAN.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Updating-The-Firmware-From-Terminal.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Use-Functions-in-CMD-Script.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Use-Host-Names-In-Firewall-Rules.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Use-string-as-function.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Using-Fetch-and-Scripting-to-add-IP-Address-Lists.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Using-scripting-to-overcome-the-inability-to-specify-number-ranges-on-the-command-line.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Virus-and-Malware-Port-Blocking.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Write-simple-queue-stats-in-multiple-files.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/Youtube-Restricted-Mode-use-DNS-Static.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/add-a-data-limit-to-trial-hotspot-users.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/address-list-dynamic-to-static.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/anti-hack-bootloader.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/auto-backup-filename-backup-and-send-to-gmail.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/auto-backup-filename-rsc-and-send-to-gmail.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/auto-fix-sntp-client-and-timezone-for-indonesia-country.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-ads-use-dns-static.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-bigo-layer7-content-tls.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-download-based-download-sizes.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-facebook-layer7-content-tls.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-iflix-layer7-content-tls.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-istagram-layer7-content-tls.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-netflix-layer7-content-tls.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-signal-layer7-content-tls.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-telegram-layer7-content-tls.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-tiktok-content-firewall-rules.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-tiktok-layer7-firewall-rules.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-tracert.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-twitter-layer7-content-tls.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-whatsapp-layer7-content-tls.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/block-youtube-layer7-content-tls.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/bypass-user-hotspot-with-macaddress.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/change-Interface-ether-Default-1.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/change-Interface-ether-Default-2.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/change-all-queue-type-in-queue-simple.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/change-all-queue-type-in-queue-tree.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/change-path-database-user-manager-to-disk-or-usb.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/change-system-note.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/check-ip-change.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/check-routeros-update-and-notification.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/clear-log-Mikrotik.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/discover-unknown-dhcp-server-on-the-network.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dns-chace-flush.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-changeip-behind-nat.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-changeip.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-dnsomatic-behind-nat.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-dnsomatic.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-dyndns-behind-nat.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-dyndns.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-everydns.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-hurricane-electric-dns.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-namecheap.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/dynamic-dns-update-script-for-no-ip-dns.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/expire-users-a-after-number-of-days.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/fail-over-recursive-gateway.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/filter-a-command-output.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/get-active-vpn-connections-via-e-mail.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/get-ip-address-list-netflix-with-raw.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/google-domain-list-or-all-google-host.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/gps-text-file-converter-to-google-earth-or-maps.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/how-to-change-mac-address.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/how-to-disable-google-ssl-using-static-dns.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/hurricane-electric-ipv6-tunnel-ipv4-endpoint-updater.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/initiating-the-update-process-new-version.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/limiting-a-user-to-a-given-amount-of-traffic-using-queues.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/logging-average-ccq-and-wireless-clients-stats.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/logging-snr-and-thruput-values.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/make-sure-all-clients-use-router-dns.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/notification-of-device-connection-to-the-network.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/notification-when-public-ip-address-router-changes-to-email-or-telegram.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/only-1-hotspot-id-can-login-together.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/password-ont-modem-telkom-indihome.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/paypal-with-hotspot-and-walled-garden-bypass.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/port-game-online-mobile-games.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/port-game-online-pc-games.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/port-game-online-web-games.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/port-knocking-icmp.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/protect-connection-with-anti-netcut.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/protect-default-service-port.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/protect-mikrotik-from-ddos.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/protect-mikrotik-from-exploit.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/rebuild-database-User-Manager.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/remote-ip-public-static.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/remove-firewall-address-lists.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/reset-counter.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/restore-mac-address-to-default-interface.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/script-to-find-the-day-of-the-week.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/sending-power-on-notification-to-telegram.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/sending-text-out-over-a-serial-port.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/sending-your-self-an-e-mail-with-dsl-interface-ip-address.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/setup-and-troubleshooting-routeros-email-function.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/show-usermanager-password.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/strip-ip-address-netmask.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/super-mario-bross-theme-sound-beep.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/sync-address-list-from-dns-lookup-results.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/update-static-dns-entries-every-10-minutes.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/upnp-redirect-the-port-to-mikrotik-device.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/use-mikrotik-as-fail2ban-firewall.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/wake-on-lan-before-connection-to-remote-desktop.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/welcome.html</loc></url>
<url><loc>mikrotiktool.github.io/mikrotik/wms-auto-login.html</loc></url>
<url><loc>mikrotiktool.github.io/pcc-calculation.html</loc></url>
<url><loc>mikrotiktool.github.io/pcc.html</loc></url>
<url><loc>mikrotiktool.github.io/port-knocking-icmp.html</loc></url>
<url><loc>mikrotiktool.github.io/queue-tree-generator-shared.html</loc></url>
<url><loc>mikrotiktool.github.io/remote-ip-public-static.html</loc></url>
<url><loc>mikrotiktool.github.io/simple-queue-generator-shared.html</loc></url>
<url><loc>mikrotiktool.github.io/simple-queue-generator.html</loc></url>
<url><loc>mikrotiktool.github.io/vpn-game-generator.html</loc></url>
<url><loc>mikrotiktool.github.io/vpn-game-generator2.html</loc></url></urlset>