<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset='utf-8' />
	<meta content='width=device-width, initial-scale=1, maximum-scale=1' name='viewport' />
	<title>Mikrotik Notification Expired For Hotspot, PPPoE and Static-IP Generator - mikrotiktool.Github.io</title>
	<meta content='Mikrotik Notification Expired For Hotspot, PPPoE and Static-IP Generator - mikrotiktool.Github.io' name='description' />
	<meta content='expired, isolation, pppoe, port knocking, knock, script generator, mikrotik, router, winbox, termimal, rsc, script, hotspot, wireless, rtrwnet' name='keywords' />
	<meta content='index, follow, noodp' name='robots' />
	<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon">
	<meta property="og:title" content="Mikrotik Notification Expired For Hotspot, PPPoE and Static-IP Generator ">
	<meta property="og:description" content="Mikrotik Notification Expired For Hotspot, PPPoE and Static-IP Generator ">
	<meta property="og:image" content="https://user-images.githubusercontent.com/42666125/130205304-1d7dc906-4483-40db-a28e-ca031ef0d42b.png">
	<meta property="og:image:alt" content="Mikrotik Notification Expired For Hotspot, PPPoE and Static-IP Generator ">
	<meta property="og:type" content="website">
	<meta property="og:url" content="https://mikrotiktool.github.io/mikrotik-expired-isolate-pppoe-hotspot.html"> 
	<script data-ad-client="ca-pub-3815059393374366" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
	<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M5TL99Q9M2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-M5TL99Q9M2');
</script>
	<style> 
		*{-webkit-transition: all .3s ease-in;-moz-transition: all .3s ease-in;-ms-transition: all .3s ease-in;transition: all .3s ease-in;}
		body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table{margin:0; padding:0}
		body { font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; margin:20px;}
		html, body {
		height: 100%;
		width:100%;
		margin-top: 0px;
		padding:0;
		font-size:16px;
		}
		* {
		box-sizing: border-box;
		}
		.Menu{
		width:100%;
		border-bottom:1px solid #ccc;
		height:100%;
		padding-bottom: 15px;
		margin-bottom: 20px;
		font-weight:bold;
		 }
		#wrap{
		width:1120px;
		margin:0 auto;
		padding:10px;
		background:#fff
		}
		.logo {
		margin-top:0px;
		color: #111;
		text-decoration:none;
		font-size:27px;
		font-weight:bold; 
		}	
		.logo a{
		color:#111 !important;
		text-decoration:none !important;
		}
		.header{
		height:auto; 
		width:100%;
		margin-top:0px;
		margin-bottom:10px;
		border-top:1px solid #ccc;
		}
		.main-wrap{
		display:flex;
		}     
		.content{
        flex:1;
		float:right;
		width:800px;
		padding: 10px;
		border-top:1px solid #bbb;
		border-bottom:1px solid #bbb;
		border-right:1px solid #bbb;
		background:#ddd;
		height:auto;
		}
		.sidebar{
		float:left;
		width:300px;
		padding: 10px;
		background-color:#ddd;
		border:1px solid #bbb; 
		height:auto;
		}
		.footer{
        font-size:14px;
		padding-top:15px;
		clear:both;
		width:auto;
		}

		h1 {
		color:#111;
		font-size:24px;
		margin-bottom:5px;
		margin-top:10px;
		}
		a:link {
		color: rgba(255,0,36,.7);
		}
		a:visited {
		color: rgba(255,0,36,.7);
		}
		a:hover {
		color: rgba(255,0,36,.7);
		}
		a:active {
		color: rgba(255,0,36,.7);
		}
		.menu {
		clear:both;
		width:auto;
		padding-bottom:13px;
		}
		.menu1 {
		width:360px;
		margin-bottom:1px;
		font-weight:bold;
		background-color:#4CAF50;
		padding:6px;
		color:white;
		display: inline-block;
		margin:0;
		}
		.menu1 a:link {
		text-decoration:none;
		color:white;
		}
		.menu1 a:visited{
		text-decoration:none;
		color:white;
		}
		.menu2 {
		width:300px;
		margin-bottom:1px;
		font-weight:bold;
		background-color:#bbb;
		padding:6px;
		color:white;
		display: inline-block;
		margin:0;
		}
		.menu2 a:link{
		text-decoration:none;
		color:white;
		}
		.menu2 a:visited{
		text-decoration:none;
		color:white;
		}
		 button {
		 color: #fff;
		 background-color: rgba(255,0,36,.7);
		 border-color: rgba(255,0,36,.7);
		 border:none;
		 padding:7px;
		 width:200px;
		 font-size:16px !important;
		 font-weight:bold;
		 }
		 .d-link {
		 color: #fff;
		 background-color: #fff;
		 border-color: rgba(255,0,36,.7);
		 border:none;
		 padding:7px;
		 width:200px;
		 font-size:16px !important;
		 font-weight:bold;
		 }		 
		 .d-link a:link{
		 color: rgba(255,0,36,.7);
		 text-decoration:underline;
		 }	
		 .d-link a:visited{
		 color: rgba(255,0,36,.7);
		 text-decoration:none;
		 }	
		 .d-link a:hover {
		  color: rgba(255,0,36,.7);
		}
		.d-link a:active {
		  color: rgba(255,0,36,.7);
		}
		 
		 .row:after {
		 content: "";
		 display: table;
		 clear: both;
		 }  
		 input[type=text], select, textarea {
		 width: 100%;
		 padding: 5px;
		 border: 1px solid #bbb;
		 border-radius: 1px;
		 resize: vertical;
		 margin-bottom:12px;
		 font-size:16px !important;
		 }
		 label {
		 padding: 5px 5px 5px 0;
		 display: inline-block;
		 }
		 input[type=submit] {
		 background-color: rgba(255,0,36,.7);
		 color: white;
		 padding: 12px 20px;
		 border: none;
		 border-radius: 1px;
		 cursor: pointer;
		 float: right;
		 }
		 input[type=submit]:hover {
		 background-color: #45a049;
		 }
		 .col-25 {
		 float: left;
		 width: 25%;
		 margin-top: 6px;
		 }
		 .col-75 {
		 float: left;
		 width: 75%;
		 margin-top: 6px;
		 }
		 /* Clear floats after the columns */
		 .row:after {
		 content: "";
		 display: table;
		 clear: both;
		 }
		 .list-game {
		 height: 200px;
		 background:white;
		 overflow: scroll;
		 overflow-x: hidden;
		 width: 100%;
		 margin-top:2px;
		 padding: 3px;
		 border: 1px solid rgba(0,0,0,0.25);
		 }
		 .list-game label {
		 padding: 0;
		 display: inline-block;
		 } 
		 .list-mangle {
		 height: 300px;
		 background:#fff;
		 overflow: auto;
		 overflow-x: auto;
		 width: 100%;
		 padding: 4px;
		 margin-top:8px;
		 border: 1px solid #ccc;
		 }
		 
		 .list-mangle2 {
		 height: 300px;
		 background-color:rgba(255,0,36,.7);
		 overflow: auto;
		 overflow-x: auto;
		 width: 100%;
		 padding: 20px;
		 margin-top:8px;
		 border: 2px solid #fff;
		 color:#fff;
		 font-family: montserrat,sans-serif;
		 }
		 
		 table, tr, td {
		border: none;
		}
		/* The Modal (background) */
		.modal {
		  display: none; /* Hidden by default */
		  position: fixed; /* Stay in place */
		  z-index: 1; /* Sit on top */
		  padding-top: 100px; /* Location of the box */
		  left: 0;
		  top: 0;
		  width: 100%; /* Full width */
		  height: 100%; /* Full height */
		  overflow: auto; /* Enable scroll if needed */
		  background-color: rgb(0,0,0); /* Fallback color */
		  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
		}
		/* Modal Content */
		.modal-content {
		  background-color: #fefefe;
		  margin: auto;
		  padding: 20px;
		  border: 1px solid #888;
		  width: 50%;
		  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
		}

		/* The Close Button */
		.close {
		  color: #aaaaaa;
		  float: right;
		  font-size: 28px;
		  font-weight: bold;
		}

		.close:hover,
		.close:focus {
		  color: #000;
		  text-decoration: none;
		  cursor: pointer;
		} 
	</style>
</head>
<body onLoad="callmodal()"> 
<div id="wrap">	
<div class="logo">	  
<a href="https://mikrotiktool.github.io"><span style="color:rgba(255,0,36,.7) !important">mikrotiktool</span>.Github.io</a> <img style="float:right; padding-top:10px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=hits&edge_flat=false" alt="Hits"/> 
</div>	
<div class="header">
<span style="float:left; display:inline;margin-right:6px; margin-top:10px">
<a href="https://mikrotiktool.github.io/mikrotik-expired-isolate-pppoe-hotspot.html"><img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="HOME"></a>
</span>	
<h1>MIKROTIK ALL-IN-ONE NOTIFICATION EXPIRED FOR HOTSPOT, PPPOE AND STATIC IP</h1>
</div>
<div class="main-wrap">
		<div class="sidebar">
			<label><b>Ether Name (for Hotspot only)</b><br> <span style='color:rgba(255,0,36,.7)'>ex: bridge1 or ether2 or E2-wlan</span></label>
			<input type="text" id="interface-name" placeholder="ex: bridge1 or ether2 or E2-LOCAL" value="bridge1">
			<label><b>IP Address For Expired</b><br> <span style='color:rgba(255,0,36,.7)'>(IP Subnet /24 only)</span></label>
			<input type="text" id="ip-address" placeholder="**********/24" value="**********/24">
			<label><b>Proxy Port (Redirect)</b><br> <span style='color:rgba(255,0,36,.7)'>Don't use 3128 or 8080</span></label>
			<input type="text" id="proxy-port" placeholder="8082" value="8082">
			<label><b>Expired Message</b><br><span style='color:rgba(255,0,36,.7)'>Can Use HMTL Tag</span> </label>
			<input type="text"  id="input-msg-title" placeholder="EXPIRED!" value="EXPIRED!">
					<textarea  name="data" id="input-msg-body" style="height:278px">
DEAR CUSTOMER, WE INFORM YOU THAT YOUR INTERNET SERVICE IS CURRENTLY ISOLATED. PLEASE MAKE PAYMENT OF THE BILL THROUGH THE ACCOUNT WE PROVIDE. IN ORDER FOR THE INTERNET SERVICE TO REMAIN ACTIVE, IT IS REQUESTED TO MAKE A PAYMENT BEFORE THE DUE DATE OF EACH MONTH.</textarea>
			<input type="text"  id="input-msg-contact" placeholder="Contact Owner: +62 813-2222-3333" value="Contact Owner: +62 813-2222-3333">
		</div>
		<div class="content"> 
		<div style="margin-left:2px; margin-top:5px; font-weight:bold">Copy-Paste the Script to your Terminal!</div>
			<div class="list-mangle">
				<table id="showScript" style="padding:5px; white-space: nowrap;">
					<tr>
						<td> <span style="color:rgba(255,0,36,.7);">
							###############################################################<br>
							# Mikrotik Notification Expired For Hotspot, PPPoE and Static-IP Generator<br>
							# Date/Time: <span id="tanggalwaktu"></span><br>
							#   - mikrotiktool.github.io<br>
							###############################################################<br><br>
							</span>	
							<b>/ip proxy set enabled=yes port=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-set-port-proxy">8082</span> max-cache-size=none</b></br>
							<b>/ip proxy access </b><br>
							add action=allow dst-port=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-proxy-port">8082</span> src-address=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-proxy-ip">**********/24</span> comment="EXPIRED By BNT"<br>
							add action=deny local-port=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-proxy-port2">8082</span> comment="EXPIRED By BNT"<br>
							<b>/ip address</b><br>
							add address=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-ip-address">**********/24</span> interface=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-interface">bridge1</span> comment="EXPIRED By BNT"<br>
							<b>/ip firewall address-list</b><br>
							add list=EXPIRED  address=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-address-list">**********/24</span> comment="EXPIRED By BNT"<br>
							<b>/ip pool</b><br>
							add name=EXPIRED  ranges=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-ip-pool">**********-************</span> comment="EXPIRED By BNT"<br>
							<b>/ip firewall filter</b><br>
							add chain=forward src-address-list=EXPIRED action=drop comment="EXPIRED By BNT"<br>
							<b>/ip firewall nat</b><br>
							add chain=dstnat src-address-list=EXPIRED  protocol=tcp dst-port=80,443 action=redirect to-ports=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-nat-port">8082</span> comment="EXPIRED By BNT"<br>
							<b>/ppp profile</b><br>
							add name=EXPIRED  local-address=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-ppp-profile">**********</span> remote-address=EXPIRED  comment="EXPIRED By BNT" on-down=":local username \$\"user\";\r\n/ip proxy access remove [find comment=\"ppp-\$username\"];" on-up=":local username \$\"user\";\r\n:local address \$\"remote-address\";\r\n/ip proxy access add action=deny src-address=\$address comment=\"ppp-\$username\";"<br>
							<b>/ip hotspot user profile</b><br>
							add address-pool=EXPIRED !mac-cookie-timeout transparent-proxy=no on-login=":local username \$\"user\";\r\n:local ipaddress \$\"address\";\r\n/ip proxy access add action=deny src-address=\$ipaddress comment=\"hs-\$username\";" on-logout=":local username \$user;\r\n/ip hotspot cookie remove [find user=\$username]\r\n/ip proxy access remove [find comment=\"hs-\$username\"]" name=EXPIRED shared-users=100<br>
							<b>/ip hotspot walled-garden ip</b><br>
							add action=accept disabled=no !dst-address !dst-address-list dst-port=<span style="color:rgba(255,0,36,.7); font-weight:bold" id="result-wallet-garden-port">8082</span> protocol=tcp !src-address !src-address-list comment="EXPIRED By BNT"<br>
						</td>
					</tr>
				</table>

			</div>
			<br>
			<button style="margin-right:10px;" type="button" onclick="myFunction()">Generate Script</button>
			<button style="margin-top:0px" id="copyscript" type="button" onclick="selectElementContents(document.getElementById('showScript'))">Copy Script</button>
			<span style="float:right;margin-bottom:15px; margin-top:0px;padding-left:20px" class="d-link"><a class="download" href="#" download="error.html">Download error.html</a></span><br>
		<div class="list-mangle2">
		<center>
		<span id="msg-title" style="color:#fff; font-weight:bold; font-size:70px;">EXPIRED!</span><br>
		<span id="msg-body" style="color:#fff; font-weight:bold; font-size:15px;line-height:2.0">
DEAR CUSTOMER, WE INFORM YOU THAT YOUR INTERNET SERVICE IS CURRENTLY ISOLATED. PLEASE MAKE PAYMENT OF THE BILL THROUGH THE ACCOUNT WE PROVIDE. IN ORDER FOR THE INTERNET SERVICE TO REMAIN ACTIVE, IT IS REQUESTED TO MAKE A PAYMENT BEFORE THE DUE DATE OF EACH MONTH.</span><br><br>
		<span id="msg-contact" style="color:#fff; font-weight:bold; font-size:15px;;line-height:2.0"><span>Contact Owner: +62 813-2222-3333</span><br>
		</center>
		</div>	
		<br>
		</div>
        </div>
		<div class="footer">
       <div style="margin-top:0px;float:right">
	   <span style="color:rgba(255,0,36,.7); font-weight:bold">Tutorial How-To:</span><br>
	   1. Download error.html and upload to winbox at "File" to webproxy Folder "webproxy/error.html"<br>
	   2. For Enable isolation just set your Profile to "EXPIRED" at Hotspot or PPPoE on Winbox, Don't use<br>
	   Expired for Hotspot Voucher, you can use only for Hotspot monthly.<br>
	   3. You can set manual isolation for Static-IP, just input user IP at Firewall -> Address Lists -> EXPIRED<br>
	   <br>
		<br>© Copyright 2020-2021 <a href="https://mikrotiktool.github.io/">mikrotiktool.github.io</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a><br> Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a> | <a href="">My Facebook Page</a> | Creator: <a href="">Agus Ramadhani</a>
		</div>         
		<span style="color:rgba(255,0,36,.7); font-weight:bold"># This Script for remove all Script from this Generator:</span><br>
		/ip proxy set enabled=no port=8080 max-cache-size=none <br>
		/ip pool remove [find comment="EXPIRED By BNT"]<br>
		/ip firewall nat remove [find comment="EXPIRED By BNT"]<br>
		/ip firewall filter remove [find comment="EXPIRED By BNT"]<br>
		/ip firewall address-list remove [find comment="EXPIRED By BNT"]<br>
		/ppp profile remove [find comment="EXPIRED By BNT"]<br>
		/ip proxy access remove [find comment="EXPIRED By BNT"]<br>
		/ip hotspot walled-garden ip remove [find comment="EXPIRED By BNT"]<br>
		/ip hotspot user profile remove [find name ="EXPIRED"]<br>
		/ip address remove [find comment="EXPIRED By BNT"]<br>
	</div>
	</div>
 	 
	<script type="text/javascript">
var modal = document.getElementById("myModal");
var span = document.getElementsByClassName("close")[0];
function callmodal() {
  modal.style.display = "block";
}
span.onclick = function() {
  modal.style.display = "none";
}
window.onclick = function(event) {
  if (event.target == modal) {
    modal.style.display = "none";
  }
}
</script> 
	<script>
var _0x1eb04b=_0x9cd4;(function(_0x16fc5a,_0x4d3162){var _0x39ab52=_0x9cd4,_0x362fcd=_0x16fc5a();while(!![]){try{var _0x364314=parseInt(_0x39ab52(0xd1))/0x1*(parseInt(_0x39ab52(0xcf))/0x2)+parseInt(_0x39ab52(0xd3))/0x3*(-parseInt(_0x39ab52(0xe3))/0x4)+-parseInt(_0x39ab52(0xdb))/0x5+-parseInt(_0x39ab52(0xc1))/0x6+parseInt(_0x39ab52(0xe7))/0x7+parseInt(_0x39ab52(0xd9))/0x8+-parseInt(_0x39ab52(0xcd))/0x9*(parseInt(_0x39ab52(0xda))/0xa);if(_0x364314===_0x4d3162)break;else _0x362fcd['push'](_0x362fcd['shift']());}catch(_0x2d53b0){_0x362fcd['push'](_0x362fcd['shift']());}}}(_0x46ee,0xe51af));var dt=new Date();function _0x46ee(){var _0xc6eb5b=['msg-body','input-msg-contact','getElementById','querySelector','result-ip-address','</div><div\x20style=\x27font-size:2vw;line-height:2.0;\x27>','.0/24','186088gIbtOG','addRange','ip-address','tanggalwaktu','8737274tksyaS','result-proxy-port','</div><br><div\x20style=\x27font-size:2vw;line-height:2.0;\x27>','.2-','toLocaleString','result-interface','4920966mzmDZE','<!DOCTYPE\x20html><html><meta\x20name=\x27viewport\x27\x20content=\x27width=device-width,\x20initial-scale=1.0\x27><style>body{font-family:\x20montserrat,\x20sans-serif;font-weight:700;color:#fff;background-color:rgba(255,0,36,.7);ms-text-size-adjust:\x20100%;-webkit-text-size-adjust:100%;}</style><body><center><div\x20style=\x27padding:50px\x27><div\x20style=\x27font-size:10vw;\x27>','value','result-proxy-ip','</div><div\x20style=\x27font-size:10px;\x27>Script\x20Generator\x20by\x20mikrotiktool.github.io</div></div></center></body></html></html>','createTextRange','toUpperCase','lastIndexOf','selectNode','result-nat-port','result-address-list','substring','45igKPzc','createRange','128416MzhfWZ','input-msg-title','4MeorCW','proxy-port','3AiYVsp','.download','.254','select','getSelection','innerHTML','11276192TuTAGe','2204110AjbifF','36925yfgXMZ'];_0x46ee=function(){return _0xc6eb5b;};return _0x46ee();}document['getElementById'](_0x1eb04b(0xe6))[_0x1eb04b(0xd8)]=dt[_0x1eb04b(0xeb)]();function upperCaseF(_0x28a3a5){setTimeout(function(){var _0x21b3f7=_0x9cd4;_0x28a3a5[_0x21b3f7(0xc3)]=_0x28a3a5['value'][_0x21b3f7(0xc7)]();},0x1);}function _0x9cd4(_0x3cd74b,_0x110aa6){var _0x46eeb2=_0x46ee();return _0x9cd4=function(_0x9cd466,_0x1a444a){_0x9cd466=_0x9cd466-0xc0;var _0x4a01b6=_0x46eeb2[_0x9cd466];return _0x4a01b6;},_0x9cd4(_0x3cd74b,_0x110aa6);}function myFunction(){var _0x43c4fb=_0x1eb04b,_0x1ca2fa=document[_0x43c4fb(0xde)]('input-msg-body')[_0x43c4fb(0xc3)],_0x4110c0=document[_0x43c4fb(0xde)](_0x43c4fb(0xd0))[_0x43c4fb(0xc3)],_0x1ec34d=document[_0x43c4fb(0xde)](_0x43c4fb(0xdd))['value'],_0x111b8c=document[_0x43c4fb(0xde)](_0x43c4fb(0xe5))[_0x43c4fb(0xc3)],_0x531a67=document['getElementById'](_0x43c4fb(0xd2))[_0x43c4fb(0xc3)],_0x4dcefb=document[_0x43c4fb(0xde)]('interface-name')['value'],_0xbcaefa=_0x111b8c,_0x2d45c3=_0xbcaefa[_0x43c4fb(0xcc)](0x0,_0xbcaefa[_0x43c4fb(0xc8)]('.'));document['getElementById'](_0x43c4fb(0xc4))[_0x43c4fb(0xd8)]=_0x2d45c3+_0x43c4fb(0xe2),document['getElementById']('result-ip-pool')['innerHTML']=_0x2d45c3+_0x43c4fb(0xea)+_0x2d45c3+_0x43c4fb(0xd5),document['getElementById'](_0x43c4fb(0xe0))['innerHTML']=_0xbcaefa,document['getElementById'](_0x43c4fb(0xcb))['innerHTML']=_0x2d45c3+_0x43c4fb(0xe2),document[_0x43c4fb(0xde)]('result-ppp-profile')[_0x43c4fb(0xd8)]=_0x2d45c3+'.1',document[_0x43c4fb(0xde)]('result-set-port-proxy')[_0x43c4fb(0xd8)]=_0x531a67,document[_0x43c4fb(0xde)](_0x43c4fb(0xe8))['innerHTML']=_0x531a67,document['getElementById']('result-proxy-port2')[_0x43c4fb(0xd8)]=_0x531a67,document['getElementById'](_0x43c4fb(0xca))['innerHTML']=_0x531a67,document[_0x43c4fb(0xde)]('result-wallet-garden-port')[_0x43c4fb(0xd8)]=_0x531a67,document[_0x43c4fb(0xde)](_0x43c4fb(0xc0))[_0x43c4fb(0xd8)]=_0x4dcefb,document[_0x43c4fb(0xde)]('msg-title')[_0x43c4fb(0xd8)]=_0x4110c0,document['getElementById'](_0x43c4fb(0xdc))[_0x43c4fb(0xd8)]=_0x1ca2fa,document['getElementById']('msg-contact')[_0x43c4fb(0xd8)]=_0x1ec34d;var _0x1c1deb=_0x43c4fb(0xc2)+_0x4110c0+_0x43c4fb(0xe1)+_0x1ca2fa+_0x43c4fb(0xe9)+_0x1ec34d+_0x43c4fb(0xc5);document[_0x43c4fb(0xdf)](_0x43c4fb(0xd4))['href']='data:text;base64,'+btoa(_0x1c1deb);}function selectElementContents(_0x1f8124){var _0x4b8f9b=_0x1eb04b,_0x3fa607=document['body'],_0x56420f,_0x43e220;if(document[_0x4b8f9b(0xce)]&&window[_0x4b8f9b(0xd7)]){_0x56420f=document['createRange'](),_0x43e220=window[_0x4b8f9b(0xd7)](),_0x43e220['removeAllRanges']();try{_0x56420f['selectNodeContents'](_0x1f8124),_0x43e220[_0x4b8f9b(0xe4)](_0x56420f);}catch(_0x178875){_0x56420f[_0x4b8f9b(0xc9)](_0x1f8124),_0x43e220['addRange'](_0x56420f);}}else _0x3fa607[_0x4b8f9b(0xc6)]&&(_0x56420f=_0x3fa607['createTextRange'](),_0x56420f['moveToElementText'](_0x1f8124),_0x56420f[_0x4b8f9b(0xd6)]());document['execCommand']('copy');}
	</script>
</body>
</html>