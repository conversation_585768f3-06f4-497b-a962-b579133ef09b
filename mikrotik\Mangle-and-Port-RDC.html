<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Mangle and Port Remote Desktop Connection (RDC) - MikroTik Script RouterOS</title>
<meta content='Mangle and Port Remote Desktop Connection (RDC) - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Mangle and Port Remote Desktop Connection (RDC) - MikroTik Script RouterOS</h1>
<pre>If you're in front of your computer and need to access a different machine, Microsoft's Remote Desktop Connection tool will let you access everything from afar. As long as the computer is turned on and set up for remote connection, you can grab a file, open an application, troubleshoot a problem, or just work remotely. 

Port forward Remote Desktop Connection:
TCP/UDP: 3389/3389

<code class="routeros">
/ip firewall mangle
add action=mark-connection chain=prerouting dst-port="3389" new-connection-mark=conn-RDC protocol=tcp
add action=mark-connection chain=prerouting dst-port="3389" new-connection-mark=conn-RDC protocol=udp
add action=mark-packet chain=prerouting connection-mark=conn-RDC new-packet-mark=RDC_pkt-up passthrough=no src-address=***********/24
add action=mark-packet chain=prerouting connection-mark=conn-RDC dst-address=***********/24 new-packet-mark=RDC_pkt-down passthrough=no</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>