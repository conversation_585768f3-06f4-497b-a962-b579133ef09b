<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset='utf-8' />
	<meta content='width=device-width, initial-scale=1, maximum-scale=1' name='viewport' />
	<title>Mikrotik Port Knocking Generator with Icmp + Packet Size - mikrotiktool.Github.io</title>
	<meta content='Mikrotik Port Knocking Generator with Icmp + Packet Size - mikrotiktool.Github.io' name='description' />
	<meta content='port knocking, knock, script generator, mikrotik, router, winbox, termimal, rsc, script, hotspot, wireless, rtrwnet' name='keywords' />
	<meta content='index, follow, noodp' name='robots' />
	<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon">
	<meta property="og:title" content="Mikrotik Port Knocking Generator with Icmp + Packet Size ">
	<meta property="og:description" content="Mikrotik Port Knocking Generator with Icmp + Packet Size ">
	<meta property="og:image" content="https://user-images.githubusercontent.com/42666125/111254047-cbcd6380-8646-11eb-9be9-c8edfd7bfe81.png">
	<meta property="og:image:alt" content="Mikrotik Port Knocking Generator with Icmp + Packet Size ">
	<meta property="og:type" content="website">
	<meta property="og:url" content="https://mikrotiktool.github.io/port-knocking-icmp.html"> 
	<script data-ad-client="ca-pub-3815059393374366" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
	<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M5TL99Q9M2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-M5TL99Q9M2');
</script>
	<style>
		
		*{-webkit-transition: all .3s ease-in;-moz-transition: all .3s ease-in;-ms-transition: all .3s ease-in;transition: all .3s ease-in;}
		body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table{margin:0; padding:0}
		body { font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; margin:20px;}
		html, body {
		height: 100%;
		width:100%;
		margin-top: 0px;
		padding:0;
		font-size:16px;
		}
		* {
		box-sizing: border-box;
		}
		.Menu{
		width:100%;
		border-bottom:1px solid #ccc;
		height:100%;
		padding-bottom: 15px;
		margin-bottom: 20px;
		font-weight:bold;
		 }
		#wrap{
		width:1120px;
		margin:0 auto;
		padding:10px;
		background:#fff
		}
		.logo {
		margin-top:0px;
		color: #111;
		text-decoration:none;
		font-size:27px;
		font-weight:bold; 
		}	
		.logo a{
		color:#111 !important;
		text-decoration:none !important;
		}
		.header{
		height:auto; 
		width:100%;
		margin-top:0px;
		margin-bottom:10px;
		border-top:1px solid #ccc;
		}
		.main-wrap{
		display:flex;
		}     
		.content{
        flex:1;
		float:right;
		width:800px;
		padding: 10px;
		border-top:1px solid #bbb;
		border-bottom:1px solid #bbb;
		border-right:1px solid #bbb;
		background:#ddd;
		height:auto;
		}
		.sidebar{
		float:left;
		width:300px;
		padding: 10px;
		background-color:#ddd;
		border:1px solid #bbb; 
		height:auto;
		}
		.footer{
        font-size:14px;
		padding-top:15px;
		clear:both;
		width:auto;
		}

		h1 {
		color:#111;
		font-size:24px;
		margin-bottom:5px;
		margin-top:10px;
		}
		a:link {
		color: #08c;
		}
		a:visited {
		color: #08c;
		}
		a:hover {
		color: #08c;
		}
		a:active {
		color: #08c;
		}
		.menu {
		clear:both;
		width:auto;
		padding-bottom:13px;
		}
		.menu1 {
		width:360px;
		margin-bottom:1px;
		font-weight:bold;
		background-color:#4CAF50;
		padding:6px;
		color:white;
		display: inline-block;
		margin:0;
		}
		.menu1 a:link {
		text-decoration:none;
		color:white;
		}
		.menu1 a:visited{
		text-decoration:none;
		color:white;
		}
		.menu2 {
		width:300px;
		margin-bottom:1px;
		font-weight:bold;
		background-color:#bbb;
		padding:6px;
		color:white;
		display: inline-block;
		margin:0;
		}
		.menu2 a:link{
		text-decoration:none;
		color:white;
		}
		.menu2 a:visited{
		text-decoration:none;
		color:white;
		}
		 button {
		 color: #fff;
		 background-color: #08c;
		 border-color: #08c;
		 border:none;
		 padding:7px;
		 width:200px;
		 font-size:16px !important;
		 font-weight:bold;
		 }
		 .d-link {
		 color: #fff;
		 background-color: #08c;
		 border-color: #08c;
		 border:none;
		 padding:7px;
		 width:200px;
		 font-size:16px !important;
		 font-weight:bold;
		 }		 
		 .d-link a:link{
		 color: #fff;
		 text-decoration:none;
		 }	
		 .d-link a:visited{
		 color: #fff;
		 text-decoration:none;
		 }	
		 .d-link a:hover {
		  color: #fff;
		}
		.d-link a:active {
		  color: #fff;
		}
		 
		 .row:after {
		 content: "";
		 display: table;
		 clear: both;
		 }  
		 input[type=text], select, textarea {
		 width: 100%;
		 padding: 5px;
		 border: 1px solid #bbb;
		 border-radius: 1px;
		 resize: vertical;
		 margin-bottom:12px;
		 font-size:16px !important;
		 }
		 label {
		 padding: 5px 5px 5px 0;
		 display: inline-block;
		 }
		 input[type=submit] {
		 background-color: #08c;
		 color: white;
		 padding: 12px 20px;
		 border: none;
		 border-radius: 1px;
		 cursor: pointer;
		 float: right;
		 }
		 input[type=submit]:hover {
		 background-color: #45a049;
		 }
		 .col-25 {
		 float: left;
		 width: 25%;
		 margin-top: 6px;
		 }
		 .col-75 {
		 float: left;
		 width: 75%;
		 margin-top: 6px;
		 }
		 /* Clear floats after the columns */
		 .row:after {
		 content: "";
		 display: table;
		 clear: both;
		 }
		 .list-game {
		 height: 200px;
		 background:white;
		 overflow: scroll;
		 overflow-x: hidden;
		 width: 100%;
		 margin-top:2px;
		 padding: 3px;
		 border: 1px solid rgba(0,0,0,0.25);
		 }
		 .list-game label {
		 padding: 0;
		 display: inline-block;
		 } 
		 .list-mangle {
		 height: 380px;
		 background:#fff;
		 overflow: auto;
		 overflow-x: auto;
		 width: 100%;
		 padding: 4px;
		 margin-top:8px;
		 border: 1px solid #ccc;
		 }
		 table, tr, td {
		border: none;
		}
		/* The Modal (background) */
		.modal {
		  display: none; /* Hidden by default */
		  position: fixed; /* Stay in place */
		  z-index: 1; /* Sit on top */
		  padding-top: 100px; /* Location of the box */
		  left: 0;
		  top: 0;
		  width: 100%; /* Full width */
		  height: 100%; /* Full height */
		  overflow: auto; /* Enable scroll if needed */
		  background-color: rgb(0,0,0); /* Fallback color */
		  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
		}
		/* Modal Content */
		.modal-content {
		  background-color: #fefefe;
		  margin: auto;
		  padding: 20px;
		  border: 1px solid #888;
		  width: 50%;
		  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
		}

		/* The Close Button */
		.close {
		  color: #aaaaaa;
		  float: right;
		  font-size: 28px;
		  font-weight: bold;
		}

		.close:hover,
		.close:focus {
		  color: #000;
		  text-decoration: none;
		  cursor: pointer;
		} 
	</style>
</head>
<body onLoad="callmodal()"> 
<div id="wrap">	
<div class="logo">	  
<a href="https://mikrotiktool.github.io"><span style="color:#08c !important">mikrotiktool</span>.Github.io</a> <img style="float:right; padding-top:10px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=hits&edge_flat=false" alt="Hits"/> 
</div>	
<div class="header">
<span style="float:left; display:inline;margin-right:6px; margin-top:10px">
<a href="https://mikrotiktool.github.io/port-knocking-icmp.html"><img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="HOME"></a>
</span>	
<h1>MIKROTIK PORT KNOCKING GENERATOR WITH ICMP + PACKET SIZE</h1>
</div>
<div class="main-wrap">
		<div class="sidebar">
			<label>First Icmp Packet Size <br>range: 50-65535<br><span style='color:#08c'>example: 100</span></label>
			<br>
			<input type="text" id="packet-size-key-first" placeholder="100" value="" onkeyup="myFunction()">
			<br>
			<label>Second Icmp Packet Size <br>range: 50-65535
			<br><span style='color:#08c'>example: 200</span></label>
			<br>
			<input type="text" id="packet-size-key-second" placeholder="200" value="" onkeyup="myFunction()">
			<br>
			<label>On Login Time Duration<br><span style='color:#08c'>example: 00:30:00 (30 minute)</span><br><span style='color:#08c'>example: 00:00:00 (unlimited)</span></label>
			<br>
			<input type="text" id="timeout-access" placeholder="00:30:00" value="00:00:00" onkeyup="myFunction()">
			<label>Port Service To Protected</label>
			<input type="text" id="port-services" placeholder="8291,21,22,23,80,443" value="8291,21,22,23,80,443" onkeyup="myFunction()">
			<label style="padding-left:0px">Default Port Services:</label>
			<br>
			-> <span style='color:#08c'>8728</span> (api)<br>
			-> <span style='color:#08c'>8729</span> (api-ssl)<br>
			-> <span style='color:#08c'>21</span> (ftp<br>
			-> <span style='color:#08c'>22</span> (ssh)<br>
			-> <span style='color:#08c'>23</span> (telnet)<br>
			-> <span style='color:#08c'>8291</span> (winbox)<br>
			-> <span style='color:#08c'>80</span> (www)<br>
			-> <span style='color:#08c'>443</span> (www-ssl)<br>
			-> <span style='color:#08c'>3128</span> (web proxy)<br>
		</div>
		<div class="content"> <div style="margin-left:2px; margin-top:5px">Script Generator Result</div>
			<div class="list-mangle">
			<div style="width:100%; height:90px; margin-top:10px; margin-left:10px; margin-bottom:5px">	
			<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
			<!-- MIKROTIK SCRIPT DB3 -->
			<ins class="adsbygoogle"
			style="display:inline-block;width:728px;height:90px"
			data-ad-client="ca-pub-3815059393374366"
			data-ad-slot="4637807296"></ins>
			<script>
			(adsbygoogle = window.adsbygoogle || []).push({});
			</script> 
			</div>
				<table id="showScript" style="padding:5px; ;">
					<tr>
						<td> <span style="color:#08c;">
							###############################################################<br>
							# Mikrotik Port Knocking Generator with Icmp + Packet Size<br>
							# Date/Time: <span id="tanggalwaktu"></span><br>
							#   - mikrotiktool.github.io<br>
							###############################################################<br>
							</span>	
							<b>/ip firewall filter</b><br>
							<span>add action=add-src-to-address-list address-list="port-knocking-first" address-list-timeout="<span style="color:#08c; font-weight:bold" id="timeout-access-first-list">00:00:00</span>" chain=input packet-size="<span style="color:#08c; font-weight:bold" id="first-key-list">0</span>" protocol=icmp comment="Port Knocking By BNT</span><br>
							<span>add action=add-src-to-address-list address-list="port-knocking-second" address-list-timeout="<span style="color:#08c; font-weight:bold" id="timeout-access-second-list">00:00:00</span>" chain=input packet-size="<span style="color:#08c; font-weight:bold" id="second-key-list">0</span>" protocol=icmp src-address-list="port-knocking-first"</span><br>
							<span>add action=accept chain=input dst-port="<span style='color:#08c; font-weight:bold' id="port-services-list">0</span>" protocol=tcp src-address-list="port-knocking-second"</span><br>
							<span>add action=drop chain=input dst-port="<span style='color:#08c; font-weight:bold' id="port-services-list2">0</span>" protocol=tcp src-address-list="!port-knocking-second"</span>
						</td>
					</tr>
				</table>
			</div>
			<br>
			Copy-Paste Firewall Script into the Terminal!
			<br><br>
 			<span style='color:#08c; font-weight:bold'>Unique Packet Size For Key Knocking: </span><span style="font-size:16px; font-weight:bold" id="packet-size-result1"></span> <span style='color:#08c; font-weight:bold'>and </span> <span style="font-size:16px; font-weight:bold" id="packet-size-result2"></span>  <span style="margin-left:20px" class="d-link"><a class="download" href="#" download="My-Knocking.bat">Download My-Knocking.bat</a></span><br><br>
			<b>Example Manually Open Key Ping in CMD Windows:</b><br>
			First Key Knock -> <span style='color:green;'>ping -l <span id='packet-size-win1'></span> (IP Adrress)</span><br>
			Second Key Knock -> <span style='color:green;'>ping -l <span id='packet-size-win2'></span> (IP Adrress)</span><br><br>
			<b>Example Manually Open Key Ping in Terminal Linux or MacOS:</b><br>
			First Key Knock -> <span style='color:green; '>ping -s <span id='packet-size-linux1'></span> (IP Adrress)</span><br>
			Second Key Knock -> <span style='color:green; '>ping -s <span id='packet-size-linux2'></span> (IP Adrress)</span><br><br>

		</div>
        </div>
		<div class="footer">
       <div style="margin-top:9px;float:right">
		© Copyright 2020-2021 <a href="https://mikrotiktool.github.io/">mikrotiktool.github.io</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a><br> Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a> | <a href="">My Facebook Page</a> | Creator: <a href="">Agus Ramadhani</a>
		</div>         
		<span style="color:red; font-weight:bold; font-size:20px;">Disclaimer!</span> We are not responsible for errors or damage or your router is locked due to deliberate or accidental factors, you should ask for help from a friend who understands trying it.   
	</div>
	</div>
 	 
	<script type="text/javascript">
var modal = document.getElementById("myModal");
var span = document.getElementsByClassName("close")[0];
function callmodal() {
  modal.style.display = "block";
}
span.onclick = function() {
  modal.style.display = "none";
}
window.onclick = function(event) {
  if (event.target == modal) {
    modal.style.display = "none";
  }
}
</script> 
	<script>
var _0x5aa9=['1MkUJTU','createRange','packet-size-result1','getElementById','150831PRSfVT','href','3encKRD','execCommand','305322gVQuAZ','createTextRange','port-services-list2','packet-size-linux2','addRange','packet-size-key-first','timeout-access-first-list','<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>','second-key-list','packet-size-result2','.download','timeout-access-second-list','\x20-n\x202\x20%ipaddress%\x0aping\x20-l\x20','</span>','getSelection','tanggalwaktu','695765NhCEJS','\x20-n\x202\x20%ipaddress%\x0aECHO.\x0aECHO\x20======================================\x0aECHO\x20**\x20Knocking\x20Done!\x20**\x0aECHO\x20======================================\x0aECHO.\x0aPAUSE\x20>NUL\x0a','data:text;base64,','packet-size-key-second','1218036vwBERm','innerHTML','1168735bDboRT','1UYzsRu','383338zeJknZ','packet-size-win1','value','selectNodeContents','timeout-access','port-services','packet-size-win2','removeAllRanges','1235473KtjHvM','body','querySelector'];var _0x6a77=function(_0x5c4d81,_0x506301){_0x5c4d81=_0x5c4d81-0x11c;var _0x5aa93e=_0x5aa9[_0x5c4d81];return _0x5aa93e;};var _0x466b69=_0x6a77;(function(_0x4efede,_0x5e1594){var _0xf2e85f=_0x6a77;while(!![]){try{var _0x17c280=parseInt(_0xf2e85f(0x131))+parseInt(_0xf2e85f(0x122))*parseInt(_0xf2e85f(0x121))+-parseInt(_0xf2e85f(0x12a))*parseInt(_0xf2e85f(0x12d))+-parseInt(_0xf2e85f(0x145))+-parseInt(_0xf2e85f(0x135))+-parseInt(_0xf2e85f(0x120))+-parseInt(_0xf2e85f(0x11e))*-parseInt(_0xf2e85f(0x133));if(_0x17c280===_0x5e1594)break;else _0x4efede['push'](_0x4efede['shift']());}catch(_0x5a1e41){_0x4efede['push'](_0x4efede['shift']());}}}(_0x5aa9,0xbf286));var packetsizekeyfirstbat=document['getElementById'](_0x466b69(0x13a))[_0x466b69(0x124)],packetsizekeysecondbat=document[_0x466b69(0x130)](_0x466b69(0x11d))[_0x466b69(0x124)],bat_source='@ECHO\x20OFF\x0a:PING\x0aECHO\x20============================================================================\x0aset\x20/p\x20ipaddress=Your\x20Router\x20IP\x20Address:\x20\x0aping\x20-l\x20'+(packetsizekeyfirstbat-0x1c)+_0x466b69(0x141)+(packetsizekeysecondbat-0x1c)+'\x20-n\x202\x20%ipaddress%\x0aECHO.\x0aECHO\x20======================================\x0aECHO\x20**\x20Knocking\x20Done!\x20**\x0aECHO\x20======================================\x0aECHO.\x0aPAUSE\x20>NUL\x0a';document[_0x466b69(0x12c)](_0x466b69(0x13f))[_0x466b69(0x132)]=_0x466b69(0x11c)+btoa(bat_source);var dt=new Date();document['getElementById'](_0x466b69(0x144))[_0x466b69(0x11f)]=dt['toLocaleString']();function myFunction(){var _0x5f05de=_0x466b69,_0x193349=document[_0x5f05de(0x130)]('packet-size-key-first')['value'],_0x3537ef=document[_0x5f05de(0x130)](_0x5f05de(0x11d))['value'],_0x2bef34='@ECHO\x20OFF\x0a:PING\x0aECHO\x20============================================================================\x0aset\x20/p\x20ipaddress=Your\x20Router\x20IP\x20Address:\x20\x0aping\x20-l\x20'+(_0x193349-0x1c)+_0x5f05de(0x141)+(_0x3537ef-0x1c)+_0x5f05de(0x146);document[_0x5f05de(0x12c)](_0x5f05de(0x13f))['href']=_0x5f05de(0x11c)+btoa(_0x2bef34);var _0x51671a=document[_0x5f05de(0x130)]('packet-size-key-first')['value'],_0x2a1ec9=document[_0x5f05de(0x130)](_0x5f05de(0x11d))[_0x5f05de(0x124)],_0x42981b=document[_0x5f05de(0x130)](_0x5f05de(0x126))['value'],_0x3d3958=document[_0x5f05de(0x130)]('timeout-access')[_0x5f05de(0x124)],_0x2a27bc=document[_0x5f05de(0x130)](_0x5f05de(0x127))[_0x5f05de(0x124)];document[_0x5f05de(0x130)]('first-key-list')['innerHTML']=_0x5f05de(0x13c)+_0x51671a+_0x5f05de(0x142),document[_0x5f05de(0x130)](_0x5f05de(0x13d))[_0x5f05de(0x11f)]=_0x5f05de(0x13c)+_0x2a1ec9+_0x5f05de(0x142),document['getElementById'](_0x5f05de(0x13b))[_0x5f05de(0x11f)]=_0x5f05de(0x13c)+_0x42981b+'</span>',document['getElementById'](_0x5f05de(0x140))['innerHTML']=_0x5f05de(0x13c)+_0x3d3958+_0x5f05de(0x142),document[_0x5f05de(0x130)]('port-services-list')[_0x5f05de(0x11f)]='<span\x20style=\x27color:#08c;\x20font-weight:bold\x27>'+_0x2a27bc+_0x5f05de(0x142),document[_0x5f05de(0x130)](_0x5f05de(0x137))[_0x5f05de(0x11f)]=_0x5f05de(0x13c)+_0x2a27bc+'</span>',document['getElementById'](_0x5f05de(0x12f))[_0x5f05de(0x11f)]=_0x51671a-0x1c,document['getElementById'](_0x5f05de(0x13e))['innerHTML']=_0x2a1ec9-0x1c,document[_0x5f05de(0x130)](_0x5f05de(0x123))[_0x5f05de(0x11f)]=_0x51671a-0x1c,document[_0x5f05de(0x130)](_0x5f05de(0x128))[_0x5f05de(0x11f)]=_0x2a1ec9-0x1c,document[_0x5f05de(0x130)]('packet-size-linux1')['innerHTML']=_0x51671a-0x1c,document[_0x5f05de(0x130)](_0x5f05de(0x138))['innerHTML']=_0x2a1ec9-0x1c;}function selectElementContents(_0x19d6ec){var _0x233bd6=_0x466b69,_0x2d9b5c=document[_0x233bd6(0x12b)],_0x3678a8,_0x4e5784;if(document[_0x233bd6(0x12e)]&&window[_0x233bd6(0x143)]){_0x3678a8=document['createRange'](),_0x4e5784=window[_0x233bd6(0x143)](),_0x4e5784[_0x233bd6(0x129)]();try{_0x3678a8[_0x233bd6(0x125)](_0x19d6ec),_0x4e5784[_0x233bd6(0x139)](_0x3678a8);}catch(_0x4f773c){_0x3678a8['selectNode'](_0x19d6ec),_0x4e5784[_0x233bd6(0x139)](_0x3678a8);}}else _0x2d9b5c[_0x233bd6(0x136)]&&(_0x3678a8=_0x2d9b5c[_0x233bd6(0x136)](),_0x3678a8['moveToElementText'](_0x19d6ec),_0x3678a8['select']());document[_0x233bd6(0x134)]('copy');}
	</script>
</body>
</html>