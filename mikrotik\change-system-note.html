<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Change System Note from Terminal - Mikrotik Script RouterOS</title>
<meta content='Change System Note from Terminal - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Change System Note from Terminal - Mikrotik Script RouterOS</h1>
<pre>Note - How To Change System Note from Terminal

Copy-Paste Script In to terminal
free ascii creator <a target="_blank" href="http://www.network-science.de/ascii" >http://www.network-science.de/ascii</a>

<code class="routeros">/system note
set note=" ______  _     _        ______         ______  _______ _______ \
    (____  \\| |   | |  /\\  |  ___ \\   /\\  |  ___ \\(_______|_______)\
     ____)  ) |   | | /  \\ | |   | | /  \\ | |   | |_____   _       \
    |  __  (| |   | |/ /\\ \\| |   | |/ /\\ \\| |   | |  ___) | |      \
    | |__)  ) |___| | |__| | |   | | |__| | |   | | |_____| |_____ \
    |______/ \\______|______|_|   |_|______|_|   |_|_______)\\______)\
    Protected  SECURE!\
    "</code>
 
EXAMPLE: 

 ______  _____ _    _ ______   _____  _______ _____ _    _ 
|  ___ \(_____) |  / |_____ \ / ___ \(_______|_____) |  / )
| | _ | |  _  | | / / _____) ) |   | |_         _  | | / / 
| || || | | | | |< < (_____ (| |   | | |       | | | |< <  
| || || |_| |_| | \ \      | | |___| | |_____ _| |_| | \ \ 
|_||_||_(_____)_|  \_)     |_|\_____/ \______|_____)_|  \_)


   _____  .___ ____  __.__________ ___________________.___ ____  __.
  /     \ |   |    |/ _|\______   \\_____  \__    ___/|   |    |/ _|
 /  \ /  \|   |      <   |       _/ /   |   \|    |   |   |      <  
/    Y    \   |    |  \  |    |   \/    |    \    |   |   |    |  \ 
\____|__  /___|____|__ \ |____|_  /\_______  /____|   |___|____|__ \
        \/            \/        \/         \/                     \/

 :::=======  ::: :::  === :::====  :::====  :::==== ::: :::  ===
 ::: === === ::: ::: ===  :::  === :::  === :::==== ::: ::: === 
 === === === === ======   =======  ===  ===   ===   === ======  
 ===     === === === ===  === ===  ===  ===   ===   === === === 
 ===     === === ===  === ===  ===  ======    ===   === ===  ===

                                                                            
88b           d88 88 88      a8P  88888888ba    ,ad8888ba, 888888888888 88 88      a8P   
888b         d888 88 88    ,88'   88      "8b  d8"'    `"8b     88      88 88    ,88'   
88`8b       d8'88 88 88  ,88"     88      ,8P d8'        `8b    88      88 88  ,88"      
88 `8b     d8' 88 88 88,d88'      88aaaaaa8P' 88          88    88      88 88,d88'      
88  `8b   d8'  88 88 8888"88,     88""""88'   88          88    88      88 8888"88,       
88   `8b d8'   88 88 88P   Y8b    88    `8b   Y8,        ,8P    88      88 88P   Y8b     
88    `888'    88 88 88     "88,  88     `8b   Y8a.    .a8P     88      88 88     "88,       
88     `8'     88 88 88       Y8b 88      `8b   `"Y8888Y"'      88      88 88       Y8b
  
 
M)mm mmm  I)iiii K)   kk  R)rrrrr   O)oooo  T)tttttt I)iiii K)   kk  
M)  mm  mm   I)   K)  kk   R)    rr O)    oo    T)      I)   K)  kk   
M)  mm  mm   I)   K)kkk    R)  rrr  O)    oo    T)      I)   K)kkk    
M)  mm  mm   I)   K)  kk   R) rr    O)    oo    T)      I)   K)  kk   
M)      mm   I)   K)   kk  R)   rr  O)    oo    T)      I)   K)   kk  
M)      mm I)iiii K)    kk R)    rr  O)oooo     T)    I)iiii K)    kk    
 
.___  ___.  __   __  ___ .______        ______   .___________. __   __  ___ 
|   \/   | |  | |  |/  / |   _  \      /  __  \  |           ||  | |  |/  / 
|  \  /  | |  | |  '  /  |  |_)  |    |  |  |  | `---|  |----`|  | |  '  /  
|  |\/|  | |  | |    <   |      /     |  |  |  |     |  |     |  | |    <   
|  |  |  | |  | |  .  \  |  |\  \----.|  `--'  |     |  |     |  | |  .  \  
|__|  |__| |__| |__|\__\ | _| `._____| \______/      |__|     |__| |__|\__\ 

Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
