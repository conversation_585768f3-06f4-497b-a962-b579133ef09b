<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Limiting a User To a Given Amount Of Traffic With User Levels - MikroTik Script RouterOS</title>
<meta content='Limiting a User To a Given Amount Of Traffic With User Levels - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Limiting a User To a Given Amount Of Traffic With User Levels - MikroTik Script RouterOS</h1>
<pre>If you need to limit users for amount of download and also need several user levels (different limits for groups of user), check this script.

<code class="routeros">/queue simple; :foreach i in=[find] \
do={  \
     :set sqName [get $i name]; \
     :set sqTotalBytes [get $i total-bytes]; \
     :set sqLevel [:find $sqName "\[LevelA\]"]; \
     :if ($sqLevel >= 0)  do={ \
        set $i limit-at=32000/32000 max-limit=64000/64000 burst-threshold=48000/48000 burst-limit=128000/128000 burst-time=30/30; \
        :if ($sqTotalBytes  > (100 * 1048576))  do = { \
          set $i limit-at=24000/24000 max-limit=32000/32000 burst-threshold=0/0 burst-limit=0/0 burst-time=0/0; \
        }; \
     } else {
       :set sqLevel [:find $sqName "\[LevelB\]"]; \
       :if ($sqLevel >= 0)  do={ \
           set $i limit-at=64000/64000 max-limit=128000/128000 burst-threshold=78000/78000 burst-limit=256000/256000 burst-time=30/30; \
           :if ($sqTotalBytes  > (200 * 1048576))  do = { \
             set $i limit-at=64000/64000 max-limit=64000/64000 burst-threshold=0/0 burst-limit=0/0 burst-time=0/0; \
           }; \
       } else { \
           :set sqLevel [:find $sqName "\[LevelC\]"]; \
           :if ($sqLevel >= 0)  do={ \
               set $i limit-at=72000/72000 max-limit=256000/256000 burst-threshold=96000/96000 burst-limit=1000000/1000000 burst-time=30/30; \
               :if ($sqTotalBytes  > (300 * 1048576))  do = { \
                  set $i limit-at=72000/72000 max-limit=72000/72000 burst-threshold=0/0 burst-limit=0/0 burst-time=0/0; \
                }; \
            }; \
        }; \
     }; \
     :if ($sqLevel >= 0) do { \
        :put ([get $i name] . " : " . [get $i limit-at] . " : " . [get $i max-limit]  . " : " . [get $i burst-limit]   . " : " . [get $i burst-threshold]  . " : " . [get $i burst-time]) ; \
     }; \
}</code>
This scripts checks names of simple queues and if it finds [LevelA], [LevelB] or [LevelC] in name, it sets specified parameters. It also checks total-bytes and if limit is reached it sets different parameters for a queue.

This allows you to set groups at will (it is easily expandable to needed number of groups, these three are just for an example) and easily put queue in needed group by entering group ID within name. This means you may keep custom simple queue names, just add Group ID in it.

If you want to change group for some simple queue, just change group id in that queue name.

This script should be scheduled in regular time periods, for instance every 30 minutes, and also there should be triger that resets total-bytes once a day. Recomendation is that reset is done about 2 AM.

Script runs but this is my the very first MT script for so it is possible it is not of greatest quality code. I hope some of experienced members will suggest if there are possible improvements in code.

How to reset counters:

For older MT versions:

<code class="routeros">/queue simple reset-counters</code>
For new versions:

<code class="routeros">/queue simple reset-counters-all</code>
Oh yes, if you use Mikrotik v3 or newer, than this script won't work (script syntax has been changed).

Try this one instead:

<code class="routeros">/queue simple; 
:foreach i in=[find] \
do={  \
     :local sqName [get $i name]; \
     :local sqTotalBytes [get $i total-bytes]; \
     :local sqLevel [:find $sqName "[LevelA]"]; \
     :if ($sqLevel >= 0)  do={ \
        set $i limit-at=32000/32000 max-limit=64000/64000 burst-threshold=48000/48000 burst-limit=128000/128000 burst-time=30/30; \
        :if ($sqTotalBytes  > (100 * 1048576))  do={ \
          set $i limit-at=24000/24000 max-limit=32000/32000 burst-threshold=0/0 burst-limit=0/0 burst-time=0/0; \
        }; \
     } else {
       :set sqLevel [:find $sqName "[LevelB]"]; \
       :if ($sqLevel >= 0)  do={ \
           set $i limit-at=64000/64000 max-limit=128000/128000 burst-threshold=78000/78000 burst-limit=256000/256000 burst-time=30/30; \
           :if ($sqTotalBytes  > (200 * 1048576))  do={ \
             set $i limit-at=64000/64000 max-limit=64000/64000 burst-threshold=0/0 burst-limit=0/0 burst-time=0/0; \
           }; \
       } else { \
           :set sqLevel [:find $sqName "[LevelC]"]; \
           :if ($sqLevel >= 0)  do={ \
               set $i limit-at=72000/72000 max-limit=256000/256000 burst-threshold=96000/96000 burst-limit=1000000/1000000 burst-time=30/30; \
               :if ($sqTotalBytes  > (300 * 1048576))  do={ \
                  set $i limit-at=72000/72000 max-limit=72000/72000 burst-threshold=0/0 burst-limit=0/0 burst-time=0/0; \
                }; \
            }; \

        }; \
     }; \
     :if ($sqLevel >= 0) do { \
        :put ([get $i name] . " : " . [get $i limit-at] . " : " . [get $i max-limit]  . " : " . [get $i burst-limit]   . " : " . [get $i burst-threshold]  . " : " . [get $i burst-time]) ; \
     }; \
}</code>
Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
