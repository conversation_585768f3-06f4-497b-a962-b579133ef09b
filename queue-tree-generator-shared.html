<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Queue Tree Script Generator For Mikrotik Plus Bandwidth Shared (UpTo) - mikrotiktool.Github.io</title>
<meta content='Queue Tree Script Generator For Mikrotik - mikrotiktool.Github.io' name='description'/>
<meta content='script generator, mikrotik, router, winbox, termimal, rsc, script, hotspot, wireless, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<meta property="og:title" content="Queue Tree Script Generator For Mikrotik Plus Bandwidth Shared (UpTo) ">
<meta property="og:description" content="Queue Tree Script Generator For Mikrotik Plus Bandwidth Shared (UpTo) ">
<meta property="og:image:alt" content="Queue Tree Script Generator For Mikrotik Plus Bandwidth Shared (UpTo) ">
<meta property="og:type" content="website">
<meta property="og:url" content="https://mikrotiktool.github.io/queue-tree-generator-shared.html"> 
<script data-ad-client="ca-pub-3815059393374366" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>	
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M5TL99Q9M2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-M5TL99Q9M2');
</script>
<style>
*{-webkit-transition: all .3s ease-in;-moz-transition: all .3s ease-in;-ms-transition: all .3s ease-in;transition: all .3s ease-in;}
body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table{margin:0; padding:0}
body { font-size:16px; font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; margin:20px;}
html, body {
height: 100%;
width:100%;
margin-top: 0px;
padding:0;
}
* {
box-sizing: border-box;
}
.logo {
margin-top:30px; 
color: #111;
text-decoration:none;
font-size:27px;
font-weight:bold; 
}	
.logo a{
color:#111 !important;
text-decoration:none !important;
}
.Menu{
width:100%;
border-bottom:1px solid #ccc;
height:100%;
padding-bottom: 15px;
margin-bottom: 20px;
font-size:14px;
font-weight:bold;
}
#wrap{
width:1220px;
margin:0 auto;
padding:10px;
background:#fff
}
.header{
height:auto; 
width:100%;
margin-top:0px;
margin-bottom:10px;
border-top:1px solid #ccc;
}
.main-wrap{
display: flex;
}   
.content{
flex:1;
float:right;
width:800px;
padding: 10px;
border-top:1px solid #bbb;
border-bottom:1px solid #bbb;
border-right:1px solid #bbb;
background:#ddd;
}
.sidebar{
float:left;
width:400px;
Padding-top:8px;
padding-left: 10px;
Padding-right:10px;
Padding-bottom:20px;
background-color:#ddd;
border:1px solid #bbb; 
height:auto;
}
.footer{
font-size:14px;
padding-top:15px;
text-align:center;
clear:both;
width:auto;
}

h1 {
color:#111;
font-size:22px;
margin-bottom:10px;
margin-top:10px;
}
a:link {
color: #ff6600;
}
a:visited {
color: #ff6600;
}
a:hover {
color: #ff6600;
}
a:active {
color: #ff6600;
}
.menu {
clear:both;
width:auto;
padding-bottom:13px;
}
.menu1 {
width:360px;
margin-bottom:1px;
font-weight:bold;
background-color:#4CAF50;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu1 a:link {
text-decoration:none;
color:white;
}
.menu1 a:visited{
text-decoration:none;
color:white;
}
.menu2 {
width:300px;
margin-bottom:1px;
font-weight:bold;
background-color:#bbb;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu2 a:link{
text-decoration:none;
color:white;
}
.menu2 a:visited{
text-decoration:none;
color:white;
}
button {
color: #fff;
background-color: #ff6600;
border-color: #ff6600;
border:none;
padding:8px;
width:177px;
font-size:16px;
font-weight:bold;
}
.row:after {
content: "";
display: table;
clear: both;
}  
input[type=checkbox]{
 margin-right:7px; 
}
input[type=text], select, textarea {
width: 100%;
padding: 5px;
border: 1px solid #bbb;
border-radius: 1px;
resize: vertical;
margin-bottom:12px;
font-size:16px !important;
}
label {
padding: 5px 5px 5px 0;
display: inline-block;
}
input[type=submit] {
background-color: #ff6600;
color: white;
padding: 12px 20px;
border: none;
border-radius: 1px;
cursor: pointer;
float: right;
}
input[type=submit]:hover {
background-color: #45a049;
}
.col-25 {
float: left;
width: 25%;
margin-top: 6px;
}
.col-75 {
float: left;
width: 75%;
margin-top: 6px;
}
/* Clear floats after the columns */
.row:after {
content: "";
display: table;
clear: both;
}
.list-game {
height: 200px;
background:white;
overflow: scroll;
overflow-x: hidden;
width: 100%;
margin-top:2px;
padding: 3px;
border: 1px solid rgba(0,0,0,0.25);
}
.list-game label {
padding: 0;
display: inline-block;
} 
.list-mangle {
height: 564px;
background:#fff;
overflow: scroll;
width: 100%;
padding: 4px;
margin-top:8px;
border: 1px solid #ccc;
}
table, tr, td {
border: none;
}
/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}
/* Modal Content */
.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
}

/* The Close Button */
.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
} 
</style>
</head>
<body onLoad="callmodal()"> 
<div id="wrap">
<div class="logo">	  
<a href="https://mikrotiktool.github.io"><span style="color:#ff6600 !important">mikrotiktool</span>.Github.io</a> <img style="float:right; padding-top:10px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=hits&edge_flat=false" alt="Hits"/>
</div>	
<div class="header">
<span style="float:left; display:inline;margin-right:6px; margin-top:10px">
<a href="https://mikrotiktool.github.io/queue-tree-generator-shared.html"><img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="HOME"></a>
</span>
<h1>QUEUE TREE SCRIPT GENERATOR MIKROTIK + BANDWIDTH SHARED (UPTO) - FOR IP SUBNET /24 ONLY</h1>
</div>
<div class="main-wrap">
    <div class="sidebar">
		<div style="margin-bottom:10px; height:190px">
		 <label>Parent Name Queue</label><div style="clear: both;"></div>
         <input type="text" id="input-parent" value="Global-Connection" placeholder="Global-Connection">
		 <div style="float:left; width:180px;">
		 <label>Sub Queue Upload</label><div style="clear: both;"></div>
         <input type="text" id="input-up-sub-parent" value="Upload" placeholder="Upload">
		 </div>
		 <div style="float:left; width:180px;margin-left:10px">
         <label>Sub Queue Download</label><div style="clear: both;"></div>
         <input type="text" id="input-down-sub-parent" value="Download" placeholder="Download">
		 </div>
		 <div style="float:left; width:180px;">
		 <label>Upload Max-Limit</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-up-parent" value="5M" placeholder="5M">
		 </div>
		 <div style="float:left; width:180px;margin-left:10px">
         <label>Download Max-Limit</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-down-parent" value="10M" placeholder="10M">
		 </div>
		 </div>
		 <div style="clear: both;"></div>
         <div style="margin-top:10px;margin-bottom:10px;border-bottom:1px solid #bbb">Sub Parent Queue / Child Queue</div>
		 <div style="float:left;width:180px;">
		 <label>Client Name Queue</label><div style="clear: both;"></div>
         <input type="text" id="input-client" value="Client-" placeholder="Client-">
		 </div>
		 <div style="float:left;width:180px;margin-left:10px;">
		 <label>Client Identity</label>
         <input type="text" id="input-client-initial" value="1" placeholder="1">
		 </div>
     	  <div style="float:left; width:180px;">
		 <label>Start IP Client</label><div style="clear: both;"></div>
         <input type="text" id="input-start-ip-client" value="*************" placeholder="*************">
		 </div>
		 <div style="float:left; width:180px;margin-left:10px;">
         <label>End IP Client</label><div style="clear: both;"></div>
         <input type="text" id="input-end-ip-client" value="*************" placeholder="*************">
		 </div>
     	 <div style="float:left; width:180px;">
		 <label>Upload Max-Limit</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-up-client" value="512K" placeholder="512K">
		 </div>
		 <div style="float:left; width:180px;margin-left:10px;">
         <label>Download Max-Limit</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-down-client" value="1M" placeholder="1M">
		 </div>
		 <div style="margin-bottom:10px; height:220px">
		 <div style="float:left; width:180px;">
		 <label>Upload Limit-At</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-limit-up" value="0" placeholder="0">
		 </div>
		 <div style="float:left; width:180px;margin-left:10px;">
         <label>Download Limit-At</label><div style="clear: both;"></div>
         <input onkeydown="upperCaseF(this)" type="text" id="input-limit-down" value="0" placeholder="0">
		 </div>

		 </div>
	<div style="margin-top:20px;margin-bottom:0px">
	 <input type="checkbox" id="auto-shared"><label for="auto-shared" style="padding-left:0px">Auto Set For Bandwidth Shared (UP-TO)</label><div style="clear: both;"></div>

         
	 </div>
         <button style="margin-top:20px;" type="button" onclick="myFunctionInput()">Generate Script</button> <button style="margin-left:10px" type="button" onclick="location.reload()">Clear All Script</button> 
         <div style="clear: both;"></div>
    </div>
    <div class="content"> 
        <span style="margin-left:2px">Script Generator Result</span>
         <div class="list-mangle">
		 <div style="width:100%; height:90px; margin-top:10px; margin-left:10px; margin-bottom:5px;">	
			<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
			<!-- MIKROTIK SCRIPT DB3 -->
			<ins class="adsbygoogle"
			style="display:inline-block;width:728px;height:90px"
			data-ad-client="ca-pub-3815059393374366"
			data-ad-slot="4637807296"></ins>
			<script>
			(adsbygoogle = window.adsbygoogle || []).push({});
			</script> 
		</div>
            <table id="showScript" style="padding:5px; white-space: nowrap;">
               <tr>
                  <td>
                     <span style="color:#ff6600;">
                     ####################################################################<br>
                     # Queue Tree Script Generator For Mikrotik + Bandwidth Shared (UpTo)<br>
                     # Date/Time: <span id="tanggalwaktu"></span><br>
                     # By mikrotiktool.Github.io +  <br>
                     ####################################################################<br><br>
                     </span>
					 <div id="list-output"></div>
				</td>
				</tr>			
			</table>
         </div>
         <br>
         <button style="margin-top:-5px" id="copyscript" type="button" onclick="selectElementContents(document.getElementById('showScript'))">Copy Script</button><span style="padding:7px; font-weight:normal;margin-left:10px"><b>Copy-Paste the Script into the Terminal!</b></span>
    </div>
</div>	
   <div class="footer">
© Copyright 2020-2021 <a href="https://mikrotiktool.github.io/">mikrotiktool.github.io</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a> - Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a>  
   </div>
 </div>
 	 
	<script type="text/javascript">
var modal = document.getElementById("myModal");
var span = document.getElementsByClassName("close")[0];
function callmodal() {
  modal.style.display = "block";
}
span.onclick = function() {
  modal.style.display = "none";
}
window.onclick = function(event) {
  if (event.target == modal) {
    modal.style.display = "none";
  }
}
</script>  
<script>  
var _0x5379=['438971toyKTd','toFixed','replace','moveToElementText','32uZKkzn','addRange','</span>\x22<br>','value','parseFloat','toUpperCase','input-parent','</span>\x22\x20name=\x22<span\x20style=\x22color:#ff6600;\x22>','1147432WUjTcH','4093idXvdZ','1175681iqnWCQ','input-end-ip-client','ceil','body','input-client-initial','lastIndexOf','<br>/ip\x20firewall\x20mangle<br>','2230sgbvFf','createTextRange','input-start-ip-client','auto-shared','input-down-parent','innerHTML','input-up-client','</span>\x22\x20name=\x22<span\x20style=\x22color:#ff6600;\x22>Up-','input-down-client','input-limit-up','select','removeAllRanges','</span>\x22\x20parent=\x22<span\x20style=\x22color:#ff6600;\x22>','match','add\x20max-limit=\x22<span\x20style=\x22color:#ff6600;\x22>','</span>\x22\x20packet-mark=\x22<span\x20style=\x22color:#ff6600;\x22>U-','onchange','disabled','list-output','36313JYwgpr','512K','2594794xgLLub','497UCiYRB','\x22</span>\x20passthrough=no\x20comment=\x22Download\x20for\x20IP\x20','</span>\x22\x20limit-at=\x22<span\x20style=\x22color:#ff6600;\x22>','copy','1fssdwx','getElementById','201JkBfCs','createRange','getSelection','input-limit-down','substring','input-client','</span>\x22\x20packet-mark=\x22<span\x20style=\x22color:#ff6600;\x22>D-','toLocaleString','\x22<br>','1rmWRtN','input-up-parent','input-down-sub-parent','execCommand','add\x20name=\x22<span\x20style=\x22color:#ff6600;\x22>'];var _0x2e93=function(_0x13ca99,_0x2a98d7){_0x13ca99=_0x13ca99-0xe5;var _0x537923=_0x5379[_0x13ca99];return _0x537923;};var _0x47989f=_0x2e93;(function(_0x5f2111,_0x2b6f6e){var _0x31f38d=_0x2e93;while(!![]){try{var _0x4f2bc6=-parseInt(_0x31f38d(0x10e))+-parseInt(_0x31f38d(0x102))+-parseInt(_0x31f38d(0xf4))*-parseInt(_0x31f38d(0x10f))+-parseInt(_0x31f38d(0xf2))*parseInt(_0x31f38d(0x110))+-parseInt(_0x31f38d(0x106))*parseInt(_0x31f38d(0xeb))+-parseInt(_0x31f38d(0xee))*-parseInt(_0x31f38d(0x117))+-parseInt(_0x31f38d(0xfd))*-parseInt(_0x31f38d(0xed));if(_0x4f2bc6===_0x2b6f6e)break;else _0x5f2111['push'](_0x5f2111['shift']());}catch(_0x127237){_0x5f2111['push'](_0x5f2111['shift']());}}}(_0x5379,0x92e61));var dt=new Date();document['getElementById']('tanggalwaktu')['innerHTML']=dt[_0x47989f(0xfb)]();function upperCaseF(_0xec1528){setTimeout(function(){var _0x189e3a=_0x2e93;_0xec1528[_0x189e3a(0x109)]=_0xec1528[_0x189e3a(0x109)][_0x189e3a(0x10b)]();},0x1);}function resultToFixed(_0x36456b){var _0x585650=_0x47989f;return Number[_0x585650(0x10a)](_0x36456b)[_0x585650(0x103)](0x2);}document['getElementById'](_0x47989f(0x11a))[_0x47989f(0xe8)]=function(){var _0x4e6d3b=_0x47989f,_0x182a0c=document['getElementById'](_0x4e6d3b(0x11a));if(_0x182a0c['checked']){var _0x229bdc=document[_0x4e6d3b(0xf3)](_0x4e6d3b(0x119))['value'],_0x299da4=document['getElementById'](_0x4e6d3b(0x111))['value'],_0x1ca06c=document[_0x4e6d3b(0xf3)]('input-up-parent')[_0x4e6d3b(0x109)],_0x52f0cb=document['getElementById'](_0x4e6d3b(0x11b))[_0x4e6d3b(0x109)],_0x252507=_0x229bdc,_0x44ede8=_0x252507[_0x4e6d3b(0x115)]('.'),_0x5b4ca2=_0x252507[_0x4e6d3b(0xf8)](_0x44ede8+0x1),_0x383518=_0x299da4,_0x4b058d=_0x383518[_0x4e6d3b(0x115)]('.'),_0x29e193=_0x383518['substring'](_0x4b058d+0x1),_0x263297=_0x229bdc[_0x4e6d3b(0xf8)](0x0,_0x229bdc['lastIndexOf']('.')),_0x2a1a75=_0x1ca06c,_0x324709=_0x2a1a75[_0x4e6d3b(0xe5)](/[a-zA-Z]/),_0xbdd971=_0x2a1a75[_0x4e6d3b(0x104)](/[^0-9]/g,''),_0x48ccb1=_0x52f0cb,_0x3e442b=_0x48ccb1[_0x4e6d3b(0xe5)](/[a-zA-Z]/),_0x1420d5=_0x48ccb1[_0x4e6d3b(0x104)](/[^0-9]/g,''),_0x44510e=0x400*_0xbdd971,_0x234d33=0x400*_0x1420d5,_0x5cbfb6=_0x44510e/(_0x29e193-_0x5b4ca2),_0x1b5a38=_0x234d33/(_0x29e193-_0x5b4ca2);document[_0x4e6d3b(0xf3)](_0x4e6d3b(0x11d))[_0x4e6d3b(0xe9)]=!![],document['getElementById'](_0x4e6d3b(0x11f))['disabled']=!![],document[_0x4e6d3b(0xf3)](_0x4e6d3b(0x120))[_0x4e6d3b(0xe9)]=!![],document[_0x4e6d3b(0xf3)](_0x4e6d3b(0xf7))[_0x4e6d3b(0xe9)]=!![],document[_0x4e6d3b(0xf3)](_0x4e6d3b(0x11d))[_0x4e6d3b(0x109)]=document['getElementById'](_0x4e6d3b(0xfe))['value'],document['getElementById'](_0x4e6d3b(0x11f))['value']=document['getElementById'](_0x4e6d3b(0x11b))[_0x4e6d3b(0x109)],document['getElementById']('input-limit-up')[_0x4e6d3b(0x109)]=Math[_0x4e6d3b(0x112)](_0x5cbfb6)+''+'K',document[_0x4e6d3b(0xf3)](_0x4e6d3b(0xf7))[_0x4e6d3b(0x109)]=Math['ceil'](_0x1b5a38)+''+'K',document[_0x4e6d3b(0xf3)](_0x4e6d3b(0xea))[_0x4e6d3b(0x11c)]='';}else document[_0x4e6d3b(0xf3)]('input-up-client')[_0x4e6d3b(0xe9)]=![],document['getElementById'](_0x4e6d3b(0x11f))[_0x4e6d3b(0xe9)]=![],document['getElementById'](_0x4e6d3b(0x120))[_0x4e6d3b(0xe9)]=![],document[_0x4e6d3b(0xf3)](_0x4e6d3b(0xf7))['disabled']=![],document['getElementById'](_0x4e6d3b(0x11d))['value']=_0x4e6d3b(0xec),document['getElementById'](_0x4e6d3b(0x11f))[_0x4e6d3b(0x109)]='1M',document[_0x4e6d3b(0xf3)](_0x4e6d3b(0x120))['value']='0',document[_0x4e6d3b(0xf3)]('input-limit-down')['value']='0',document[_0x4e6d3b(0xf3)](_0x4e6d3b(0xea))[_0x4e6d3b(0x11c)]='';};function myFunctionInput(){var _0x54a0f6=_0x47989f,_0x179aee='',_0x521ac3='',_0x5de83d='',_0x28f48b='',_0x4b8bbc='',_0x229aec,_0x25b88b=document[_0x54a0f6(0xf3)](_0x54a0f6(0x10c))['value'],_0x512ed1=document['getElementById']('input-up-sub-parent')['value'],_0x84227c=document['getElementById'](_0x54a0f6(0xff))[_0x54a0f6(0x109)],_0x473455=document['getElementById'](_0x54a0f6(0xfe))[_0x54a0f6(0x109)],_0xd023f=document[_0x54a0f6(0xf3)](_0x54a0f6(0x11b))['value'],_0x179aee=_0x54a0f6(0x101)+_0x25b88b+'</span>\x22\x20parent=\x22global\x22\x20comment=\x22Script\x20Generator\x20<br>\x20add\x20max-limit=\x22<span\x20style=\x22color:#ff6600;\x22>'+_0xd023f[_0x54a0f6(0x10b)]()+_0x54a0f6(0x10d)+_0x84227c+_0x54a0f6(0x123)+_0x25b88b+'</span>\x22<br>'+_0x54a0f6(0xe6)+_0x473455['toUpperCase']()+_0x54a0f6(0x10d)+_0x512ed1+_0x54a0f6(0x123)+_0x25b88b+'</span>\x22<br>',_0x82d6c3=document[_0x54a0f6(0xf3)](_0x54a0f6(0x114))['value'],_0x32d2fa=document[_0x54a0f6(0xf3)](_0x54a0f6(0x114))[_0x54a0f6(0x109)],_0x1d6ac8=document['getElementById']('input-client-initial')[_0x54a0f6(0x109)],_0x574774=document[_0x54a0f6(0xf3)](_0x54a0f6(0x114))[_0x54a0f6(0x109)],_0x1d9265=document[_0x54a0f6(0xf3)](_0x54a0f6(0x114))[_0x54a0f6(0x109)],_0x10d10c=document[_0x54a0f6(0xf3)](_0x54a0f6(0x114))[_0x54a0f6(0x109)],_0x265515=document['getElementById'](_0x54a0f6(0xf9))[_0x54a0f6(0x109)],_0x15d3f3=document[_0x54a0f6(0xf3)](_0x54a0f6(0x114))['value'],_0x7e95b3=document['getElementById'](_0x54a0f6(0x11d))['value'],_0xeb17c7=document[_0x54a0f6(0xf3)]('input-down-client')['value'],_0x581746=document[_0x54a0f6(0xf3)](_0x54a0f6(0x119))['value'],_0x39ceca=document[_0x54a0f6(0xf3)](_0x54a0f6(0x111))[_0x54a0f6(0x109)],_0x28f9b8=document['getElementById'](_0x54a0f6(0x120))[_0x54a0f6(0x109)],_0x2f488c=document[_0x54a0f6(0xf3)](_0x54a0f6(0xf7))[_0x54a0f6(0x109)],_0x476df2=_0x581746,_0x40b55d=_0x476df2[_0x54a0f6(0x115)]('.'),_0x11d896=_0x476df2['substring'](_0x40b55d+0x1),_0x561a62=_0x39ceca,_0x1dcbc9=_0x561a62['lastIndexOf']('.'),_0x19b755=_0x561a62['substring'](_0x1dcbc9+0x1),_0x438f97=_0x581746[_0x54a0f6(0xf8)](0x0,_0x581746[_0x54a0f6(0x115)]('.')),_0x4de36d=_0x473455,_0x3d13f9=_0x4de36d['match'](/[a-zA-Z]/),_0x58ee3e=_0x4de36d[_0x54a0f6(0x104)](/[^0-9]/g,''),_0x3c469c=_0xd023f,_0x311802=_0x3c469c[_0x54a0f6(0xe5)](/[a-zA-Z]/),_0x3445e4=_0x3c469c['replace'](/[^0-9]/g,''),_0x3387e8=0x400*_0x58ee3e,_0x2ec10c=0x400*_0x3445e4,_0x43980d=_0x3387e8/(_0x19b755-_0x11d896),_0x3c6f26=_0x2ec10c/(_0x19b755-_0x11d896),_0x3cf050=document[_0x54a0f6(0xf3)]('auto-shared');_0x3cf050['checked']&&(document['getElementById'](_0x54a0f6(0x11d))['value']=document[_0x54a0f6(0xf3)](_0x54a0f6(0xfe))[_0x54a0f6(0x109)],document[_0x54a0f6(0xf3)](_0x54a0f6(0x11f))[_0x54a0f6(0x109)]=document[_0x54a0f6(0xf3)]('input-down-parent')['value'],document['getElementById'](_0x54a0f6(0x120))['value']=Math[_0x54a0f6(0x112)](_0x43980d)+''+'K',document['getElementById'](_0x54a0f6(0xf7))[_0x54a0f6(0x109)]=Math[_0x54a0f6(0x112)](_0x3c6f26)+''+'K',document['getElementById'](_0x54a0f6(0xea))[_0x54a0f6(0x11c)]='');var _0x28f9b8=document[_0x54a0f6(0xf3)]('input-limit-up')[_0x54a0f6(0x109)],_0x2f488c=document[_0x54a0f6(0xf3)]('input-limit-down')[_0x54a0f6(0x109)];for(var _0x229aec=parseInt(_0x11d896);_0x229aec<=parseInt(_0x19b755);_0x229aec++){_0x521ac3+=_0x54a0f6(0xe6)+_0x7e95b3[_0x54a0f6(0x10b)]()+_0x54a0f6(0xf0)+_0x28f9b8+_0x54a0f6(0xe7)+_0x265515+_0x82d6c3++ +_0x54a0f6(0x11e)+_0x265515+_0x1d6ac8++ +_0x54a0f6(0x123)+_0x512ed1+_0x54a0f6(0x108),_0x5de83d+='add\x20max-limit=\x22<span\x20style=\x22color:#ff6600;\x22>'+_0xeb17c7['toUpperCase']()+_0x54a0f6(0xf0)+_0x2f488c+_0x54a0f6(0xfa)+_0x265515+_0x32d2fa++ +'</span>\x22\x20name=\x22<span\x20style=\x22color:#ff6600;\x22>Down-'+_0x265515+_0x574774++ +'</span>\x22\x20parent=\x22<span\x20style=\x22color:#ff6600;\x22>'+_0x84227c+_0x54a0f6(0x108),_0x28f48b+='add\x20action=mark-packet\x20chain=forward\x20src-address=\x22<span\x20style=\x22color:#ff6600;\x22>'+_0x438f97+'.'+_0x229aec+'</span>\x22\x20new-packet-mark=\x22<span\x20style=\x22color:#ff6600;\x22>U-'+_0x265515+_0x1d9265++ +'</span>\x22\x20passthrough=no\x20comment=\x22Upload\x20for\x20IP\x20'+_0x438f97+'.'+_0x229aec+'\x22<br>',_0x4b8bbc+='add\x20action=mark-packet\x20chain=forward\x20dst-address=\x22<span\x20style=\x22color:#ff6600;\x22>'+_0x438f97+'.'+_0x229aec+'\x22</span>\x20new-packet-mark=\x22<span\x20style=\x22color:#ff6600;\x22>D-'+_0x265515+_0x10d10c++ +_0x54a0f6(0xef)+_0x438f97+'.'+_0x229aec+_0x54a0f6(0xfc);}document[_0x54a0f6(0xf3)]('list-output')['innerHTML']='/queue\x20tree<br>'+_0x179aee+''+_0x521ac3+''+_0x5de83d+_0x54a0f6(0x116)+_0x28f48b+''+_0x4b8bbc;}function selectElementContents(_0x5d5c06){var _0xa7c288=_0x47989f,_0x22dee0=document[_0xa7c288(0x113)],_0xcb8e8f,_0x43527e;if(document[_0xa7c288(0xf5)]&&window['getSelection']){_0xcb8e8f=document[_0xa7c288(0xf5)](),_0x43527e=window[_0xa7c288(0xf6)](),_0x43527e[_0xa7c288(0x122)]();try{_0xcb8e8f['selectNodeContents'](_0x5d5c06),_0x43527e[_0xa7c288(0x107)](_0xcb8e8f);}catch(_0x4eb789){_0xcb8e8f['selectNode'](_0x5d5c06),_0x43527e['addRange'](_0xcb8e8f);}}else _0x22dee0[_0xa7c288(0x118)]&&(_0xcb8e8f=_0x22dee0[_0xa7c288(0x118)](),_0xcb8e8f[_0xa7c288(0x105)](_0x5d5c06),_0xcb8e8f[_0xa7c288(0x121)]());document[_0xa7c288(0x100)](_0xa7c288(0xf1));}
</script>
</body>
</html>