<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Port Game Online for PC - Mikrotik Script RouterOS</title>
<meta content='Port Game Online for PC - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>  
</head> 
<body>
<div id="hidelink"></div>
<h1>Port Game Online for PC - Mikrotik Script RouterOS</h1>
<pre>MIKROTIK PORT FORWARDING GAME ONLINE FOR PC GAMES

<code class="routeros">Port forward Game: Origin APEX Legends
TCP: 9960-9969,1024-1124,3216,18000,18120,18060,27900,28910,29900
UDP: 1024-1124,18000,29900,37000-40000

Port forward Game: PUBG PLAYERUNKNOWN’S BATTLEGROUNDS
UDP : 7080-8000

Port forward Game: ROE Ring of Elysium
TCP : 9002,10000-10015

Port forward Game: RULES OF SURVIVAL
UDP : 24000-24100

Port forward Game: FORTNITE EPICGAMES
Udp : 9000-9100

Counter-Strike: Global Offensive - Steam
TCP: 27015-27030,27036-27037
UDP: 4380,27000-27031,27036

Counter-Strike: Global Offensive - Xbox 360
TCP: 3074
UDP: 88,3074

Counter-Strike: Global Offensive - Playstation 3
TCP: 3478-3480,5223,8080
UDP: 3074,3478-3479,3658

Port forward Game: Atlantica Port Game:
TCP : 4300

Port forward Game: Aura Kingdom Port Game:
TCP :5540-5580

Port forward Game: Ayodance Port Game:
TCP : 18900-18910

Port forward Game: World in Ayodance Port Game:
TCP : 52510,53100-53110,54100,55100

Port forward Game: Blackretribution Steam Port Game:
UDP : 7020-7050,8200-8220,9000-9020

Port forward Game: Bounty Hound Port Game:
TCP : 9810-9860

Port forward Game: Clash of God Port Game:
TCP : 9430-9450,5220-5230

Port forward Game: Cabal Indonesia Port Game:
TCP : 63000-64000,38101,38110-38130

Port forward Game: Cabal Extreme Private Port Game:
TCP : 60170-60180,63000-64000,38101,38110-38600

Port forward Game: Cross Fire indonesia Port Game:
TCP : 10009,13008,16666,28012
UDP : 12020-12080,13000-13080

Port forward Game: Dragon Nest Indo Port Game:
TCP : 14300-15512
UDP : 15000-15500

Port forward Game: Dragona Port Game:
TCP : 10000-10030


BLIZZARD
=============
complete all blizzard port game:
TCP Ports - 80, 443, 1119, 1120, 3074, 3724, 4000, 6112-6120, 27014-27050
UDP Ports - 80, 443, 1119, 1120, 3478-3479, 3724, 4000, 4379-4380, 5060, 5062, 6112-6119, 6250, 27000-27031, 27036, 12000-64000

Blizzard Battle.net
TCP: 1119
UDP: 1119
Blizzard Voice Chat
TCP: 1119
UDP: 3478-3479, 5060, 5062, 6250, 12000-64000
Blizzard Downloader
TCP: 1119, 1120, 3724, 4000, 6112, 6113, 6114
UDP: 1119, 1120, 3724, 4000, 6112, 6113, 6114

Diablo
TCP: 6112-6119
UDP: 6112-6119
Diablo II
TCP:6112 and 4000
Diablo III
TCP: 1119
UDP: 1119, 6120

Hearthstone
TCP: 1119, 3724
UDP: 1119, 3724

Heroes of the Storm
TCP: 1119-1120, 3724, 6113
UDP: 1119-1120, 3478-3479, 3724, 5060, 5062, 6113, 6250, 12000-64000

Overwatch
TCP: 1119, 3724, 6113
UDP: 3478-3479, 5060, 5062, 6250, 12000-64000

StarCraft
TCP: 6112
UDP: 6112
StarCraft II
TCP: 1119, 6113, 1120, 80, 3724
UDP: 1119, 6113, 1120, 80, 3724

Warcraft II Battle.net Edition
TCP: 6112-6119
UDP: 6112-6119

Warcraft III
TCP: 6112 (Default) and 6113-6119

World of Warcraft
TCP: 3724, 1119, 6012
UDP: 3724, 1119, 6012

Call of Duty: Black Ops 4 PC:
TCP: 3074, 27014-27050
UDP: 3478, 4379-4380, 27000-27031, 27036
Call of Duty: Black Ops 4 PlayStation 4
TCP: 80,443,1935,3478-3480
UDP: 3478-3479
Call of Duty: Black Ops 4 XBox One
TCP: 53, 80, 3074
UDP: 53, 88, 500, 3074, 3075, 3544, 4500

Call of Duty: Modern Warfare PC
TCP: 3074, 27014-27050
UDP: 3074, 3478, 4379-4380, 27000-27031, 27036
Call of Duty: Modern Warfare PlayStation 4:
TCP: 80, 443, 1935, 3478-3480
UDP: 3074, 3478-3479
Call of Duty: Modern Warfare XBox One
TCP: 53, 80, 3074
UDP: 53, 88, 500, 3074, 3075, 3544, 4500


Black Desert Online (BDO) Port Game:
===============================================
Black Desert Online (BDO) - Steam
TCP: 8888,9991-9993,27015-27030,27036-27037
UDP: 4380,8888,9991-9993,27000-27031,27036

Black Desert Online (BDO) - PC
TCP: 8888,9991-9993
UDP: 8888,9991-9993

Black Desert Online (BDO) - Xbox One
TCP: 3074
UDP: 88,500,3074,3544,4500

Black Desert Online (BDO) - Playstation 4
TCP: 1935,3478-3480
UDP: 3074,3478-3479
===============================================

Dota 2 Steam Port Game:
----------------------------------------------------------------
Port forward Game: Dota 2 Steam Port Game:
TCP : 9100-9200,8230-8250,8110-8120
UDP : 28010-28200,27010-27200,39000

Tambahan:
Steam Client
UDP 27000 to 27015 inclusive (Game client traffic)
UDP 27015 to 27030 inclusive (Typically Matchmaking and HLTV)
TCP 27014 to 27050 inclusive (Steam downloads)
UDP 4380
Dedicated or Listen Servers
TCP 27015 (SRCDS Rcon port)
Steamworks P2P Networking and Steam Voice Chat
UDP 3478 (Outbound)
UDP 4379 (Outbound)
UDP 4380 (Outbound)
-----------------------------------------------------------------

Port forward Game: Grand Chase Port Game:
TCP : 9300,9400,9700
UDP : 9401,9600,16440-16450

Port forward Game: Garena League of Legend (LOL) Port Game:
TCP : 2080-2099
UDP : 5100

Port forward Game: Fifa Online 3 Garena (FOL3) Port Game:
TCP: 7770-7790
UDP: 16300-16350

Port forward Game: Hon Port Game:
UDP : 9100-9200,11200-11500

Port forward Game: Heroes of Atarsia Port Game:
TCP : 7777,9400

Port forward Game: Idol Street Port Game:
TCP : 2001-2010

Port forward Game: Left4Dead 2 Steam Port Game:
UDP : 4360-4390

Port forward Game: Lineage 2 Port Game:
TCP : 7777,10000,11000,13000

Port forward Game: Lost Saga Port Game:
TCP : 14000-14010
UDP : 14000-14010

Port forward Game: Lune of eden Port Game:
TCP : 8400

Port forward Game: Mircovolt Port Game:
TCP : 13000

Port forward Game: Mercenary ops Port Game:
TCP : 6000-6125

Port forward Game: Modo marble Port Game:
TCP : 28900-28914

Port forward Game: Paradins Hi-Rez (steam) games ports:
TCP : 9000-9999
UDP : 9002-9999

Port forward Game: Point blank Indonesia Port Game:
TCP : 39190-39200
UDP : 40000-40010

Port forward Game: Ragnarok 2 Port Game:
TCP : 7201-7210,7401-7410

Port forward Game: Seal Online :
TCP : 1818

Port forward Game: RF Online Port Game:
TCP : 27780

Port forward Game: Special Force Port Game:
TCP : 27920-27940
UDP : 30000-30030

Port forward Game: WARFRAME (steam) games ports:
UDP : 4950-4955
TCP : 6695-6699

Port forward Game: World of Tanks games ports:
UDP Range 12000-29999, 32801-32825, and UDP 5060, 5062, 3478, 3479, 20014
TCP Range 20000-25000, and TCP 53, 80, 443, 3128, 8081, 8088, 32801, 32803.

Port forward Game: X-shot Indonesia Port Game:
TCP : 7320-7350
UDP : 7800-7850, 30000</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
