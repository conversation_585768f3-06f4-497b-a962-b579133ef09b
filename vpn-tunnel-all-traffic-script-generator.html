<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>VPN Tunnel All Traffic Script Generator For Mikrotik Routeros - mikrotiktool.Github.io</title>
<meta content='VPN Tunnel All Traffic Script Generator For Mikrotik Routeros  - mikrotiktool.Github.io' name='description'/>
<meta content='script generator, mikrotik, router, winbox, termimal, rsc, script, hotspot, wireless, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon">
<meta property="og:title" content="VPN Tunnel All Traffic Script Generator ">
<meta property="og:description" content="VPN Tunnel All Traffic Script Generator ">
<meta property="og:image:alt" content="VPN Tunnel All Traffic Script Generator ">
<meta property="og:type" content="website">
<meta property="og:url" content="https://mikrotiktool.github.io/vpn-tunnel-all-traffic-script-generator.html"> 
<script data-ad-client="ca-pub-3815059393374366" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>	
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-M5TL99Q9M2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-M5TL99Q9M2');
</script>
<style>
*{-webkit-transition: all .3s ease-in;-moz-transition: all .3s ease-in;-ms-transition: all .3s ease-in;transition: all .3s ease-in;}
body, div, span, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, textarea, p, blockquote, th, td, tr, table{margin:0; padding:0}
body { font-family:-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; margin:20px;}
html, body {
height: 100%;
width:100%;
margin-top: 0px;
padding:0;
font-size:16px;
}
* {
box-sizing: border-box;
}
.Menu{
width:100%;
border-bottom:1px solid #ccc;
height:100%;
padding-bottom: 15px;
margin-bottom: 20px;
font-size:14px;
font-weight:bold;
 }
.logo {
margin-top:20px;
color: #111;
text-decoration:none;
font-size:27px;
font-weight:bold; 
}	
.logo a{
color:#111 !important;
text-decoration:none !important;
}
#wrap{
width:1120px;
margin:0 auto;
padding:10px;
background:#fff
}
.header{
height:auto; 
width:100%;
margin-top:0px;
margin-bottom:10px;
border-top:1px solid #ccc;
}
.main-wrap{
display:flex;
}     
.content{
flex:1;
float:right;
width:800px;
padding: 10px;
border-top:1px solid #ccc;
border-bottom:1px solid #ccc;
border-right:1px solid #ccc;
background:#eee;
height: auto;
}
.sidebar{
float:left;
width:300px;
Padding-top:8px;
padding-left: 10px;
Padding-right:10px;
Padding-bottom:20px;
background-color:#eee;
border:1px solid #ccc; 
height: auto;
}
.footer{
padding-top:15px;
clear:both;
width:auto;
font-size:14px;
}
h1 {
color:#111;
font-size:22px;
margin-bottom:5px;
margin-top:10px;
}
a:link {
  color: #154c79;
}
a:visited {
  color: #154c79;
}
a:hover {
  color: #154c79;
}
a:active {
  color: #154c79;
}
.menu {
margin-bottom:13px;
clear:both;
width:auto;
}
.menu1 {
width:300px;
margin-bottom:1px;
font-weight:bold;
background-color:#154c79;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu1 a:link {
text-decoration:none;
color:white;
}
.menu1 a:visited{
text-decoration:none;
color:white;
}
.menu2 {
width:360px;
margin-bottom:1px;
font-weight:bold;
background-color:#ff6600;
padding:6px;
color:white;
display: inline-block;
margin:0;
}
.menu2 a:link{
text-decoration:none;
color:white;
}
.menu2 a:visited{
text-decoration:none;
color:white;
}
 button {
 color: #fff;
 background-color: #154c79;
 border-color: #154c79;
 border:none;
 padding:8px;
 width:135px;
 font-size:16px;
 font-weight:bold;
 }
 .row:after {
 content: "";
 display: table;
 clear: both;
 }  
 input[type=checkbox]{
 margin-right:7px; 
}
 input[type=text], select, textarea {
 width: 100%;
 padding: 5px;
 border: 1px solid #ccc;
 border-radius: 1px;
 resize: vertical;
 font-size:16px !important;
 }
 label {
 padding: 5px 5px 5px 5px;
 display: inline-block;
 }
 input[type=submit] {
 background-color: #154c79;
 color: white;
 padding: 13px 20px;
 border: none;
 border-radius: 1px;
 cursor: pointer;
 float: right;
 }
 input[type=submit]:hover {
 background-color: #45a049;
 }
 .col-25 {
 float: left;
 width: 25%;
 margin-top: 6px;
 }
 .col-75 {
 float: left;
 width: 75%;
 margin-top: 6px;
 }
 /* Clear floats after the columns */
 .row:after {
 content: "";
 display: table;
 clear: both;
 }
 .list-game {
 height: 200px;
 background:white;
 overflow: scroll;
 overflow-x: hidden;
 width: 100%;
 margin-top:2px;
 padding: 3px;
 border: 1px solid rgba(0,0,0,0.25);
 }
 .list-game label {
 padding: 0;
 display: inline-block;
 } 
 .list-mangle {
 height: 466px;
 background:#fff;
 overflow: scroll;
 width: 100%;
 padding: 4px;
 margin-top:8px;
 border: 1px solid #ccc;
 }
 table, tr, td {
border: none;
}
/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 100px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}
/* Modal Content */
.modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 20px;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 3px 10px 0 rgba(0, 0, 0, 0.19);
}

/* The Close Button */
.close {
  color: #aaaaaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
} 
</style>
<script src="https://platform-api.sharethis.com/js/sharethis.js#property=5fa97f556a73380012578d69&product=video-share-buttons" async="async"></script>
</head>
<body onLoad="callmodal()">  
<div id="wrap">	
<div class="logo">	  
<a href="https://mikrotiktool.github.io"><span style="color:#154c79 !important">mikrotiktool</span>.Github.io</a> <img style="float:right; padding-top:10px" src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=hits&edge_flat=false" alt="Hits"/> 
</div>	
<div class="header">
<span style="float:left; display:inline;margin-right:6px; margin-top:10px">
<a href="https://mikrotiktool.github.io/vpn-game-generator.html"><img src="https://mikrotiktool.github.io/mikrotik/img/logo.png" alt="HOME"></a>
</span>
<h1>VPN TUNNEL ALL TRAFFIC SCRIPT GENERATOR FOR MIKROTIK ROUTEROS</h1> 
</div>
<div class="main-wrap">
    <div class="sidebar">
         <label>Select VPN Connection</label><div style="clear: both;"></div><div style="clear: both;"></div>
         <select onchange="myFunction()" id="vpn-list">
            <option value="pptp">PPTP - Point to Point Tunneling Protocol</option>
            <option value="l2tp">L2TP - Layer Two Tunneling Protocol</option>
         </select>
         <div style="clear: both;"></div><br>
         <label>Create VPN Name on Interface VPN</label><div style="clear: both;"></div>
         <input type="text" id="vpn-name" value="VPN-TUNNEL" placeholder="VPN-TUNNEL"><div style="clear: both;"></div><br>
         <label>VPN IP Address (don't use host name)</label><div style="clear: both;"></div>
         <input type="text" id="vpn-ip" placeholder="example:**************"><div style="clear: both;"></div><br>
         <label>VPN Username</label><div style="clear: both;"></div>
         <input type="text" id="vpn-username" placeholder="username"><div style="clear: both;"></div><br>
         <label>VPN Password</label><div style="clear: both;"></div>
         <input type="text" id="vpn-password" placeholder="password"><div style="clear: both;"></div><br>
	      <label style="padding-left:0px">Target IP Gateway Your ISP</label>
         <input type="text" id="ip-gateway-input" placeholder="example:***********"><div style="clear: both;"></div>
         <br><br>
         <button style="margin-right:2px" type="button" onclick="myFunction()">Generate</button> 
		 <button type="button" onclick="location.reload()">Clear All</button> 
         <br>
    </div>
    <div class="content">
        <span style="margin-left:2px">Script Generator Results</span>
         <div class="list-mangle">
		<div style="width:100%; height:90px; margin-top:10px; margin-left:10px; margin-bottom:5px;">	
			<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
			<!-- MIKROTIK SCRIPT DB3 -->
			<ins class="adsbygoogle"
			style="display:inline-block;width:728px;height:90px"
			data-ad-client="ca-pub-3815059393374366"
			data-ad-slot="4637807296"></ins>
			<script>
			(adsbygoogle = window.adsbygoogle || []).push({});
			</script> 
		</div>
            <table id="showScript" style="padding:5px;">
               <tr>
                  <td>
                     <span style="color:#154c79;">
                     ###################################################################<br>
                     # VPN Tunnel All Traffic Script Generator By mikrotiktool.Github.io<br>
                     # Date/Time: <span id="tanggalwaktu"></span><br>
                     # mikrotiktool.Github.io +  <br>
                     # VPN Type -> <span id="vpn-list-text-info" style="font-weight:bold;text-transform: uppercase">PPTP</span><br>
                     ###################################################################<br>
                     </span>
                     <span style="color:black;font-weight:bold">/interface <span id="vpn-list-text" style="color:#154c79; font-weight:bold" >pptp</span>-client</span><br>
                     add connect-to="<span id="vpn-ip-text" style="color:#154c79; font-weight:bold">x.x.x.x</span>" disabled=no name="<span id="vpn-name-text1" style="color:#154c79; font-weight:bold">VPN-TUNNEL</span>" user="<span id="vpn-username-text" style="color:#154c79; font-weight:bold" >xxxxxx</span>" password="<span id="vpn-password-text" style="color:#154c79; font-weight:bold">xxxxxx</span>" comment="<span id="vpn-name-text4">VPN-TUNNEL-BY-BNT</span>"<br>
                     <span style="color:black;font-weight:bold">/ip firewall nat</span><br>
                     add chain=srcnat out-interface="<span id="vpn-name-text2" style="color:#154c79; font-weight:bold">VPN-TUNNEL</span>" action=masquerade comment="<span id="vpn-name-text5">VPN-TUNNEL-BY-BNT</span>"<br>
                     <span style="color:black;font-weight:bold">/ip route</span><br>
                     add gateway="<span id="vpn-name-text3" style="color:#154c79; font-weight:bold">VPN-TUNNEL</span>" distance=1 comment="<span id="vpn-name-text6">VPN-TUNNEL-BY-BNT</span>"<br>
                     <span id="ip-gateway-text" ">
                     add dst-address="<span id="vpn-ip-text2" style="color:#154c79; font-weight:bold" >x.x.x.x.x</span>" distance=1 gateway="<span id="ip-gateway-game" style="color:#154c79; font-weight:bold">x.x.x.x.x</span>" comment="<span id="vpn-name-text7">VPN-TUNNEL-BY-BNT</span>"
				</td>
				</tr>			
			</table>
         </div>
         <br>
         <button id="copyscript" type="button" onclick="selectElementContents(document.getElementById('showScript'))">Copy Script</button><span style="padding:7px; font-weight:normal;margin-left:10px"><b>Copy-Paste Script to Terminal!</b></span>
    </div>
     </div>
   <div class="footer">
<div style="float:right">
© Copyright 2020-2021 <a href="https://mikrotiktool.github.io/">mikrotiktool.github.io</a> is proudly powered by <a href="https://pages.github.com/">GitHub Pages</a><br> Don't Forget to Follow <a href="https://github.com/mikrotiktool">My Github</a>  
</div>    
<span style="color:#154c79">
<b># This Script for remove all Script from this Generator</b></span><br>
/interface pptp-client remove [find comment="VPN-TUNNEL-BY-BNT"]<br>
/interface l2tp-client remove [find comment="VPN-TUNNEL-BY-BNT"]<br>
/ip route remove [find comment="VPN-TUNNEL-BY-BNT"]<br>
/ip firewall nat remove [find comment="VPN-TUNNEL-BY-BNT"]<br>
</div> 
</div>
 	 	 
	<script type="text/javascript">
var modal = document.getElementById("myModal");
var span = document.getElementsByClassName("close")[0];
function callmodal() {
  modal.style.display = "block";
}
span.onclick = function() {
  modal.style.display = "none";
}
window.onclick = function(event) {
  if (event.target == modal) {
    modal.style.display = "none";
  }
}
</script> 
<script>  
var _0x2fcc=['innerHTML','display','vpn-ip','checked','subnet','select','gateway','vpn-ip-text','733709SOejrs','1JVVRJS','<span\x20style=\x27color:red;\x20font-weight:bold\x27>[don\x27t\x20be\x20empty]<span>','Attention!\x20IP\x20Address\x20Validation\x20Incorrect!\x20Please\x20check\x20your\x20IP\x20address\x20again!','31286xOLBEl','vpn-name','ip-gateway-game','myTable','getElementById','none','selectNode','403097PmePHr','innerText','length','5nZYLDI','vpn-name-text1','toLocaleString','execCommand','IP\x20Address','ip-gateway-input','subnet\x20mask','moveToElementText','vpn-username','92000gCTpeb','block','value','vpn-list-text-info','3GTUgoD','myInput','22213jMTeDl','vpn-name-text2','getSelection','selectNodeContents','772153xpYXGY','vpn-ip-text2','vpn-username-text','vpn-list','focus','268881irjfre','dns','style','createTextRange','getElementsByTagName','textContent','toUpperCase','createRange','vpn-password','tanggalwaktu','ipaddress'];var _0x30e56e=_0x40f1;(function(_0x687835,_0x5a9248){var _0x580374=_0x40f1;while(!![]){try{var _0x361dbb=-parseInt(_0x580374(0x1a6))+parseInt(_0x580374(0x172))+parseInt(_0x580374(0x186))*parseInt(_0x580374(0x185))+parseInt(_0x580374(0x190))+-parseInt(_0x580374(0x19c))+parseInt(_0x580374(0x189))*parseInt(_0x580374(0x1a0))+parseInt(_0x580374(0x193))*-parseInt(_0x580374(0x1a2));if(_0x361dbb===_0x5a9248)break;else _0x687835['push'](_0x687835['shift']());}catch(_0x5a7806){_0x687835['push'](_0x687835['shift']());}}}(_0x2fcc,0x80027));var dt=new Date();document['getElementById'](_0x30e56e(0x17b))['innerHTML']=dt[_0x30e56e(0x195)]();function myFunction(){var _0x29d724=_0x30e56e,_0x4737d1=document[_0x29d724(0x18d)](_0x29d724(0x1a9))['value'];document['getElementById']('vpn-list-text')[_0x29d724(0x17d)]=_0x4737d1,document['getElementById'](_0x29d724(0x19f))[_0x29d724(0x17d)]=_0x4737d1;var _0x2db22a=document[_0x29d724(0x18d)](_0x29d724(0x18a))['value'];_0x2db22a!=''&&_0x2db22a!=null?(document['getElementById']('vpn-name-text1')['innerHTML']=_0x2db22a,document[_0x29d724(0x18d)](_0x29d724(0x1a3))[_0x29d724(0x17d)]=_0x2db22a,document['getElementById']('vpn-name-text3')[_0x29d724(0x17d)]=_0x2db22a):(document['getElementById'](_0x29d724(0x194))[_0x29d724(0x17d)]=_0x29d724(0x187),document['getElementById']('vpn-name-text2')['innerHTML']=_0x29d724(0x187),document['getElementById']('vpn-name-text3')['innerHTML']=_0x29d724(0x187));var _0xad8e54=document[_0x29d724(0x18d)](_0x29d724(0x17f))[_0x29d724(0x19e)];_0xad8e54!=''&&_0xad8e54!=null?(document['getElementById']('vpn-ip-text')['innerHTML']=_0xad8e54,document['getElementById'](_0x29d724(0x1a7))[_0x29d724(0x17d)]=_0xad8e54):(document['getElementById'](_0x29d724(0x184))[_0x29d724(0x17d)]=_0x29d724(0x187),document['getElementById']('vpn-ip-text2')['innerHTML']='<span\x20style=\x27color:red;\x20font-weight:bold\x27>[don\x27t\x20be\x20empty]<span>');var _0x2c210e=document['getElementById'](_0x29d724(0x19b))['value'];_0x2c210e!=''&&_0x2c210e!=null?document['getElementById'](_0x29d724(0x1a8))[_0x29d724(0x17d)]=_0x2c210e:document['getElementById']('vpn-username-text')[_0x29d724(0x17d)]=_0x29d724(0x187);var _0x2f872c=document[_0x29d724(0x18d)](_0x29d724(0x17a))[_0x29d724(0x19e)];_0x2f872c!=''&&_0x2f872c!=null?document[_0x29d724(0x18d)]('vpn-password-text')['innerHTML']=_0x2f872c:document[_0x29d724(0x18d)]('vpn-password-text')[_0x29d724(0x17d)]=_0x29d724(0x187);var _0x24de6b=document['getElementById'](_0x29d724(0x198))[_0x29d724(0x19e)];_0x24de6b!=''&&_0x24de6b!=null?document[_0x29d724(0x18d)](_0x29d724(0x18b))[_0x29d724(0x17d)]=_0x24de6b:document[_0x29d724(0x18d)]('ip-gateway-game')[_0x29d724(0x17d)]=_0x29d724(0x187);}function selectElementContents(_0xce841f){var _0x1a9c6a=_0x30e56e,_0x480b41=document['body'],_0x1a78d8,_0x5bcfc1;if(document[_0x1a9c6a(0x179)]&&window[_0x1a9c6a(0x1a4)]){_0x1a78d8=document['createRange'](),_0x5bcfc1=window['getSelection'](),_0x5bcfc1['removeAllRanges']();try{_0x1a78d8[_0x1a9c6a(0x1a5)](_0xce841f),_0x5bcfc1['addRange'](_0x1a78d8);}catch(_0x4644b1){_0x1a78d8[_0x1a9c6a(0x18f)](_0xce841f),_0x5bcfc1['addRange'](_0x1a78d8);}}else _0x480b41[_0x1a9c6a(0x175)]&&(_0x1a78d8=_0x480b41[_0x1a9c6a(0x175)](),_0x1a78d8[_0x1a9c6a(0x19a)](_0xce841f),_0x1a78d8[_0x1a9c6a(0x182)]());document[_0x1a9c6a(0x196)]('copy');}function toggleSub(_0x2be445,_0x2b0b65){var _0x520773=_0x30e56e,_0x468c2e=document[_0x520773(0x18d)](_0x2b0b65);_0x2be445[_0x520773(0x180)]?_0x468c2e[_0x520773(0x174)][_0x520773(0x17e)]=_0x520773(0x19d):_0x468c2e[_0x520773(0x174)]['display']=_0x520773(0x18e);}function _0x40f1(_0x127874,_0x6abeb8){_0x127874=_0x127874-0x171;var _0x2fcce3=_0x2fcc[_0x127874];return _0x2fcce3;}function myFunctionGames(){var _0x30c470=_0x30e56e,_0x278489,_0x12ea95,_0x38ee0a,_0x5258f1,_0x52b8e9,_0x40f187,_0x35c4c5;_0x278489=document[_0x30c470(0x18d)](_0x30c470(0x1a1)),_0x12ea95=_0x278489[_0x30c470(0x19e)]['toUpperCase'](),_0x38ee0a=document['getElementById'](_0x30c470(0x18c)),_0x5258f1=_0x38ee0a[_0x30c470(0x176)]('tr');for(_0x40f187=0x0;_0x40f187<_0x5258f1[_0x30c470(0x192)];_0x40f187++){_0x52b8e9=_0x5258f1[_0x40f187][_0x30c470(0x176)]('td')[0x0],_0x52b8e9&&(_0x35c4c5=_0x52b8e9[_0x30c470(0x177)]||_0x52b8e9[_0x30c470(0x191)],_0x35c4c5[_0x30c470(0x178)]()['indexOf'](_0x12ea95)>-0x1?_0x5258f1[_0x40f187][_0x30c470(0x174)]['display']='':_0x5258f1[_0x40f187][_0x30c470(0x174)][_0x30c470(0x17e)]=_0x30c470(0x18e));}}function ValidateIPaddressOnChange(_0x34fdd9,_0x2a0fb2){var _0x16a5c8=_0x30e56e,_0xdca6be=/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,_0x21e392='';switch(_0x2a0fb2){case _0x16a5c8(0x17c):_0x21e392=_0x16a5c8(0x197);break;case _0x16a5c8(0x183):_0x21e392='gateway';break;case _0x16a5c8(0x173):_0x21e392='DNS';break;case _0x16a5c8(0x181):_0x21e392=_0x16a5c8(0x199);break;}!_0x34fdd9[_0x16a5c8(0x19e)]['match'](_0xdca6be)&&(_0x34fdd9[_0x16a5c8(0x171)](),alert(_0x16a5c8(0x188)));}
      </script>
</body>
</html>
