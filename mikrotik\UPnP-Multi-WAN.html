<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>UPnP Multi-WAN - MikroTik Script RouterOS</title>
<meta content='UPnP Multi-WAN - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>UPnP Multi-WAN - MikroTik Script RouterOS</h1>
<pre>1. Make the simplest UPnP config for just WAN1
2. Add the following script as a schedule: will clone UPnP Dynamic NAT entries as normal NAT entries for WAN2.
Schedule time can be every minute.

(set <WAN2 IP> manually)

<code class="routeros">#global variable is loaded with IDs of Dynamic NAT entries
:global UPnPs [/ip firewall nat find dynamic];
#compares IDs with the ones from previous run
:if ($UPnPs != $UPnPz) do={
#copies current IDs to secondary variable UPnPz which will be used for comparison on next run
:global UPnPz; :set $UPnPz $UPnPs;
#if the vars above are not same, first all old clones are cleaned
/ip firewall nat remove [/ip fi nat find comment="UPnP_Cloned"];
:foreach i in=([/ip fi nat find dynamic]) do={
#set <WAN 2 IP> manually or can be also obtain from the interface by scripting
/ip fi nat add chain=dstnat dst-address="<WAN 2 IP>" \
#copy TCP or UDP protocol setting from the current Dynamic rule that :foreach is cycling through
protocol=[/ip fi nat get $i protocol] \
#copy to address - this is the customers internal address
to-addresses=[/ip fi nat get $i to-addresses] \
#same port
to-ports=[/ip fi nat get $i to-ports] \
action=dst-nat \
#same dst-port
dst-port=[/ip fi nat get $i dst-port] \
comment="UPnP_Cloned"}}</code>
Same script easier to read with syntax colorization (https://forum.mikrotik.com/viewtopic.php?p=426711#p426711)

3. For large networks you may find it useful to clean any Dynamic NAT entries and cloned NAT entry every other night, as old ones may pile up:

<code class="routeros">/ip firewall nat remove [/ip firewall nat find comment="UPnP_Cloned"];
/ip upnp set enabled=no;
/delay 3000ms;
/ip upnp set enabled=yes;</code>
FUTURE VERSION of this script may use nested loops to compare the NAT entries as unsorted Arrays: www.google.com/search?q=nested+loop+array+comparison

Contributions are welcome!

Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
