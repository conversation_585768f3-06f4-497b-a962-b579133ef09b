<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Anti Hack Protected BootLoader - MikroTik Script RouterOS</title>
<meta content='Anti Hack Protected BootLoader - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Anti Hack Protected BootLoader - MikroTik Script RouterOS</h1>
<pre>The way this script works is simple but smart, this script will monitor anyone who intends to activate the Mirotik Protected Bootloader, it will be detected immediately and the router will automatically shutdown immediately, when the router is turned on again, all the Protected Bootloader settings will be returned to the default setting, so any prank actions of malicious intruders before will be in vain

If after installing this script the router shuts down itself, don't panic yet, it could be that the script is working and it might mean that someone is trying to activate the bootloader on the router illegally, if it's true, this can be seen by changing the window caption or identity in Winbox to BOOTLOADER> Min: 00:00:20 - Max: 00:10:00

Why does this script have to change the identity or caption in Winbox? I am trying to think if the reset button and NetInstall don't work at least we know how much time for the "reformat hold button" it takes and how long it takes to press the reset button for it to work. Please modify it, if you want it to be good, we can also add other information such as sending it to email or to telegram 🙂

For additional information, actually this script can not only be run through a schedule, because there are still many gaps that we can use to install the script.

Several locations can be installed to run the script:

- DHCP Client
- DHCP Server
- Hotspot Profile
- PPP Profile
- Netwatch tool

<code class="routeros">#=====================================================
# ANTI HACKER PROTECTED BOOTLOADER  SECURE!
#=====================================================
# Set Local String
:local logprotectedrouterboot;
:local logreformatholdbutton;
:local logreformatholdbuttonmax;
# Set local Input
:set logprotectedrouterboot "$[/system routerboard setting get protected-routerboot]"
:set logreformatholdbutton "$[/system routerboard setting get reformat-hold-button]"
:set logreformatholdbuttonmax "$[/system routerboard setting get reformat-hold-button-max]"
# Ckeck if Bootloader change to Enable
:if ($logprotectedrouterboot ="enabled") do={
# Set info reformat hold button on Identity
/system identity set name="BOOTLOADER > Min:$logreformatholdbutton - Max:$logreformatholdbuttonmax"
# Set back BootLoader to default config
/system routerboard setting set protected-routerboot disabled
/system routerboard setting set reformat-hold-button=20s
/system routerboard setting set reformat-hold-button-max=10m
# delay 5 second
:delay 5s
# Shutdown mikrotik or use /system reboot
/system shutdown
}</code>
Credit: https://www.o-om.com/2020/10/script-mengamankan-protected-bootloader.html
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
