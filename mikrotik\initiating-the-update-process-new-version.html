<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Initiating The Update Process New Version - MikroTik Script RouterOS</title>
<meta content='Initiating The Update Process New Version - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Initiating The Update Process New Version - MikroTik Script RouterOS</h1>
<pre>How To Initiating The Update Process New Version
We can now update our router with the following command:

<code class="routeros">/system package update download  

Result:
[admin@MikroTik] > system package update download          
          channel: bugfix
  current-version: 6.42.6
   latest-version: 6.45.8
           status: Downloaded, please reboot router to upgrade it</code>
 This process is usually fast. When it’s finished, we can see all downloaded packages:

<code class="routeros">/file print where name~".npk"

Result:
[admin@MikroTik] > file print where name~".npk"
 # NAME                                  TYPE                                       SIZE CREATION-TIME       
 0 routeros-mipsbe-6.45.8.npk            package                                 11.5MiB mar/09/2020 21:48:44
 1 ntp-6.45.8-mipsbe.npk                 package                                260.1KiB mar/09/2020 21:48:28</code>
The update process will be finished after reboot. During the shutdown process, RouterOS will check for the new .npk files on the local disk and install them. To reboot the router from the command line, type the command:

<code class="routeros">/system reboot 

[admin@MikroTik] > system reboot       
Reboot, yes? [y/N]: y</code>
System will reboot shortly
The update and reboot process lasts about minute. Just be patient.

Credit: https://mivilisnet.wordpress.com/2020/03/31/updating-mikrotik-router-from-the-command-line/
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

