<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Mikrotik Terminal Command Line and Keyboard Command - Mikrotik Script RouterOS</title>
<meta content='Mikrotik Terminal Command Line and Keyboard Command - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script> 
</head> 
<body>
<div id="hidelink"></div>
<h1>Mikrotik Terminal Command Line and Keyboard Command - Mikrotik Script RouterOS</h1>
<pre>Command line or Terminal in ROS Mikrotik will actually be easier to understand if we are Linux users, because basically Mikrotik is Linux Base which is compiled for special purpose Router networks.

In monitoring the network, usually reliable administrators use the terminal more than the Winbox GUI or Webfig , perhaps in terms of security and speed.

LOGIN PARAMETERS FOR TELNET and SSH
login_name :: = user_name ['+' parameters]
parameters :: = parameter [parameters]
parameter :: = [number] 'a' .. 'z' number :: = '0' .. ' 9 '[number]

<code class="routeros">"w" = Set terminal width
"h" = Set terminal height
"c" = disable/enable console colours
"t" = Do auto detection of terminal capabilities
"e" = Enables "dumb" terminal mode</code>
Example login: 
admin + c80w

This means logged in with user "admin", "text is colorless" and 80w is the width of the window in the terminal.

<code class="routeros">List of Keys
Control-C = keyboard interrupt
Control-D = log out (if an input line is empty)
Control-K = clear from the cursor to the end of line
Control-X = toggle safe mode
Control-V = toggle hot lock mode mode
F6 = toggle cellar
F1 or ? = show context-sensitive help. If the previous character is \, then inserts literal ?.
Tab = perform line completion. When pressed a second time, show possible completions.
Delete = remove character at the cursor
Control-H or Backspace = remove character before cursor and move the cursor back one position.
Control-\ = split line at cursor. Insert newline at the cursor position. Display second of the two resulting lines.
Control-B or Left = move cursor backward one character
Control-F or Right = move cursor forward one character
Control-P or Up = go to the previous line. If this is the first line of input then recall previous input from history.
Control-N or Down = go to the next line. If this is the last line of input then recall the next input from the history
Control-A or Home = move the cursor to the beginning of the line. If the cursor is already at the beginning of the line, then go to the beginning of the first line of the current input
Control-E or End = move the cursor to the end of the line. If the cursor is already at the end of the line, then move it to the end of the last line of the current input
Control-L or F5 = reset terminal and repaint screen</code>
Credit: wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
