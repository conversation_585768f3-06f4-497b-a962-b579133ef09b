<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Block Access To Specific Websites - Mikrotik Script RouterOS</title>
<meta content='Block Access To Specific Websites - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>  
</head>   
<body>
<div id="hidelink"></div>

<h1>Block Access To Specific Websites - Mikrotik Script RouterOS</h1>
<pre>This script is useful if you want to block certain web sites but you don't want to use web proxy.
This example looks entries "rapidshare" and "youtube" in dns cache and adds IPs to address list named "restricted".

Before you begin, you must set up router to catch all dns requests:

<code class="routeros">/ip firewall nat
add action=redirect chain=dstnat comment=DNS dst-port=53 protocol=tcp to-ports=53
add action=redirect chain=dstnat dst-port=53 protocol=udp to-ports=53
and add firewall

/ip firewall filter
add chain=forward dst-address-list=restricted action=drop </code>

Now we can write a script and schedule it to run, lets say, every 30 seconds.

Script Code:

<code class="routeros">:foreach i in=[/ip dns cache find] do={
:local bNew "true";
:local cacheName [/ip dns cache all get $i name] ;
# :put $cacheName;
:if (([:find $cacheName "rapidshare"] >= 0) || ([:find $cacheName "youtube"] >= 0)) do={
:local tmpAddress [/ip dns cache get $i address] ;
# :put $tmpAddress;
# if address list is empty do not check
:if ( [/ip firewall address-list find list="restricted" ] = "") do={
:log info ("added entry: $[/ip dns cache get $i name] IP $tmpAddress");
/ip firewall address-list add address=$tmpAddress list=restricted comment=$cacheName;
} else={
:foreach j in=[/ip firewall address-list find list="restricted"] do={
:if ( [/ip firewall address-list get $j address] = $tmpAddress ) do={
:set bNew "false";
}
}
:if ( $bNew = "true" ) do={
:log info ("added entry: $[/ip dns cache get $i name] IP $tmpAddress");
/ip firewall address-list add address=$tmpAddress list=restricted comment=$cacheName;
}
}
}
} </code>
Credit: https://wiki.mikrotik.com/wiki/Manual:Scripting-examples
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
