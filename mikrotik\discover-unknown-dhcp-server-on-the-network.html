<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Discover Unknown DHCP Server On The Network (Telegram) - MikroTik Script RouterOS</title>
<meta content='Discover Unknown DHCP Server On The Network (Telegram) - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Discover Unknown DHCP Server On The Network (Telegram) - MikroTik Script RouterOS</h1>
<pre>Configuring discovered of an unknown DHCP server on the network and sending a notification to Telegram or email. The name of the DHCP interface on which the server is found, the IP and MAC addresses of the unauthorized device are sent.

Sometimes it happens when an employee connects the situation to the local network of the enterprise, a personal router, with the DHCP server enabled, which can cause problems with connecting new IP addresses of devices on the local network. Problems may not be detected immediately, but when the lease of IP addresses expires.

For quick notification of the appearance of an unauthorized DHCP server in the local network, use the Alerts setting in the DHCP server settings.

<code class="routeros"># Find DHCP Alert 
:local CurrentTime [/system clock get time];
:local MsgID [/log find where message ~"dhcp alert" time =$CurrentTime];
:local MsgText [/log get number=$MsgID message];

# Send Telegram Message
:local DeviceName [/system identity get name];
:local MessageText "\F0\9F\94\B4 <b>$DeviceName: UNKNOWN DHCP SERVER FOUND! </b> Info: $CurrentTime $MsgText";
:local SendTelegramMessage [:parse [/system script  get MyTGBotSendMessage source]];
$SendTelegramMessage MessageText=$MessageText;</code>
Credit: <a href="https://mhelp.pro/mikrotik-scripts-discover-unknown-dhcp-server-on-the-network/">https://mhelp.pro/mikrotik-scripts-discover-unknown-dhcp-server-on-the-network/</>
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</body>
</html>

