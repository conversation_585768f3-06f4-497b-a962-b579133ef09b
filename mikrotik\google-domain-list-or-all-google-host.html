<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Google Domain List Or All Google Host - MikroTik Script RouterOS</title>
<meta content='Google Domain List Or All Google Host - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>

<h1>Google Domain List Or All Google Host - MikroTik Script RouterOS</h1>
<pre>How to Add All Google Domain List Or All Google Host For Your Firewall
Below is a list of the most complete Google web search addresses, to make it easier for all of my links to sort by country name.

<code class="routeros">/ip firewall address-list
add comment="USA (.com)" address=www.google.com list=Google
add comment="Afghanistan (.com.af)" address=www.google.com.af list=Google
add comment="American Samoa (.as)" address=www.google.As list=Google
add comment="Anguilla (.off.ai)" address=www.google.off.ai list=Google
add comment="Antigua and Barbuda (.com.ag)" address=www.google.com.ag list=Google
add comment="Argentina (.com.ar)" address=www.google.com.ar list=Google
add comment="Armenia (.am)" address=www.google.am list=Google
add comment="Australia (.com.au)" address=www.google.com.au list=Google
add comment="Austria (.at)" address=www.google.at list=Google
add comment="Azerbaijan (.az)" address=www.google.az list=Google
add comment="Bahrain (.com.bh)" address=www.google.com.bh list=Google
add comment="Bangladesh (.com.bd)" address=www.google.com.bd list=Google
add comment="Belgium (.be)" address=www.google.be list=Google
add comment="Belize (.com.bz)" address=www.google.com.bz list=Google
add comment="Bolivia (.com.bo)" address=www.google.com.bo list=Google
add comment="Bosnia and Herzegovina (.ba)" address=www.google.ba list=Google
add comment="Botswana (.co.bw)" address=www.google.co.bw list=Google
add comment="Brazil (.com.br)" address=www.google.com.br list=Google
add comment="British Virgin Islands (.vg)" address=www.google.vg list=Google
add comment="Bulgaria (.bg)" address=www.google.bg list=Google
add comment="Burundi (.bi)" address=www.google.bi list=Google
add comment="Canada (.ca)" address=www.google.ca list=Google
add comment="Chile (.cl)" address=www.google.cl list=Google
add comment="China (.cn)" address=www.google.cn list=Google
add comment="Colombia (.com.co)" address=www.google.com.co list=Google
add comment="Congo, Democratic Republic of the (.cd)" address=www.google.cd list=Google
add comment="Congo, Republic of the (.cg)" address=www.google.cg list=Google
add comment="Cook Islands (.co.ck)" address=www.google.co.ck list=Google
add comment="Costa Rica (.co.cr)" address=www.google.co.cr list=Google
add comment="Croatia (.hr)" address=www.google.hr list=Google
add comment="Cuba (.com.cu)" address=www.google.com.cu list=Google
add comment="Czech Republic (.cz)" address=www.google.cz list=Google
add comment="Cote dCase Ivoire (.ci)" address=www.google.ci list=Google
add comment="Denmark (.dk)" address=www.google.dk list=Google
add comment="Djibouti (.dj)" address=www.google.dj list=Google
add comment="Dominica (.dm)" address=www.google.dm list=Google
add comment="Dominican Republic (.com.do)" address=www.google.com.do list=Google
add comment="Ecuador (.com.ec)" address=www.google.com.ec list=Google
add comment="Egypt (.com.eg)" address=www.google.com.eg list=Google
add comment="El Salvador (.com.sv)" address=www.google.com.sv list=Google
add comment="Estonia (.ee)" address=www.google.ee list=Google
add comment="Ethiopia (.com.et)" address=www.google.com.et list=Google
add comment="Fiji (.com.fj)" address=www.google.com.fj list=Google
add comment="Finland (.fi)" address=www.google.fi list=Google
add comment="France (.fr)" address=www.google.fr list=Google
add comment="Gambia (.gm)" address=www.google.gm list=Google
add comment="Germany (.de)" address=www.google.de list=Google
add comment="Gibraltar (.com.gi)" address=www.google.com.gi list=Google
add comment="Greece (.com.gr)" address=www.google.com.gr list=Google
add comment="Greenland (.gl)" address=www.google.gl list=Google
add comment="Guatemala (.com.gt)" address=www.google.com.gt list=Google
add comment="Guernsey (.gg)" address=www.google.gg list=Google
add comment="Haiti (.ht)" address=www.google.ht list=Google
add comment="Honduras (.hn)" address=www.google.hn list=Google
add comment="Hong Kong (.com.hk)" address=www.google.com.hk list=Google
add comment="Hungary (.hu)" address=www.google.hu list=Google
add comment="Iceland (.is)" address=www.google.is list=Google
add comment="India (.co.in)" address=www.google.co.in list=Google
add comment="Indonesia (.co.id)" address=www.google.co.id list=Google
add comment="Ireland (.ie)" address=www.google.ie list=Google
add comment="Isle of Man (.co.im)" address=www.google.co.im list=Google
add comment="Israel (.co.il)" address=www.google.co.il list=Google
add comment="Italy (.it)" address=www.google.it list=Google
add comment="Jamaica (.com.im)" address=www.google.com.im list=Google
add comment="Japan (.co.jp)" address=www.google.co.jp list=Google
add comment="Jersey (.co.je)" address=www.google.co.je list=Google
add comment="Jordan (.jo)" address=www.google.jo list=Google
add comment="Kazakhstan (.kz)" address=www.google.kz list=Google
add comment="Kenya (.co.ke)" address=www.google.co.ke list=Google
add comment="Kyrgyzstan (.kg)" address=www.google.kg list=Google
add comment="Latvia (.lv)" address=www.google.lv list=Google
add comment="Lesotho (.co.ls)" address=www.google.co.ls list=Google
add comment="Libya (.com.ly)" address=www.google.co.ly list=Google
add comment="Liechtenstein (.li)" address=www.google.li list=Google
add comment="Lithuania (.lt)" address=www.google.lt list=Google
add comment="Luxembourg (.lu)" address=www.google.lu list=Google
add comment="Malawi (.mw)" address=www.google.mw list=Google
add comment="Malaysia (.com.my)" address=www.google.com.my list=Google
add comment="Malta (.com.mt)" address=www.google.com.mt list=Google
add comment="Mauritius (.mu)" address=www.google.mu list=Google
add comment="Mexico (.com.mx)" address=www.google.com.mx list=Google
add comment="Micronesia (.fm)" address=www.google.fm list=Google
add comment="Mongolia (.mn)" address=www.google.mn list=Google
add comment="Montserrat (.ms)" address=www.google.ms list=Google
add comment="Morocco (.co.ma)" address=www.google.co.ma list=Google
add comment="Namibia (.com.na)" address=www.google.com.na list=Google
add comment="Nepal (.com.np)" address=www.google.com.np list=Google
add comment="Netherlands (.nl)" address=www.google.nl list=Google
add comment="New Zealand (.co.nz)" address=www.google.co.nz list=Google
add comment="Nicaragua (.com.ni)" address=www.google.com.ni list=Google
add comment="Norfolk Island (.com.nf)" address=www.google.com.nf list=Google
add comment="Norway (.no)" address=www.google.no list=Google
add comment="Oman (.com.om)" address=www.google.com.om list=Google
add comment="Pakistan (.com.pk)" address=www.google.com.pk list=Google
add comment="Panama (.com.pa)" address=www.google.com.pa list=Google
add comment="Paraguay (.com.py)" address=www.google.com.py list=Google
add comment="Peru (.com.pe)" address=www.google.com.pe list=Google
add comment="Philippines (.com.ph)" address=www.google.com.ph list=Google
add comment="Pitcairn (.pn)" address=www.google.com.pn list=Google
add comment="Poland (.pl)" address=www.google.pl list=Google
add comment="Portugal (.pt)" address=www.google.pt list=Google
add comment="Puerto Rico (.com.pr)" address=www.google.com.pr list=Google
add comment="Qatar (.com.qa)" address=www.google.com.qa list=Google
add comment="Romania (.ro)" address=www.google.ro list=Google
add comment="Russia (.ru)" address=www.google.ru list=Google
add comment="Rwanda (.rw)" address=www.google.rw list=Google
add comment="Saint Helena (.sh)" address=www.google.sh list=Google
add comment="San Marino (.sm)" address=www.google.sm list=Google
add comment="Saudi Arabia (.com.sa)" address=www.google.com.sa list=Google
add comment="Senegal (.sn)" address=www.google.sn list=Google
add comment="Seychelles (.sc)" address=www.google.sc list=Google
add comment="Singapore (.com.sg)" address=www.google.com.sg list=Google
add comment="Slovakia (.sk)" address=www.google.sk list=Google
add comment="Slovenia (.si)" address=www.google.si list=Google
add comment="South Africa (.co.za)" address=www.google.co.za list=Google
add comment="South Korea (.co.kr)" address=www.google.co.kr list=Google
add comment="Spain (.es)" address=www.google.es list=Google
add comment="Sri Lanka (.lk)" address=www.google.lk list=Google
add comment="St.Vincent and the Grenadines (.com.vc)" address=www.google.com.vc list=Google
add comment="Sweden (.se)" address=www.google.se list=Google
add comment="Switzerland (.ch)" address=www.google.ch list=Google
add comment="Taiwan (.com.tw)" address=www.google.com.tw list=Google
add comment="Tajikistan (.com.tj)" address=www.google.com.tj list=Google
add comment="Thailand (.co.th)" address=www.google.co.th list=Google
add comment="The Bahamas (.bs)" address=www.google.bs list=Google
add comment="Tonga (.to)" address=www.google.to list=Google
add comment="Trinidad and Tobago (.tt)" address=www.google.tt list=Google
add comment="Turkey (.com.tr)" address=www.google.com.tr list=Google
add comment="Turkmenistan (.tm)" address=www.google.tm list=Google
add comment="U.S. Virgin Islands (.co.vi)" address=www.google.co.vi list=Google
add comment="Uganda (.co.ug)" address=www.google.co.ug list=Google
add comment="Ukraine (.com.ua)" address=www.google.com.ua list=Google
add comment="United Arab Emirates (.ae)" address=www.google.ae list=Google
add comment="United Kingdom (.co.uk)" address=www.google.co.uk list=Google
add comment="Uruguay (.com.uy)" address=www.google.com.uy list=Google
add comment="Uzbekistan (.co.uz)" address=www.google.co.uz list=Google
add comment="Venezuela (.co.ve)" address=www.google.co.ve list=Google
add comment="Vietnam (.com.vn)" address=www.google.com.vn list=Google
add comment="Zambia (.co.zm)" address=www.google.co.zm list=Google</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>