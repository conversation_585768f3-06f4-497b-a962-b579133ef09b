<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Logging Average CCQ and Wireless Clients Stats - MikroTik Script RouterOS</title>
<meta content='Logging Average CCQ and Wireless Clients Stats - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Logging Average CCQ and Wireless Clients Stats - MikroTik Script RouterOS</h1>
<pre>This simple little script logs the specified wireless interface's overall ccq, noise floor, and frequencies. Very useful if you set this up on a scheduler, say each minute and syslog it to a remote server for parsing/graphing etc. Also handy for remembering what your frequency was set at previously


<code class="routeros">/int wir monitor [find name=wlan1] once do {
:env; :log info ([/system identity get name] . "," . $clients . "," . $overall-tx-ccq . "," . $noise-floor . ","  
. $frequency .             "," . $current-tx-powers);
}</code>
Sample output

<code class="routeros">Feb  8 11:53:40 ************* script,info AP.Antar,32,62,-92,2412,1Mbps:18,2Mbps:18,5.5Mbps:18,11Mbps:18</code>

This second script parses mac, last ip address, tx rates, uptime, last activity, snr etc... of each connected wireless client. Just be sure to change the interface= to your interface name. I usually set this one up on a 5 minute schedule. Good for matching IP/Mac Pairs

<code class="routeros">:foreach i in=[ /int wir reg find interface=wlan1] do={
:log info ([/system identity get name] . "," . [get $i  mac-address ]  . "," . [get $i last-ip] . "," . [get $i rx-rate] . "," .  
[get $i tx-rate] . "," . [get $i uptime] . "," . [get $i last-activity] . "," . [get $i signal-strength] . "," . [get $i signal- 
to-noise] . "," . [get $i tx-ccq] . "," . [get $i bytes] . "," . [get $i packets])
}</code>
Sample Output

<code class="routeros">Feb  8 11:54:09 ************** script,info AP.Grizz,00:02:6F:34:2E:1C,**************,11Mbps,11Mbps,3w4d15:50:58,00:00:00.010,-   
56dBm@11Mbps,36,100,3485064358,828420047,3532238,2666063</code>

Check all the client ( all wireless interface) with this

<code class="routeros">:foreach i in=[ /int wir reg find ap=no] do={
:log info ("Mikrotik " . [/system identity get name] . " AP: " . [int wir reg get $i interface] . "  MAC: " . [int wir reg get 
$i mac-address ] . " Last IP: " . [int wir reg get $i last-ip] . " RX: " .   [int wir reg get $i rx-rate] . " TX: " . [int wir  
reg get $i tx-rate] . " Uptime: " . [int wir reg get $i uptime] . " LastACT: " . [int wir reg get $i last-activity] . "  
SignalStrength: " . [int wir reg get $i signal-strength] . " SNR: " . [int wir reg get $i signal-to-noise] . " TX/RX-CCQ: " .  
[int wir reg get $i tx-ccq] . " / " . [int wir reg get $i rx-ccq] . " PThroughput: " . [int wir reg get $i  p-throughput] )
}
 /system scheduler
 add disabled=no interval=6h name=log_reg_table on-event=log_client start-time=startup
 /system script
 add name=log_client policy=ftp,reboot,read,write,policy,test,winbox,password,sniff source=":foreach i in=[ /int wir reg find ap=no] do={\r\
    :log info (\"Mikrotik \" . [/system identity get name] . \" AP: \" . [int wir reg get \$i interface] . \"  MAC: \" . [int wir reg get \$i mac-address ] . \" Last IP:\
     \" . [int wir reg get \$i last-ip] . \" RX: \" . [int wir reg get \$i rx-rate] . \" TX: \" . [int wir reg get \$i tx-rate] . \" Uptime: \" . [int wir reg get \$i upti\
    me] . \" LastACT: \" . [int wir reg get \$i last-activity] . \" SignalStrength: \" . [int wir reg get \$i signal-strength] . \" SNR: \" . [int wir reg get \$i signal-t\
    o-noise] . \" TX/RX-CCQ: \" . [int wir reg get \$i tx-ccq] . \" / \" . [int wir reg get \$i rx-ccq] . \" PThroughput: \" . [int wir reg get \$i  p-throughput] )\r\
    }"</code>
Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

