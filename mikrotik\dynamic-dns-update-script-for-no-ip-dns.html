<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Dynamic DNS Update Script for No-IP DNS - MikroTik Script RouterOS</title>
<meta content='Dynamic DNS Update Script for No-IP DNS - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>

<h1>Dynamic DNS Update Script for No-IP DNS - MikroTik Script RouterOS</h1>
<pre>This script was designed to utilize the No-IP Update API which offers direct access to No-IP's DNS update system.

This script is based on the Dynamic DNS Update Script for DNSoMatic.com.

Tested to work on RouterOS 5.13.

1. Create a new script named no-ip_ddns_update

The following permissions are required for this script to run:

- write
- test
- read

2. Paste the source code that appears below. Edit the user, password, hostname and interface info to match your setup.

<code class="routeros"># No-IP automatic Dynamic DNS update
#--------------- Change Values in this section to match your setup ------------------
# No-IP User account info
:local noipuser "your_no-ip_user"
:local noippass "your_no-ip_pass"

# Set the hostname or label of network to be updated.
# Hostnames with spaces are unsupported. Replace the value in the quotations below with your host names.
# To specify multiple hosts, separate them with commas.
:local noiphost "hostname.no-ip.net"

# Change to the name of interface that gets the dynamic IP address
:local inetinterface "your_external_interface"
#------------------------------------------------------------------------------------
# No more changes need
:global previousIP
:if ([/interface get $inetinterface value-name=running]) do={
# Get the current IP on the interface
   :local currentIP [/ip address get [find interface="$inetinterface" disabled=no] address]

# Strip the net mask off the IP address
   :for i from=( [:len $currentIP] - 1) to=0 do={
       :if ( [:pick $currentIP $i] = "/") do={ 
           :set currentIP [:pick $currentIP 0 $i]
       } 
   }
   :if ($currentIP != $previousIP) do={
       :log info "No-IP: Current IP $currentIP is not equal to previous IP, update needed"
       :set previousIP $currentIP
# The update URL. Note the "\3F" is hex for question mark (?). Required since ? is a special character in commands.
       :local url "http://dynupdate.no-ip.com/nic/update\3Fmyip=$currentIP"
       :local noiphostarray
       :set noiphostarray [:toarray $noiphost]
       :foreach host in=$noiphostarray do={
           :log info "No-IP: Sending update for $host"
           /tool fetch url=($url . "&hostname=$host") user=$noipuser password=$noippass mode=http dst-path=("no-ip_ddns_update-" . $host . ".txt")
           :log info "No-IP: Host $host updated on No-IP with IP $currentIP"
       }
   }  else={
       :log info "No-IP: Previous IP $previousIP is equal to current IP, no update needed"
   }
} else={
   :log info "No-IP: $inetinterface is not currently running, so therefore will not update."
}</code>
3. Create a new scheduler entry to run this script every 5 mins.

<code class="routeros">/system scheduler add comment="Update No-IP DDNS" disabled=no interval=5m \
name=no-ip_ddns_update on-event=no-ip_ddns_update policy=read,write,test</code>
--riverron 03:18, 18 March 2012 (UTC)

Alternative script.
Alternative script uses DNS Resolve and is based on an documentation http://wiki.mikrotik.com/wiki/Manual:Scripting-examples and documentation No-IP DNS Update API http://www.no-ip.com/integrate/request

Can be modified for use with other Dynamic DNS services supported ReST API.

<code class="routeros">##############Script Settings##################
:local NOIPUser "no-ip.com LOGIN"
:local NOIPPass "no-ip.com PASSWORD"
:local WANInter "MikroTik Router WAN Interface Name"
###############################################
:local NOIPDomain "$NOIPUser.no-ip.org"
:local IpCurrent [/ip address get [find interface=$WANInter] address];
:for i from=( [:len $IpCurrent] - 1) to=0 do={ 
  :if ( [:pick $IpCurrent $i] = "/") do={ 
    :local NewIP [:pick $IpCurrent 0 $i];
    :if ([:resolve $NOIPDomain] != $NewIP) do={
      /tool fetch mode=http user=$NOIPUser password=$NOIPPass url="http://dynupdate.no-ip.com/nic/update\3Fhostname=$NOIPDomain&myip=$NewIP" keep-result=no
      :log info "NO-IP Update: $NOIPDomain - $NewIP"
     }
   } 
}</code>
Note:

:local NOIPDomain "$NOIPUser.no-ip.org" - True if the username is the third level domain and is registered in the domain *. no-ip.org(example: username=testuser1 and domain=testuser1.no-ip.org). In another case, the string to be changed.

The symbol "\3F" is used to replace the shielded symbol "?" in the URL.

Tested to work on RouterOS 5.21.

Sample configuration:

<code class="routeros">##############Script Settings##################
:local NOIPUser "testuser1"
:local NOIPPass "testpassword"
:local WANInter "ether1"
###############################################</code>
If you use another WAN-interface, use it(example: pppoe-out1, pptp-out1, l2tp-out1).

--k.keker 15:18, 14 November 2012 (UTC)

Credit: https://wiki.mikrotik.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>