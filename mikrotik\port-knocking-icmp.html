<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>How Port Knocking Maker - Mikrotik Script RouterOS</title>
<meta content='How Port Knocking Maker - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head> 
<body>
<div id="hidelink"></div>

<h1>How Port Knocking Maker - Mikrotik Script RouterOS</h1>
<pre>In computer networking, port knocking is a method of externally opening ports on a firewall by generating a connection attempt on a set of prespecified closed ports. Once a correct sequence of connection attempts is received, the firewall rules are dynamically modified to allow the host which sent the connection attempts to connect over specific port(s). A variant called single packet authorization (SPA) exists, where only a single "knock" is needed, consisting of an encrypted packet.

The primary purpose of port knocking is to prevent an attacker from scanning a system for potentially exploitable services by doing a port scan, because unless the attacker sends the correct knock sequence, the protected ports will appear closed

<code class="routeros">###########################################################
# Mikrotik Port Knocking Generator with Icmp + Packet Size
# Date/Time: 2/14/2021, 12:14:10 PM
#  
###########################################################

/ip firewall filter
add action=add-src-to-address-list address-list="port-knocking-first" address-list-timeout="00:00:00" chain=input packet-size="100" protocol=icmp comment="Port Knocking By BNT
add action=add-src-to-address-list address-list="port-knocking-second" address-list-timeout="00:00:00" chain=input packet-size="200" protocol=icmp src-address-list="port-knocking-first"
add action=accept chain=input dst-port="8291,21,22,23,80,443" protocol=tcp src-address-list="port-knocking-second"
add action=drop chain=input dst-port="8291,21,22,23,80,443" protocol=tcp src-address-list="!port-knocking-second"</code>
<b>Copy-Paste Firewall Script into the Terminal!</b>

Unique Packet Size For Key Knocking: 72 and 172

Example Manually Open Key Ping in CMD Windows:
First Key Knock -> ping -l 72 (IP Adrress)
Second Key Knock -> ping -l 172 (IP Adrress)

Example Manually Open Key Ping in Terminal Linux or MacOS:
First Key Knock -> ping -s 72 (IP Adrress)
Second Key Knock -> ping -s 172 (IP Adrress)

Or you can use <a target="_blank" href="https://mikrotiktool.github.io/port-knocking-icmp.html" >Port Knocking Maker or Port Knock Generator</a>
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
