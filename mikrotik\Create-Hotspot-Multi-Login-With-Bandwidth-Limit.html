<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Create Hotspot Multi Login With Bandwidth Limit - MikroTik Script RouterOS</title>
<meta content='Create Hotspot Multi Login With Bandwidth Limit - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Create Hotspot Multi Login With Bandwidth Limit - MikroTik Script RouterOS</h1>
<pre>
Limiting Hotspot Profiles using Multi Login (1 profile raped together) is sometimes confusing, because the shared bandwidth usually does not use the Total Bandwidth Limit but instead is given bandwidth with the same speed to each IP. Actually this is not a problem if we use Mangle and Queue Tree, but it will be a different story if we use Simple Queue.

Actually this method is quite interesting for hotspot players who want to provide additional free access for users at their cafe or internet cafe and of course do not interfere with paid hotspot users, because they are on the limit as they are :)

Create a new IP Address = *********/24 (free only)
IP Pool = *********-*********54
Interface hotspot [wlan-hotspot] = replace with the name of the hotspot interface if it's been a long time, it doesn't matter because that's our real goal, khan for one interface can be set to multi IP :)

<code class="routeros">/ip address
add address=*********/24 comment="Login Multi User" interface=[wlan-hotspot] network=*********
/ip pool
add name=hs_multi_user ranges=*********-*********54
/ip firewall nat
add action=masquerade chain=srcnat comment="masquerade hotspot network multi login" src-address=*********/24
/ip hotspot user profile
add address-pool=hs_multi_user name=multi-user shared-users=10
/ip hotspot user
add name=muser password=12345 profile=multi-user
/queue simple
add max-limit=256k/512k name="Multi User" target=*********/24</code>
Credit: https://www.o-om.com/2018/11/cara-melimit-bandwidth-multi-login.html
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
