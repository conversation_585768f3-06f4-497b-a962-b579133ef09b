<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Password and username ONT Modem Telkom Indihome - MikroTik Script RouterOS</title>
<meta content='Password and username ONT Modem Telkom Indihome - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Password and username ONT Modem Telkom Indihome - MikroTik Script RouterOS</h1>
<pre>Default Password and Username For Modem ONT Telkom Indihome

<code class="routeros">username : admin
password : %0|F?H@f!berhO3e

username : telecomadmin
password : zep2kjzol

username : admin
password : Telkomdso123

username : support
password : theworldinyourhand

username : Support
password : theworldinyourhand

Username : telecomadmin
Password : zep2kjzol

Username : Support
Password : zep2kjzol

username : admin
password : admin

username : user
password : user

username : support
password : support

username : admin
password : Mn@lh4!nk9#m

username : admin
password : Dj9@t!n03g4r6#f

username : admin
password : Zs3!m@rh4n#z

username : admin
password : Atr3gy!0n@l$to#e

username : admin
password : Jq@yh4p0ur4#j

username : admin
password : Pq@54r!e8ow&q#u

username : admin
password : Siy!dho@r7o#s

username : admin
password : Yu9j#4qa!rth

username : admin
password : telkomjatineg4r4</code>
Credit: unknowm
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>