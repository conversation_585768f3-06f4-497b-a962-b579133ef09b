<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Solution For Hotspot Login Page with LB PCC - Mikrotik Script RouterOS</title>
<meta content='Solution For Hotspot Login Page with LB PCC - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head> 
<body>
<div id="hidelink"></div>
<h1>Solution For Hotspot Login Page with LB PCC - Mikrotik Script RouterOS</h1>
<pre>Having trouble with the Hotspot Login Page in PCC Load Balancing which often fails to appear or doesn't automatically redirect?

Just add this in NAT for <span style="color:red">hotspot=auth</span>

Still failing too? we play a little with hotspot = auth in Load balancing PCC in mangle, adjust it to each condition, for example like this, just add hotspot=auth

<code class="routeros">/ip firewall nat add action=accept chain=pre-hotspot disabled=no dst-address-type=!local hotspot=auth</code>
Still failed too? delete all the above methods and try to bypass all local traffic, make sure it is located at the top of the mangle (LOCAL-IP respectively, enter in the add-list)

<code class="routeros">add action=mark-connection chain=prerouting dst-address-list=!IP-LAN dst-address-type=!local hotspot=auth new-connection-mark=E1-WAN_connmark passthrough=yes per-connection-classifier=both-addresses-and-ports:2/0 src-address-list=IP-LAN
add action=mark-connection chain=prerouting dst-address-list=!IP-LAN dst-address-type=!local hotspot=auth new-connection-mark=E2-WAN_connmark passthrough=yes per-connection-classifier=both-addresses-and-ports:2/1 src-address-list=IP-LAN</code>
Credit: Unknown
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%********&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
