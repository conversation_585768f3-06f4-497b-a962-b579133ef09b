<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Block Facebook with "Layer 7" or "Content" or "TLS" - MikroTik Script RouterOS</title>
<meta content='Block Facebook with "Layer 7" or "Content" or "TLS" - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>

<h1>Block Facebook with "Layer 7" or "Content" or "TLS" - MikroTik Script RouterOS</h1>
<pre>How to Block Facebook with "Layer 7" or "Content" or "TLS"

Block Facebook With "Layer-7"

<code class="routeros">/ip firewall layer7-protocol
add name=Facebook regexp="^.+(facebook.com|fbcdn.net).*\$"
/ip firewall filter
add action=drop chain=forward layer7-protocol=Facebook</code>

Block Facebook With "Content"

<code class="routeros">/ip firewall filter
add action=drop chain=forward content="facebook.com" 
add action=drop chain=forward content="fbcdn.net"
add action=drop chain=forward content=".facebook."
add action=drop chain=forward content=".fbcdn."</code>

Block Facebook With "TLS"

<code class="routeros">/ip firewall filter
add action=drop chain=forward protocol=tcp tls-host="facebook.com"
add action=drop chain=forward protocol=tcp tls-host="fbcdn.net"
add action=drop chain=forward protocol=tcp tls-host="*.facebook.*"
add action=drop chain=forward protocol=tcp tls-host="*.fbcdn.*"</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
