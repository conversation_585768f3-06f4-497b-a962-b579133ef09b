<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>DNSCRYPT with OpenDNS port 443 or 5353 - MikroTik Script RouterOS</title>
<meta content='DNSCRYPT with OpenDNS port 443 or 5353 - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>

<h1>DNSCRYPT with OpenDNS port 443 or 5353 - MikroTik Script RouterOS</h1>
<pre>Why DNSCrypt is so significant?
In the same way the SSL turns HTTP web traffic into HTTPS encrypted Web traffic, DNSCrypt turns regular DNS traffic into encrypted DNS traffic that is secure from eavesdropping and man-in-the-middle attacks. It doesn’t require any changes to domain names or how they work, it simply provides a method for securely encrypting communication between our customers and our DNS servers in our data centers. We know that claims alone don’t work in the security world, however, so we’ve opened up the source to our DNSCrypt code base and it’s available on GitHub.

<code class="routeros">/ip firewall nat
add action=dst-nat chain=dstnat comment="opendns 222" dst-port=53 protocol=tcp to-addresses=************** to-ports=443
add action=dst-nat chain=dstnat comment="opendns 222" dst-port=53 protocol=udp to-addresses=************** to-ports=443
add action=dst-nat chain=dstnat comment="opendns 220" dst-port=53 protocol=tcp to-addresses=************** to-ports=443
add action=dst-nat chain=dstnat comment="opendns 220" dst-port=53 protocol=udp to-addresses=************** to-ports=443</code>

Or

<code class="routeros">/ip firewall nat
add action=dst-nat chain=dstnat comment="opendns 222" dst-port=53 protocol=tcp to-addresses=************** to-ports=5353
add action=dst-nat chain=dstnat comment="opendns 222" dst-port=53 protocol=udp to-addresses=************** to-ports=5353
add action=dst-nat chain=dstnat comment="opendns 220" dst-port=53 protocol=tcp to-addresses=************** to-ports=5353
add action=dst-nat chain=dstnat comment="opendns 220" dst-port=53 protocol=udp to-addresses=************** to-ports=5353</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

