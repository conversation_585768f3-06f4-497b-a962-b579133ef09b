<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Add Static DHCP Leases to ARP List - MikroTik Script RouterOS</title>
<meta content='Add Static DHCP Leases to ARP List - MikroTik RouterOS Script DataBase' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>
</head>  
<body>
<div id="hidelink"></div>
<h1>Add Static DHCP Leases to ARP List - MikroTik Script RouterOS</h1>
<pre>This script will update the ARP List with all static, enabled leases. It makes the changes in place (i.e. it doesn't delete all the ARP entries first) to minimize disruptions. Limitation of the script is that the interface for the ARP entry needs to be hardcoded for now.

<code class="routeros">:local wanInterfaceName "ether1_wan";
# Remove ARP entries that do not have static DHCP leases or are disabled
:foreach arpId in=[/ip arp find] do={
#Don't remove the dynamic entry on the WAN side
  :if ([/ip arp get $arpId interface] != $wanInterfaceName) do={
#If there is no matching entry in the lease table remove it
:local mac [/ip arp get $arpId mac-address];
:local leaseId [/ip dhcp-server lease find where mac-address=$mac];
:if ($leaseId="") do={
/ip arp remove $arpId;
:log info ("Removing old ARP entry");
} else={
:if ([/ip dhcp-server lease get $leaseId disabled]) do={
/ip arp remove $arpId;
:log info ("Removing disabled ARP entry");
}}}}
:foreach leaseId in=[/ip dhcp-server lease find where !dynamic] do={
:local mac  [/ip dhcp-server lease get $leaseId mac-address];
:local arpId [/ip arp find where mac-address=$mac];
:if ($arpId="" && ![/ip dhcp-server lease get $leaseId disabled]) do={
:local ip [/ip dhcp-server lease get $leaseId address];
:local comment  [/ip dhcp-server lease get $leaseId comment];
#interface should not be hard coded but couldn't figure out what to do
:local interface lan_wlan_bridge;
/ip arp add address= $ip mac-address= $mac comment= $comment interface= $interface;
:log info ("Adding new ARP entry");
}
}</code>
Credit: wiki.mikrotik.com
</pre>

<br>
<div id="hidelink"></div>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>

