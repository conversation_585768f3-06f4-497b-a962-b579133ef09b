<!DOCTYPE html>
<html lang="en">
<head>
<meta charset='utf-8'/>
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Protect Winbox From Exploit - Mikrotik Script RouterOS</title>
<meta content='Protect Router Winbox From Exploit - Mikrotik Script' name='description'/>
<meta content='mikrotik script, routeros script, script database, script, mikrotik, routeros, router, hotspot, rtrwnet' name='keywords'/>
<meta content='index, follow, noodp' name='robots'/>
<link rel="shortcut icon" href="https://mikrotiktool.github.io/favicon.ico" type="image/x-icon"> 
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/stytes.css">
<link rel="stylesheet" href="//mikrotiktool.github.io/mikrotik/highlightjs/styles/github.css">
<script src="//mikrotiktool.github.io/mikrotik/highlightjs/highlight.pack.js"></script>
<script>hljs.initHighlightingOnLoad();</script>  
</head> 
<body>
<div id="hidelink"></div>
<h1>Protect Winbox From Exploit (VULNERABILITY) - Mikrotik Script RouterOS</h1>
<pre>This post summarises the Winbox server vulnerability in RouterOS, discovered and fixed in RouterOS on April 23, 2018. 
Note that although Winbox was used as point of attack, the vulnerabilitty was in RouterOS. This issue was later assigned a universal identifier CVE-2018-14847. 

How it works: The vulnerability allowed a special tool to connect to the Winbox port, and request the system user database file. 

Versions affected:

Affected all bugfix releases from 6.30.1 to 6.40.7, fixed in 6.40.8 on 2018-Apr-23
Affected all current releases from 6.29 to 6.42, fixed in 6.42.1 on 2018-Apr-23
Affected all RC releases from 6.29rc1 to 6.43rc3, fixed in 6.43rc4 on on 2018-Apr-23

Am I affected? Currently there is no sure way to see if you were affected. If your Winbox port is open to untrusted networks, 
assume that you are affected and upgrade + change password + add firewall according to our guidelines. Make sure that you change password after an upgrade. 
The log may show unsuccessful login attempt, followed by a succefful login attempt from unknown IP addresses. 

How to Protect?

<code class="routeros">/ip firewall filter
add action=reject chain=input comment="PROTECT ROUTER" in-interface=ether1 content=user.dat reject-with=icmp-network-unreachable
add action=drop chain=input in-interface=ether1 content="user.dat"</code>
Credit: www.o-om.com
</pre>
<script>if (window.top != window.self){document.getElementById("hidelink").innerHTML = "";} else {document.getElementById("hidelink").innerHTML = "<div class='logo'><span class='logo-left'><a href='mikrotiktool.github.io/mikrotik'><span style='color:#8E2DE2 !important'>mikrotiktool</span>.Github.io</a></span><span class='logo-right'><img style='padding-top:12px' src='https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fmikrotiktool.github.io%2F&count_bg=%23C83D3D&title_bg=%23555555&icon=&icon_color=%23E7E7E7&title=pageview&edge_flat=false' alt='Hits'/></span><div style='clear: both;'></div></div><div class='header'><span class='header-left'><span style='float:left; margin-right:6px; margin-top:-2px'><a href='mikrotiktool.github.io/mikrotik/'><img src='mikrotiktool.github.io/mikrotik/img/logo.png' alt='Mikrotik Script RouterOS'></a></span><span style='color:#fff;font-weight:bold;font-size:20px;margin-top:-2px;'>MIKROTIK SCRIPT ROUTEROS DATABASE</span></span><div style='clear: both;'></div></div>"}</script>
<br>
</body>
</html>
